var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
import { B as BaseStyle, y as script$s, cA as script$t, m as createBaseVNode, o as openBlock, f as createElementBlock, G as mergeProps, Z as toDisplayString, U as Ripple, r as resolveDirective, i as withDirectives, J as createBlock, K as resolveDynamicComponent, c5 as script$u, aD as resolveComponent, V as normalizeClass, aF as createSlots, P as withCtx, bG as script$v, bD as script$w, H as Fragment, I as renderList, aG as createTextVNode, bx as setAttribute, am as UniqueComponentId, bv as normalizeProps, M as renderSlot, L as createCommentVNode, T as equals, br as script$x, cg as script$y, cB as getFirstFocusableElement, ap as OverlayEventBus, E as getVNodeProp, ao as resolveFieldData, cC as invokeElementMethod, Q as getAttribute, cD as getNextElementSibling, C as getOuterWidth, cE as getPreviousElementSibling, l as script$z, aA as script$A, Y as script$B, bu as script$D, al as isNotEmpty, b3 as withModifiers, D as getOuterHeight, cF as _default, an as ZIndex, S as focus, ar as addStyle, at as absolutePosition, au as ConnectedOverlayScrollHandler, av as isTouchDevice, cG as FilterOperator, az as script$E, cH as script$F, cI as FocusTrap, k as createVNode, aE as Transition, c3 as withKeys, cJ as getIndex, aW as script$G, cK as isClickable, cL as clearSelection, cM as localeComparator, cN as sort, cO as FilterService, cu as FilterMatchMode, R as findSingle, c9 as findIndexInList, ca as find, cP as exportCSV, W as getOffset, cQ as getHiddenElementOuterWidth, cR as getHiddenElementOuterHeight, cS as reorderArray, cT as getWindowScrollTop, cU as removeClass, cV as addClass, aq as isEmpty, ay as script$H, aB as script$I } from "./index-QvfM__ze.js";
import { s as script$C } from "./index-Q1cQr26V.js";
var ColumnStyle = BaseStyle.extend({
  name: "column"
});
var script$1$3 = {
  name: "BaseColumn",
  "extends": script$s,
  props: {
    columnKey: {
      type: null,
      "default": null
    },
    field: {
      type: [String, Function],
      "default": null
    },
    sortField: {
      type: [String, Function],
      "default": null
    },
    filterField: {
      type: [String, Function],
      "default": null
    },
    dataType: {
      type: String,
      "default": "text"
    },
    sortable: {
      type: Boolean,
      "default": false
    },
    header: {
      type: null,
      "default": null
    },
    footer: {
      type: null,
      "default": null
    },
    style: {
      type: null,
      "default": null
    },
    "class": {
      type: String,
      "default": null
    },
    headerStyle: {
      type: null,
      "default": null
    },
    headerClass: {
      type: String,
      "default": null
    },
    bodyStyle: {
      type: null,
      "default": null
    },
    bodyClass: {
      type: String,
      "default": null
    },
    footerStyle: {
      type: null,
      "default": null
    },
    footerClass: {
      type: String,
      "default": null
    },
    showFilterMenu: {
      type: Boolean,
      "default": true
    },
    showFilterOperator: {
      type: Boolean,
      "default": true
    },
    showClearButton: {
      type: Boolean,
      "default": true
    },
    showApplyButton: {
      type: Boolean,
      "default": true
    },
    showFilterMatchModes: {
      type: Boolean,
      "default": true
    },
    showAddButton: {
      type: Boolean,
      "default": true
    },
    filterMatchModeOptions: {
      type: Array,
      "default": null
    },
    maxConstraints: {
      type: Number,
      "default": 2
    },
    excludeGlobalFilter: {
      type: Boolean,
      "default": false
    },
    filterHeaderClass: {
      type: String,
      "default": null
    },
    filterHeaderStyle: {
      type: null,
      "default": null
    },
    filterMenuClass: {
      type: String,
      "default": null
    },
    filterMenuStyle: {
      type: null,
      "default": null
    },
    selectionMode: {
      type: String,
      "default": null
    },
    expander: {
      type: Boolean,
      "default": false
    },
    colspan: {
      type: Number,
      "default": null
    },
    rowspan: {
      type: Number,
      "default": null
    },
    rowReorder: {
      type: Boolean,
      "default": false
    },
    rowReorderIcon: {
      type: String,
      "default": void 0
    },
    reorderableColumn: {
      type: Boolean,
      "default": true
    },
    rowEditor: {
      type: Boolean,
      "default": false
    },
    frozen: {
      type: Boolean,
      "default": false
    },
    alignFrozen: {
      type: String,
      "default": "left"
    },
    exportable: {
      type: Boolean,
      "default": true
    },
    exportHeader: {
      type: String,
      "default": null
    },
    exportFooter: {
      type: String,
      "default": null
    },
    filterMatchMode: {
      type: String,
      "default": null
    },
    hidden: {
      type: Boolean,
      "default": false
    }
  },
  style: ColumnStyle,
  provide: /* @__PURE__ */ __name(function provide() {
    return {
      $pcColumn: this,
      $parentInstance: this
    };
  }, "provide")
};
var script$r = {
  name: "Column",
  "extends": script$1$3,
  inheritAttrs: false,
  inject: ["$columns"],
  mounted: /* @__PURE__ */ __name(function mounted() {
    var _this$$columns;
    (_this$$columns = this.$columns) === null || _this$$columns === void 0 || _this$$columns.add(this.$);
  }, "mounted"),
  unmounted: /* @__PURE__ */ __name(function unmounted() {
    var _this$$columns2;
    (_this$$columns2 = this.$columns) === null || _this$$columns2 === void 0 || _this$$columns2["delete"](this.$);
  }, "unmounted"),
  render: /* @__PURE__ */ __name(function render() {
    return null;
  }, "render")
};
var script$q = {
  name: "ArrowDownIcon",
  "extends": script$t
};
var _hoisted_1$i = /* @__PURE__ */ createBaseVNode("path", {
  "fill-rule": "evenodd",
  "clip-rule": "evenodd",
  d: "M6.99994 14C6.91097 14.0004 6.82281 13.983 6.74064 13.9489C6.65843 13.9148 6.58387 13.8646 6.52133 13.8013L1.10198 8.38193C0.982318 8.25351 0.917175 8.08367 0.920272 7.90817C0.923368 7.73267 0.994462 7.56523 1.11858 7.44111C1.24269 7.317 1.41014 7.2459 1.58563 7.2428C1.76113 7.23971 1.93098 7.30485 2.0594 7.42451L6.32263 11.6877V0.677419C6.32263 0.497756 6.394 0.325452 6.52104 0.198411C6.64808 0.0713706 6.82039 0 7.00005 0C7.17971 0 7.35202 0.0713706 7.47906 0.198411C7.6061 0.325452 7.67747 0.497756 7.67747 0.677419V11.6877L11.9407 7.42451C12.0691 7.30485 12.2389 7.23971 12.4144 7.2428C12.5899 7.2459 12.7574 7.317 12.8815 7.44111C13.0056 7.56523 13.0767 7.73267 13.0798 7.90817C13.0829 8.08367 13.0178 8.25351 12.8981 8.38193L7.47875 13.8013C7.41621 13.8646 7.34164 13.9148 7.25944 13.9489C7.17727 13.983 7.08912 14.0004 7.00015 14C7.00012 14 7.00009 14 7.00005 14C7.00001 14 6.99998 14 6.99994 14Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$f = [_hoisted_1$i];
function render$p(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_2$f, 16);
}
__name(render$p, "render$p");
script$q.render = render$p;
var script$p = {
  name: "ArrowUpIcon",
  "extends": script$t
};
var _hoisted_1$h = /* @__PURE__ */ createBaseVNode("path", {
  "fill-rule": "evenodd",
  "clip-rule": "evenodd",
  d: "M6.51551 13.799C6.64205 13.9255 6.813 13.9977 6.99193 14C7.17087 13.9977 7.34182 13.9255 7.46835 13.799C7.59489 13.6725 7.66701 13.5015 7.66935 13.3226V2.31233L11.9326 6.57554C11.9951 6.63887 12.0697 6.68907 12.1519 6.72319C12.2341 6.75731 12.3223 6.77467 12.4113 6.77425C12.5003 6.77467 12.5885 6.75731 12.6707 6.72319C12.7529 6.68907 12.8274 6.63887 12.89 6.57554C13.0168 6.44853 13.0881 6.27635 13.0881 6.09683C13.0881 5.91732 13.0168 5.74514 12.89 5.61812L7.48846 0.216594C7.48274 0.210436 7.4769 0.204374 7.47094 0.198411C7.3439 0.0713707 7.1716 0 6.99193 0C6.81227 0 6.63997 0.0713707 6.51293 0.198411C6.50704 0.204296 6.50128 0.210278 6.49563 0.216354L1.09386 5.61812C0.974201 5.74654 0.909057 5.91639 0.912154 6.09189C0.91525 6.26738 0.986345 6.43483 1.11046 6.55894C1.23457 6.68306 1.40202 6.75415 1.57752 6.75725C1.75302 6.76035 1.92286 6.6952 2.05128 6.57554L6.31451 2.31231V13.3226C6.31685 13.5015 6.38898 13.6725 6.51551 13.799Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$e = [_hoisted_1$h];
function render$o(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_2$e, 16);
}
__name(render$o, "render$o");
script$p.render = render$o;
function _typeof$c(o) {
  "@babel/helpers - typeof";
  return _typeof$c = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$c(o);
}
__name(_typeof$c, "_typeof$c");
function _defineProperty$b(e, r, t) {
  return (r = _toPropertyKey$b(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty$b, "_defineProperty$b");
function _toPropertyKey$b(t) {
  var i = _toPrimitive$b(t, "string");
  return "symbol" == _typeof$c(i) ? i : i + "";
}
__name(_toPropertyKey$b, "_toPropertyKey$b");
function _toPrimitive$b(t, r) {
  if ("object" != _typeof$c(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof$c(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive$b, "_toPrimitive$b");
var theme$2 = /* @__PURE__ */ __name(function theme(_ref) {
  var dt = _ref.dt;
  return "\n.p-paginator {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-wrap: wrap;\n    background: ".concat(dt("paginator.background"), ";\n    color: ").concat(dt("paginator.color"), ";\n    padding: ").concat(dt("paginator.padding"), ";\n    border-radius: ").concat(dt("paginator.border.radius"), ";\n    gap: ").concat(dt("paginator.gap"), ";\n}\n\n.p-paginator-content {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-wrap: wrap;\n    gap: ").concat(dt("paginator.gap"), ";\n}\n\n.p-paginator-content-start {\n    margin-right: auto;\n}\n\n.p-paginator-content-end {\n    margin-left: auto;\n}\n\n.p-paginator-page,\n.p-paginator-next,\n.p-paginator-last,\n.p-paginator-first,\n.p-paginator-prev {\n    cursor: pointer;\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    line-height: 1;\n    user-select: none;\n    overflow: hidden;\n    position: relative;\n    background: ").concat(dt("paginator.nav.button.background"), ";\n    border: 0 none;\n    color: ").concat(dt("paginator.nav.button.color"), ";\n    min-width: ").concat(dt("paginator.nav.button.width"), ";\n    height: ").concat(dt("paginator.nav.button.height"), ";\n    transition: background ").concat(dt("paginator.transition.duration"), ", color ").concat(dt("paginator.transition.duration"), ", outline-color ").concat(dt("paginator.transition.duration"), ", box-shadow ").concat(dt("paginator.transition.duration"), ";\n    border-radius: ").concat(dt("paginator.nav.button.border.radius"), ";\n    padding: 0;\n    margin: 0;\n}\n\n.p-paginator-page:focus-visible,\n.p-paginator-next:focus-visible,\n.p-paginator-last:focus-visible,\n.p-paginator-first:focus-visible,\n.p-paginator-prev:focus-visible {\n    box-shadow: ").concat(dt("paginator.nav.button.focus.ring.shadow"), ";\n    outline: ").concat(dt("paginator.nav.button.focus.ring.width"), " ").concat(dt("paginator.nav.button.focus.ring.style"), " ").concat(dt("paginator.nav.button.focus.ring.color"), ";\n    outline-offset: ").concat(dt("paginator.nav.button.focus.ring.offset"), ";\n}\n\n.p-paginator-page:not(.p-disabled):not(.p-paginator-page-selected):hover,\n.p-paginator-first:not(.p-disabled):hover,\n.p-paginator-prev:not(.p-disabled):hover,\n.p-paginator-next:not(.p-disabled):hover,\n.p-paginator-last:not(.p-disabled):hover {\n    background: ").concat(dt("paginator.nav.button.hover.background"), ";\n    color: ").concat(dt("paginator.nav.button.hover.color"), ";\n}\n\n.p-paginator-page.p-paginator-page-selected {\n    background: ").concat(dt("paginator.nav.button.selected.background"), ";\n    color: ").concat(dt("paginator.nav.button.selected.color"), ";\n}\n\n.p-paginator-current {\n    color: ").concat(dt("paginator.current.page.report.color"), ";\n}\n\n.p-paginator-pages {\n    display: flex;\n    align-items: center;\n    gap: ").concat(dt("paginator.gap"), ";\n}\n\n.p-paginator-jtp-input .p-inputtext {\n    max-width: ").concat(dt("paginator.jump.to.page.input.max.width"), ";\n}\n");
}, "theme");
var classes$2 = {
  paginator: /* @__PURE__ */ __name(function paginator(_ref2) {
    var instance = _ref2.instance, key = _ref2.key;
    return ["p-paginator p-component", _defineProperty$b({
      "p-paginator-default": !instance.hasBreakpoints()
    }, "p-paginator-".concat(key), instance.hasBreakpoints())];
  }, "paginator"),
  content: "p-paginator-content",
  contentStart: "p-paginator-content-start",
  contentEnd: "p-paginator-content-end",
  first: /* @__PURE__ */ __name(function first(_ref4) {
    var instance = _ref4.instance;
    return ["p-paginator-first", {
      "p-disabled": instance.$attrs.disabled
    }];
  }, "first"),
  firstIcon: "p-paginator-first-icon",
  prev: /* @__PURE__ */ __name(function prev(_ref5) {
    var instance = _ref5.instance;
    return ["p-paginator-prev", {
      "p-disabled": instance.$attrs.disabled
    }];
  }, "prev"),
  prevIcon: "p-paginator-prev-icon",
  next: /* @__PURE__ */ __name(function next(_ref6) {
    var instance = _ref6.instance;
    return ["p-paginator-next", {
      "p-disabled": instance.$attrs.disabled
    }];
  }, "next"),
  nextIcon: "p-paginator-next-icon",
  last: /* @__PURE__ */ __name(function last(_ref7) {
    var instance = _ref7.instance;
    return ["p-paginator-last", {
      "p-disabled": instance.$attrs.disabled
    }];
  }, "last"),
  lastIcon: "p-paginator-last-icon",
  pages: "p-paginator-pages",
  page: /* @__PURE__ */ __name(function page(_ref8) {
    var props = _ref8.props, pageLink = _ref8.pageLink;
    return ["p-paginator-page", {
      "p-paginator-page-selected": pageLink - 1 === props.page
    }];
  }, "page"),
  current: "p-paginator-current",
  pcRowPerPageDropdown: "p-paginator-rpp-dropdown",
  pcJumpToPageDropdown: "p-paginator-jtp-dropdown",
  pcJumpToPageInput: "p-paginator-jtp-input"
};
var PaginatorStyle = BaseStyle.extend({
  name: "paginator",
  theme: theme$2,
  classes: classes$2
});
var script$o = {
  name: "AngleDoubleLeftIcon",
  "extends": script$t
};
var _hoisted_1$g = /* @__PURE__ */ createBaseVNode("path", {
  "fill-rule": "evenodd",
  "clip-rule": "evenodd",
  d: "M5.71602 11.164C5.80782 11.2021 5.9063 11.2215 6.00569 11.221C6.20216 11.2301 6.39427 11.1612 6.54025 11.0294C6.68191 10.8875 6.76148 10.6953 6.76148 10.4948C6.76148 10.2943 6.68191 10.1021 6.54025 9.96024L3.51441 6.9344L6.54025 3.90855C6.624 3.76126 6.65587 3.59011 6.63076 3.42254C6.60564 3.25498 6.525 3.10069 6.40175 2.98442C6.2785 2.86815 6.11978 2.79662 5.95104 2.7813C5.78229 2.76598 5.61329 2.80776 5.47112 2.89994L1.97123 6.39983C1.82957 6.54167 1.75 6.73393 1.75 6.9344C1.75 7.13486 1.82957 7.32712 1.97123 7.46896L5.47112 10.9991C5.54096 11.0698 5.62422 11.1259 5.71602 11.164ZM11.0488 10.9689C11.1775 11.1156 11.3585 11.2061 11.5531 11.221C11.7477 11.2061 11.9288 11.1156 12.0574 10.9689C12.1815 10.8302 12.25 10.6506 12.25 10.4645C12.25 10.2785 12.1815 10.0989 12.0574 9.96024L9.03158 6.93439L12.0574 3.90855C12.1248 3.76739 12.1468 3.60881 12.1204 3.45463C12.0939 3.30045 12.0203 3.15826 11.9097 3.04765C11.7991 2.93703 11.6569 2.86343 11.5027 2.83698C11.3486 2.81053 11.19 2.83252 11.0488 2.89994L7.51865 6.36957C7.37699 6.51141 7.29742 6.70367 7.29742 6.90414C7.29742 7.1046 7.37699 7.29686 7.51865 7.4387L11.0488 10.9689Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$d = [_hoisted_1$g];
function render$n(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_2$d, 16);
}
__name(render$n, "render$n");
script$o.render = render$n;
var script$n = {
  name: "AngleDoubleRightIcon",
  "extends": script$t
};
var _hoisted_1$f = /* @__PURE__ */ createBaseVNode("path", {
  "fill-rule": "evenodd",
  "clip-rule": "evenodd",
  d: "M7.68757 11.1451C7.7791 11.1831 7.8773 11.2024 7.9764 11.2019C8.07769 11.1985 8.17721 11.1745 8.26886 11.1312C8.36052 11.088 8.44238 11.0265 8.50943 10.9505L12.0294 7.49085C12.1707 7.34942 12.25 7.15771 12.25 6.95782C12.25 6.75794 12.1707 6.56622 12.0294 6.42479L8.50943 2.90479C8.37014 2.82159 8.20774 2.78551 8.04633 2.80192C7.88491 2.81833 7.73309 2.88635 7.6134 2.99588C7.4937 3.10541 7.41252 3.25061 7.38189 3.40994C7.35126 3.56927 7.37282 3.73423 7.44337 3.88033L10.4605 6.89748L7.44337 9.91463C7.30212 10.0561 7.22278 10.2478 7.22278 10.4477C7.22278 10.6475 7.30212 10.8393 7.44337 10.9807C7.51301 11.0512 7.59603 11.1071 7.68757 11.1451ZM1.94207 10.9505C2.07037 11.0968 2.25089 11.1871 2.44493 11.2019C2.63898 11.1871 2.81949 11.0968 2.94779 10.9505L6.46779 7.49085C6.60905 7.34942 6.68839 7.15771 6.68839 6.95782C6.68839 6.75793 6.60905 6.56622 6.46779 6.42479L2.94779 2.90479C2.80704 2.83757 2.6489 2.81563 2.49517 2.84201C2.34143 2.86839 2.19965 2.94178 2.08936 3.05207C1.97906 3.16237 1.90567 3.30415 1.8793 3.45788C1.85292 3.61162 1.87485 3.76975 1.94207 3.9105L4.95922 6.92765L1.94207 9.9448C1.81838 10.0831 1.75 10.2621 1.75 10.4477C1.75 10.6332 1.81838 10.8122 1.94207 10.9505Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$c = [_hoisted_1$f];
function render$m(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_2$c, 16);
}
__name(render$m, "render$m");
script$n.render = render$m;
var script$m = {
  name: "AngleLeftIcon",
  "extends": script$t
};
var _hoisted_1$e = /* @__PURE__ */ createBaseVNode("path", {
  d: "M8.75 11.185C8.65146 11.1854 8.55381 11.1662 8.4628 11.1284C8.37179 11.0906 8.28924 11.0351 8.22 10.965L4.72 7.46496C4.57955 7.32433 4.50066 7.13371 4.50066 6.93496C4.50066 6.73621 4.57955 6.54558 4.72 6.40496L8.22 2.93496C8.36095 2.84357 8.52851 2.80215 8.69582 2.81733C8.86312 2.83252 9.02048 2.90344 9.14268 3.01872C9.26487 3.134 9.34483 3.28696 9.36973 3.4531C9.39463 3.61924 9.36303 3.78892 9.28 3.93496L6.28 6.93496L9.28 9.93496C9.42045 10.0756 9.49934 10.2662 9.49934 10.465C9.49934 10.6637 9.42045 10.8543 9.28 10.995C9.13526 11.1257 8.9448 11.1939 8.75 11.185Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$b = [_hoisted_1$e];
function render$l(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_2$b, 16);
}
__name(render$l, "render$l");
script$m.render = render$l;
var script$a$1 = {
  name: "BasePaginator",
  "extends": script$s,
  props: {
    totalRecords: {
      type: Number,
      "default": 0
    },
    rows: {
      type: Number,
      "default": 0
    },
    first: {
      type: Number,
      "default": 0
    },
    pageLinkSize: {
      type: Number,
      "default": 5
    },
    rowsPerPageOptions: {
      type: Array,
      "default": null
    },
    template: {
      type: [Object, String],
      "default": "FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
    },
    currentPageReportTemplate: {
      type: null,
      "default": "({currentPage} of {totalPages})"
    },
    alwaysShow: {
      type: Boolean,
      "default": true
    }
  },
  style: PaginatorStyle,
  provide: /* @__PURE__ */ __name(function provide2() {
    return {
      $pcPaginator: this,
      $parentInstance: this
    };
  }, "provide")
};
var script$9$1 = {
  name: "CurrentPageReport",
  hostName: "Paginator",
  "extends": script$s,
  props: {
    pageCount: {
      type: Number,
      "default": 0
    },
    currentPage: {
      type: Number,
      "default": 0
    },
    page: {
      type: Number,
      "default": 0
    },
    first: {
      type: Number,
      "default": 0
    },
    rows: {
      type: Number,
      "default": 0
    },
    totalRecords: {
      type: Number,
      "default": 0
    },
    template: {
      type: String,
      "default": "({currentPage} of {totalPages})"
    }
  },
  computed: {
    text: /* @__PURE__ */ __name(function text() {
      var text2 = this.template.replace("{currentPage}", this.currentPage).replace("{totalPages}", this.pageCount).replace("{first}", this.pageCount > 0 ? this.first + 1 : 0).replace("{last}", Math.min(this.first + this.rows, this.totalRecords)).replace("{rows}", this.rows).replace("{totalRecords}", this.totalRecords);
      return text2;
    }, "text")
  }
};
function render$9$1(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("span", mergeProps({
    "class": _ctx.cx("current")
  }, _ctx.ptm("current")), toDisplayString($options.text), 17);
}
__name(render$9$1, "render$9$1");
script$9$1.render = render$9$1;
var script$8$1 = {
  name: "FirstPageLink",
  hostName: "Paginator",
  "extends": script$s,
  props: {
    template: {
      type: Function,
      "default": null
    }
  },
  methods: {
    getPTOptions: /* @__PURE__ */ __name(function getPTOptions(key) {
      return this.ptm(key, {
        context: {
          disabled: this.$attrs.disabled
        }
      });
    }, "getPTOptions")
  },
  components: {
    AngleDoubleLeftIcon: script$o
  },
  directives: {
    ripple: Ripple
  }
};
function render$8$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _directive_ripple = resolveDirective("ripple");
  return withDirectives((openBlock(), createElementBlock("button", mergeProps({
    "class": _ctx.cx("first"),
    type: "button"
  }, $options.getPTOptions("first"), {
    "data-pc-group-section": "pagebutton"
  }), [(openBlock(), createBlock(resolveDynamicComponent($props.template || "AngleDoubleLeftIcon"), mergeProps({
    "class": _ctx.cx("firstIcon")
  }, $options.getPTOptions("firstIcon")), null, 16, ["class"]))], 16)), [[_directive_ripple]]);
}
__name(render$8$1, "render$8$1");
script$8$1.render = render$8$1;
var script$7$1 = {
  name: "JumpToPageDropdown",
  hostName: "Paginator",
  "extends": script$s,
  emits: ["page-change"],
  props: {
    page: Number,
    pageCount: Number,
    disabled: Boolean,
    templates: null
  },
  methods: {
    onChange: /* @__PURE__ */ __name(function onChange(value) {
      this.$emit("page-change", value);
    }, "onChange")
  },
  computed: {
    pageOptions: /* @__PURE__ */ __name(function pageOptions() {
      var opts = [];
      for (var i = 0; i < this.pageCount; i++) {
        opts.push({
          label: String(i + 1),
          value: i
        });
      }
      return opts;
    }, "pageOptions")
  },
  components: {
    JTPSelect: script$u
  }
};
function render$7$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_JTPSelect = resolveComponent("JTPSelect");
  return openBlock(), createBlock(_component_JTPSelect, {
    modelValue: $props.page,
    options: $options.pageOptions,
    optionLabel: "label",
    optionValue: "value",
    "onUpdate:modelValue": _cache[0] || (_cache[0] = function($event) {
      return $options.onChange($event);
    }),
    "class": normalizeClass(_ctx.cx("pcJumpToPageDropdown")),
    disabled: $props.disabled,
    unstyled: _ctx.unstyled,
    pt: _ctx.ptm("pcJumpToPageDropdown"),
    "data-pc-group-section": "pagedropdown"
  }, createSlots({
    _: 2
  }, [$props.templates["jumptopagedropdownicon"] ? {
    name: "dropdownicon",
    fn: withCtx(function(slotProps) {
      return [(openBlock(), createBlock(resolveDynamicComponent($props.templates["jumptopagedropdownicon"]), {
        "class": normalizeClass(slotProps["class"])
      }, null, 8, ["class"]))];
    }),
    key: "0"
  } : void 0]), 1032, ["modelValue", "options", "class", "disabled", "unstyled", "pt"]);
}
__name(render$7$1, "render$7$1");
script$7$1.render = render$7$1;
var script$6$1 = {
  name: "JumpToPageInput",
  hostName: "Paginator",
  "extends": script$s,
  inheritAttrs: false,
  emits: ["page-change"],
  props: {
    page: Number,
    pageCount: Number,
    disabled: Boolean
  },
  data: /* @__PURE__ */ __name(function data() {
    return {
      d_page: this.page
    };
  }, "data"),
  watch: {
    page: /* @__PURE__ */ __name(function page2(newValue) {
      this.d_page = newValue;
    }, "page")
  },
  methods: {
    onChange: /* @__PURE__ */ __name(function onChange2(value) {
      if (value !== this.page) {
        this.d_page = value;
        this.$emit("page-change", value - 1);
      }
    }, "onChange")
  },
  computed: {
    inputArialabel: /* @__PURE__ */ __name(function inputArialabel() {
      return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.jumpToPageInputLabel : void 0;
    }, "inputArialabel")
  },
  components: {
    JTPInput: script$v
  }
};
function render$6$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_JTPInput = resolveComponent("JTPInput");
  return openBlock(), createBlock(_component_JTPInput, {
    ref: "jtpInput",
    modelValue: $data.d_page,
    "class": normalizeClass(_ctx.cx("pcJumpToPageInput")),
    "aria-label": $options.inputArialabel,
    disabled: $props.disabled,
    "onUpdate:modelValue": $options.onChange,
    unstyled: _ctx.unstyled,
    pt: _ctx.ptm("pcJumpToPageInput")
  }, null, 8, ["modelValue", "class", "aria-label", "disabled", "onUpdate:modelValue", "unstyled", "pt"]);
}
__name(render$6$1, "render$6$1");
script$6$1.render = render$6$1;
var script$5$1 = {
  name: "LastPageLink",
  hostName: "Paginator",
  "extends": script$s,
  props: {
    template: {
      type: Function,
      "default": null
    }
  },
  methods: {
    getPTOptions: /* @__PURE__ */ __name(function getPTOptions2(key) {
      return this.ptm(key, {
        context: {
          disabled: this.$attrs.disabled
        }
      });
    }, "getPTOptions")
  },
  components: {
    AngleDoubleRightIcon: script$n
  },
  directives: {
    ripple: Ripple
  }
};
function render$5$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _directive_ripple = resolveDirective("ripple");
  return withDirectives((openBlock(), createElementBlock("button", mergeProps({
    "class": _ctx.cx("last"),
    type: "button"
  }, $options.getPTOptions("last"), {
    "data-pc-group-section": "pagebutton"
  }), [(openBlock(), createBlock(resolveDynamicComponent($props.template || "AngleDoubleRightIcon"), mergeProps({
    "class": _ctx.cx("lastIcon")
  }, $options.getPTOptions("lastIcon")), null, 16, ["class"]))], 16)), [[_directive_ripple]]);
}
__name(render$5$1, "render$5$1");
script$5$1.render = render$5$1;
var script$4$1 = {
  name: "NextPageLink",
  hostName: "Paginator",
  "extends": script$s,
  props: {
    template: {
      type: Function,
      "default": null
    }
  },
  methods: {
    getPTOptions: /* @__PURE__ */ __name(function getPTOptions3(key) {
      return this.ptm(key, {
        context: {
          disabled: this.$attrs.disabled
        }
      });
    }, "getPTOptions")
  },
  components: {
    AngleRightIcon: script$w
  },
  directives: {
    ripple: Ripple
  }
};
function render$4$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _directive_ripple = resolveDirective("ripple");
  return withDirectives((openBlock(), createElementBlock("button", mergeProps({
    "class": _ctx.cx("next"),
    type: "button"
  }, $options.getPTOptions("next"), {
    "data-pc-group-section": "pagebutton"
  }), [(openBlock(), createBlock(resolveDynamicComponent($props.template || "AngleRightIcon"), mergeProps({
    "class": _ctx.cx("nextIcon")
  }, $options.getPTOptions("nextIcon")), null, 16, ["class"]))], 16)), [[_directive_ripple]]);
}
__name(render$4$1, "render$4$1");
script$4$1.render = render$4$1;
var script$3$1 = {
  name: "PageLinks",
  hostName: "Paginator",
  "extends": script$s,
  inheritAttrs: false,
  emits: ["click"],
  props: {
    value: Array,
    page: Number
  },
  methods: {
    getPTOptions: /* @__PURE__ */ __name(function getPTOptions4(pageLink, key) {
      return this.ptm(key, {
        context: {
          active: pageLink === this.page
        }
      });
    }, "getPTOptions"),
    onPageLinkClick: /* @__PURE__ */ __name(function onPageLinkClick(event2, pageLink) {
      this.$emit("click", {
        originalEvent: event2,
        value: pageLink
      });
    }, "onPageLinkClick"),
    ariaPageLabel: /* @__PURE__ */ __name(function ariaPageLabel(value) {
      return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.pageLabel.replace(/{page}/g, value) : void 0;
    }, "ariaPageLabel")
  },
  directives: {
    ripple: Ripple
  }
};
var _hoisted_1$d = ["aria-label", "aria-current", "onClick", "data-p-active"];
function render$3$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _directive_ripple = resolveDirective("ripple");
  return openBlock(), createElementBlock("span", mergeProps({
    "class": _ctx.cx("pages")
  }, _ctx.ptm("pages")), [(openBlock(true), createElementBlock(Fragment, null, renderList($props.value, function(pageLink) {
    return withDirectives((openBlock(), createElementBlock("button", mergeProps({
      key: pageLink,
      "class": _ctx.cx("page", {
        pageLink
      }),
      type: "button",
      "aria-label": $options.ariaPageLabel(pageLink),
      "aria-current": pageLink - 1 === $props.page ? "page" : void 0,
      onClick: /* @__PURE__ */ __name(function onClick3($event) {
        return $options.onPageLinkClick($event, pageLink);
      }, "onClick"),
      ref_for: true
    }, $options.getPTOptions(pageLink - 1, "page"), {
      "data-p-active": pageLink - 1 === $props.page
    }), [createTextVNode(toDisplayString(pageLink), 1)], 16, _hoisted_1$d)), [[_directive_ripple]]);
  }), 128))], 16);
}
__name(render$3$1, "render$3$1");
script$3$1.render = render$3$1;
var script$2$1 = {
  name: "PrevPageLink",
  hostName: "Paginator",
  "extends": script$s,
  props: {
    template: {
      type: Function,
      "default": null
    }
  },
  methods: {
    getPTOptions: /* @__PURE__ */ __name(function getPTOptions5(key) {
      return this.ptm(key, {
        context: {
          disabled: this.$attrs.disabled
        }
      });
    }, "getPTOptions")
  },
  components: {
    AngleLeftIcon: script$m
  },
  directives: {
    ripple: Ripple
  }
};
function render$2$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _directive_ripple = resolveDirective("ripple");
  return withDirectives((openBlock(), createElementBlock("button", mergeProps({
    "class": _ctx.cx("prev"),
    type: "button"
  }, $options.getPTOptions("prev"), {
    "data-pc-group-section": "pagebutton"
  }), [(openBlock(), createBlock(resolveDynamicComponent($props.template || "AngleLeftIcon"), mergeProps({
    "class": _ctx.cx("prevIcon")
  }, $options.getPTOptions("prevIcon")), null, 16, ["class"]))], 16)), [[_directive_ripple]]);
}
__name(render$2$1, "render$2$1");
script$2$1.render = render$2$1;
var script$1$2 = {
  name: "RowsPerPageDropdown",
  hostName: "Paginator",
  "extends": script$s,
  emits: ["rows-change"],
  props: {
    options: Array,
    rows: Number,
    disabled: Boolean,
    templates: null
  },
  methods: {
    onChange: /* @__PURE__ */ __name(function onChange3(value) {
      this.$emit("rows-change", value);
    }, "onChange")
  },
  computed: {
    rowsOptions: /* @__PURE__ */ __name(function rowsOptions() {
      var opts = [];
      if (this.options) {
        for (var i = 0; i < this.options.length; i++) {
          opts.push({
            label: String(this.options[i]),
            value: this.options[i]
          });
        }
      }
      return opts;
    }, "rowsOptions")
  },
  components: {
    RPPSelect: script$u
  }
};
function render$1$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_RPPSelect = resolveComponent("RPPSelect");
  return openBlock(), createBlock(_component_RPPSelect, {
    modelValue: $props.rows,
    options: $options.rowsOptions,
    optionLabel: "label",
    optionValue: "value",
    "onUpdate:modelValue": _cache[0] || (_cache[0] = function($event) {
      return $options.onChange($event);
    }),
    "class": normalizeClass(_ctx.cx("pcRowPerPageDropdown")),
    disabled: $props.disabled,
    unstyled: _ctx.unstyled,
    pt: _ctx.ptm("pcRowPerPageDropdown"),
    "data-pc-group-section": "pagedropdown"
  }, createSlots({
    _: 2
  }, [$props.templates["rowsperpagedropdownicon"] ? {
    name: "dropdownicon",
    fn: withCtx(function(slotProps) {
      return [(openBlock(), createBlock(resolveDynamicComponent($props.templates["rowsperpagedropdownicon"]), {
        "class": normalizeClass(slotProps["class"])
      }, null, 8, ["class"]))];
    }),
    key: "0"
  } : void 0]), 1032, ["modelValue", "options", "class", "disabled", "unstyled", "pt"]);
}
__name(render$1$1, "render$1$1");
script$1$2.render = render$1$1;
function _toConsumableArray$1(r) {
  return _arrayWithoutHoles$1(r) || _iterableToArray$1(r) || _unsupportedIterableToArray$3(r) || _nonIterableSpread$1();
}
__name(_toConsumableArray$1, "_toConsumableArray$1");
function _nonIterableSpread$1() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
__name(_nonIterableSpread$1, "_nonIterableSpread$1");
function _iterableToArray$1(r) {
  if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r);
}
__name(_iterableToArray$1, "_iterableToArray$1");
function _arrayWithoutHoles$1(r) {
  if (Array.isArray(r)) return _arrayLikeToArray$3(r);
}
__name(_arrayWithoutHoles$1, "_arrayWithoutHoles$1");
function _typeof$b(o) {
  "@babel/helpers - typeof";
  return _typeof$b = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$b(o);
}
__name(_typeof$b, "_typeof$b");
function _slicedToArray$1(r, e) {
  return _arrayWithHoles$1(r) || _iterableToArrayLimit$1(r, e) || _unsupportedIterableToArray$3(r, e) || _nonIterableRest$1();
}
__name(_slicedToArray$1, "_slicedToArray$1");
function _nonIterableRest$1() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
__name(_nonIterableRest$1, "_nonIterableRest$1");
function _unsupportedIterableToArray$3(r, a) {
  if (r) {
    if ("string" == typeof r) return _arrayLikeToArray$3(r, a);
    var t = {}.toString.call(r).slice(8, -1);
    return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray$3(r, a) : void 0;
  }
}
__name(_unsupportedIterableToArray$3, "_unsupportedIterableToArray$3");
function _arrayLikeToArray$3(r, a) {
  (null == a || a > r.length) && (a = r.length);
  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
  return n;
}
__name(_arrayLikeToArray$3, "_arrayLikeToArray$3");
function _iterableToArrayLimit$1(r, l) {
  var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
  if (null != t) {
    var e, n, i, u, a = [], f = true, o = false;
    try {
      if (i = (t = t.call(r)).next, 0 === l) {
        if (Object(t) !== t) return;
        f = false;
      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = true) ;
    } catch (r2) {
      o = true, n = r2;
    } finally {
      try {
        if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return;
      } finally {
        if (o) throw n;
      }
    }
    return a;
  }
}
__name(_iterableToArrayLimit$1, "_iterableToArrayLimit$1");
function _arrayWithHoles$1(r) {
  if (Array.isArray(r)) return r;
}
__name(_arrayWithHoles$1, "_arrayWithHoles$1");
var script$l = {
  name: "Paginator",
  "extends": script$a$1,
  inheritAttrs: false,
  emits: ["update:first", "update:rows", "page"],
  data: /* @__PURE__ */ __name(function data2() {
    return {
      d_first: this.first,
      d_rows: this.rows
    };
  }, "data"),
  watch: {
    first: /* @__PURE__ */ __name(function first2(newValue) {
      this.d_first = newValue;
    }, "first"),
    rows: /* @__PURE__ */ __name(function rows(newValue) {
      this.d_rows = newValue;
    }, "rows"),
    totalRecords: /* @__PURE__ */ __name(function totalRecords(newValue) {
      if (this.page > 0 && newValue && this.d_first >= newValue) {
        this.changePage(this.pageCount - 1);
      }
    }, "totalRecords")
  },
  mounted: /* @__PURE__ */ __name(function mounted2() {
    this.setPaginatorAttribute();
    this.createStyle();
  }, "mounted"),
  methods: {
    changePage: /* @__PURE__ */ __name(function changePage(p) {
      var pc = this.pageCount;
      if (p >= 0 && p < pc) {
        this.d_first = this.d_rows * p;
        var state = {
          page: p,
          first: this.d_first,
          rows: this.d_rows,
          pageCount: pc
        };
        this.$emit("update:first", this.d_first);
        this.$emit("update:rows", this.d_rows);
        this.$emit("page", state);
      }
    }, "changePage"),
    changePageToFirst: /* @__PURE__ */ __name(function changePageToFirst(event2) {
      if (!this.isFirstPage) {
        this.changePage(0);
      }
      event2.preventDefault();
    }, "changePageToFirst"),
    changePageToPrev: /* @__PURE__ */ __name(function changePageToPrev(event2) {
      this.changePage(this.page - 1);
      event2.preventDefault();
    }, "changePageToPrev"),
    changePageLink: /* @__PURE__ */ __name(function changePageLink(event2) {
      this.changePage(event2.value - 1);
      event2.originalEvent.preventDefault();
    }, "changePageLink"),
    changePageToNext: /* @__PURE__ */ __name(function changePageToNext(event2) {
      this.changePage(this.page + 1);
      event2.preventDefault();
    }, "changePageToNext"),
    changePageToLast: /* @__PURE__ */ __name(function changePageToLast(event2) {
      if (!this.isLastPage) {
        this.changePage(this.pageCount - 1);
      }
      event2.preventDefault();
    }, "changePageToLast"),
    onRowChange: /* @__PURE__ */ __name(function onRowChange(value) {
      this.d_rows = value;
      this.changePage(this.page);
    }, "onRowChange"),
    createStyle: /* @__PURE__ */ __name(function createStyle() {
      var _this = this;
      if (this.hasBreakpoints() && !this.isUnstyled) {
        var _this$$primevue;
        this.styleElement = document.createElement("style");
        this.styleElement.type = "text/css";
        setAttribute(this.styleElement, "nonce", (_this$$primevue = this.$primevue) === null || _this$$primevue === void 0 || (_this$$primevue = _this$$primevue.config) === null || _this$$primevue === void 0 || (_this$$primevue = _this$$primevue.csp) === null || _this$$primevue === void 0 ? void 0 : _this$$primevue.nonce);
        document.head.appendChild(this.styleElement);
        var innerHTML = "";
        var keys = Object.keys(this.template);
        var sortedBreakpoints = {};
        keys.sort(function(a, b) {
          return parseInt(a) - parseInt(b);
        }).forEach(function(key2) {
          sortedBreakpoints[key2] = _this.template[key2];
        });
        for (var _i = 0, _Object$entries = Object.entries(Object.entries(sortedBreakpoints)); _i < _Object$entries.length; _i++) {
          var _Object$entries$_i = _slicedToArray$1(_Object$entries[_i], 2), index = _Object$entries$_i[0], _Object$entries$_i$ = _slicedToArray$1(_Object$entries$_i[1], 1), key = _Object$entries$_i$[0];
          var minValue = void 0, calculatedMinValue = void 0;
          if (key !== "default" && typeof Object.keys(sortedBreakpoints)[index - 1] === "string") {
            calculatedMinValue = Number(Object.keys(sortedBreakpoints)[index - 1].slice(0, -2)) + 1 + "px";
          } else {
            calculatedMinValue = Object.keys(sortedBreakpoints)[index - 1];
          }
          minValue = Object.entries(sortedBreakpoints)[index - 1] ? "and (min-width:".concat(calculatedMinValue, ")") : "";
          if (key === "default") {
            innerHTML += "\n                            @media screen ".concat(minValue, " {\n                                .paginator[").concat(this.attributeSelector, "],\n                                    display: flex;\n                                }\n                            }\n                        ");
          } else {
            innerHTML += "\n.paginator[".concat(this.attributeSelector, "], .p-paginator-").concat(key, " {\n    display: none;\n}\n@media screen ").concat(minValue, " and (max-width: ").concat(key, ") {\n    .paginator[").concat(this.attributeSelector, "], .p-paginator-").concat(key, " {\n        display: flex;\n    }\n    .paginator[").concat(this.attributeSelector, "],\n    .p-paginator-default{\n        display: none;\n    }\n}\n                    ");
          }
        }
        this.styleElement.innerHTML = innerHTML;
      }
    }, "createStyle"),
    hasBreakpoints: /* @__PURE__ */ __name(function hasBreakpoints() {
      return _typeof$b(this.template) === "object";
    }, "hasBreakpoints"),
    setPaginatorAttribute: /* @__PURE__ */ __name(function setPaginatorAttribute() {
      var _this2 = this;
      if (this.$refs.paginator && this.$refs.paginator.length >= 0) {
        _toConsumableArray$1(this.$refs.paginator).forEach(function(el) {
          el.setAttribute(_this2.attributeSelector, "");
        });
      }
    }, "setPaginatorAttribute"),
    getAriaLabel: /* @__PURE__ */ __name(function getAriaLabel(labelType) {
      return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria[labelType] : void 0;
    }, "getAriaLabel")
  },
  computed: {
    templateItems: /* @__PURE__ */ __name(function templateItems() {
      var keys = {};
      if (this.hasBreakpoints()) {
        keys = this.template;
        if (!keys["default"]) {
          keys["default"] = "FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown";
        }
        for (var item in keys) {
          keys[item] = this.template[item].split(" ").map(function(value) {
            return value.trim();
          });
        }
        return keys;
      }
      keys["default"] = this.template.split(" ").map(function(value) {
        return value.trim();
      });
      return keys;
    }, "templateItems"),
    page: /* @__PURE__ */ __name(function page3() {
      return Math.floor(this.d_first / this.d_rows);
    }, "page"),
    pageCount: /* @__PURE__ */ __name(function pageCount() {
      return Math.ceil(this.totalRecords / this.d_rows);
    }, "pageCount"),
    isFirstPage: /* @__PURE__ */ __name(function isFirstPage() {
      return this.page === 0;
    }, "isFirstPage"),
    isLastPage: /* @__PURE__ */ __name(function isLastPage() {
      return this.page === this.pageCount - 1;
    }, "isLastPage"),
    calculatePageLinkBoundaries: /* @__PURE__ */ __name(function calculatePageLinkBoundaries() {
      var numberOfPages = this.pageCount;
      var visiblePages = Math.min(this.pageLinkSize, numberOfPages);
      var start = Math.max(0, Math.ceil(this.page - visiblePages / 2));
      var end = Math.min(numberOfPages - 1, start + visiblePages - 1);
      var delta = this.pageLinkSize - (end - start + 1);
      start = Math.max(0, start - delta);
      return [start, end];
    }, "calculatePageLinkBoundaries"),
    pageLinks: /* @__PURE__ */ __name(function pageLinks() {
      var pageLinks2 = [];
      var boundaries = this.calculatePageLinkBoundaries;
      var start = boundaries[0];
      var end = boundaries[1];
      for (var i = start; i <= end; i++) {
        pageLinks2.push(i + 1);
      }
      return pageLinks2;
    }, "pageLinks"),
    currentState: /* @__PURE__ */ __name(function currentState() {
      return {
        page: this.page,
        first: this.d_first,
        rows: this.d_rows
      };
    }, "currentState"),
    empty: /* @__PURE__ */ __name(function empty() {
      return this.pageCount === 0;
    }, "empty"),
    currentPage: /* @__PURE__ */ __name(function currentPage() {
      return this.pageCount > 0 ? this.page + 1 : 0;
    }, "currentPage"),
    attributeSelector: /* @__PURE__ */ __name(function attributeSelector() {
      return UniqueComponentId();
    }, "attributeSelector")
  },
  components: {
    CurrentPageReport: script$9$1,
    FirstPageLink: script$8$1,
    LastPageLink: script$5$1,
    NextPageLink: script$4$1,
    PageLinks: script$3$1,
    PrevPageLink: script$2$1,
    RowsPerPageDropdown: script$1$2,
    JumpToPageDropdown: script$7$1,
    JumpToPageInput: script$6$1
  }
};
function render$k(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_FirstPageLink = resolveComponent("FirstPageLink");
  var _component_PrevPageLink = resolveComponent("PrevPageLink");
  var _component_NextPageLink = resolveComponent("NextPageLink");
  var _component_LastPageLink = resolveComponent("LastPageLink");
  var _component_PageLinks = resolveComponent("PageLinks");
  var _component_CurrentPageReport = resolveComponent("CurrentPageReport");
  var _component_RowsPerPageDropdown = resolveComponent("RowsPerPageDropdown");
  var _component_JumpToPageDropdown = resolveComponent("JumpToPageDropdown");
  var _component_JumpToPageInput = resolveComponent("JumpToPageInput");
  return (_ctx.alwaysShow ? true : $options.pageLinks && $options.pageLinks.length > 1) ? (openBlock(), createElementBlock("nav", normalizeProps(mergeProps({
    key: 0
  }, _ctx.ptmi("paginatorContainer"))), [(openBlock(true), createElementBlock(Fragment, null, renderList($options.templateItems, function(value, key) {
    return openBlock(), createElementBlock("div", mergeProps({
      key,
      ref_for: true,
      ref: "paginator",
      "class": _ctx.cx("paginator", {
        key
      })
    }, _ctx.ptm("root")), [_ctx.$slots.start ? (openBlock(), createElementBlock("div", mergeProps({
      key: 0,
      "class": _ctx.cx("contentStart"),
      ref_for: true
    }, _ctx.ptm("contentStart")), [renderSlot(_ctx.$slots, "start", {
      state: $options.currentState
    })], 16)) : createCommentVNode("", true), createBaseVNode("div", mergeProps({
      "class": _ctx.cx("content"),
      ref_for: true
    }, _ctx.ptm("content")), [(openBlock(true), createElementBlock(Fragment, null, renderList(value, function(item) {
      return openBlock(), createElementBlock(Fragment, {
        key: item
      }, [item === "FirstPageLink" ? (openBlock(), createBlock(_component_FirstPageLink, {
        key: 0,
        "aria-label": $options.getAriaLabel("firstPageLabel"),
        template: _ctx.$slots.firsticon || _ctx.$slots.firstpagelinkicon,
        onClick: _cache[0] || (_cache[0] = function($event) {
          return $options.changePageToFirst($event);
        }),
        disabled: $options.isFirstPage || $options.empty,
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["aria-label", "template", "disabled", "unstyled", "pt"])) : item === "PrevPageLink" ? (openBlock(), createBlock(_component_PrevPageLink, {
        key: 1,
        "aria-label": $options.getAriaLabel("prevPageLabel"),
        template: _ctx.$slots.previcon || _ctx.$slots.prevpagelinkicon,
        onClick: _cache[1] || (_cache[1] = function($event) {
          return $options.changePageToPrev($event);
        }),
        disabled: $options.isFirstPage || $options.empty,
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["aria-label", "template", "disabled", "unstyled", "pt"])) : item === "NextPageLink" ? (openBlock(), createBlock(_component_NextPageLink, {
        key: 2,
        "aria-label": $options.getAriaLabel("nextPageLabel"),
        template: _ctx.$slots.nexticon || _ctx.$slots.nextpagelinkicon,
        onClick: _cache[2] || (_cache[2] = function($event) {
          return $options.changePageToNext($event);
        }),
        disabled: $options.isLastPage || $options.empty,
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["aria-label", "template", "disabled", "unstyled", "pt"])) : item === "LastPageLink" ? (openBlock(), createBlock(_component_LastPageLink, {
        key: 3,
        "aria-label": $options.getAriaLabel("lastPageLabel"),
        template: _ctx.$slots.lasticon || _ctx.$slots.lastpagelinkicon,
        onClick: _cache[3] || (_cache[3] = function($event) {
          return $options.changePageToLast($event);
        }),
        disabled: $options.isLastPage || $options.empty,
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["aria-label", "template", "disabled", "unstyled", "pt"])) : item === "PageLinks" ? (openBlock(), createBlock(_component_PageLinks, {
        key: 4,
        "aria-label": $options.getAriaLabel("pageLabel"),
        value: $options.pageLinks,
        page: $options.page,
        onClick: _cache[4] || (_cache[4] = function($event) {
          return $options.changePageLink($event);
        }),
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["aria-label", "value", "page", "unstyled", "pt"])) : item === "CurrentPageReport" ? (openBlock(), createBlock(_component_CurrentPageReport, {
        key: 5,
        "aria-live": "polite",
        template: _ctx.currentPageReportTemplate,
        currentPage: $options.currentPage,
        page: $options.page,
        pageCount: $options.pageCount,
        first: $data.d_first,
        rows: $data.d_rows,
        totalRecords: _ctx.totalRecords,
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["template", "currentPage", "page", "pageCount", "first", "rows", "totalRecords", "unstyled", "pt"])) : item === "RowsPerPageDropdown" && _ctx.rowsPerPageOptions ? (openBlock(), createBlock(_component_RowsPerPageDropdown, {
        key: 6,
        "aria-label": $options.getAriaLabel("rowsPerPageLabel"),
        rows: $data.d_rows,
        options: _ctx.rowsPerPageOptions,
        onRowsChange: _cache[5] || (_cache[5] = function($event) {
          return $options.onRowChange($event);
        }),
        disabled: $options.empty,
        templates: _ctx.$slots,
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["aria-label", "rows", "options", "disabled", "templates", "unstyled", "pt"])) : item === "JumpToPageDropdown" ? (openBlock(), createBlock(_component_JumpToPageDropdown, {
        key: 7,
        "aria-label": $options.getAriaLabel("jumpToPageDropdownLabel"),
        page: $options.page,
        pageCount: $options.pageCount,
        onPageChange: _cache[6] || (_cache[6] = function($event) {
          return $options.changePage($event);
        }),
        disabled: $options.empty,
        templates: _ctx.$slots,
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["aria-label", "page", "pageCount", "disabled", "templates", "unstyled", "pt"])) : item === "JumpToPageInput" ? (openBlock(), createBlock(_component_JumpToPageInput, {
        key: 8,
        page: $options.currentPage,
        onPageChange: _cache[7] || (_cache[7] = function($event) {
          return $options.changePage($event);
        }),
        disabled: $options.empty,
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["page", "disabled", "unstyled", "pt"])) : createCommentVNode("", true)], 64);
    }), 128))], 16), _ctx.$slots.end ? (openBlock(), createElementBlock("div", mergeProps({
      key: 1,
      "class": _ctx.cx("contentEnd"),
      ref_for: true
    }, _ctx.ptm("contentEnd")), [renderSlot(_ctx.$slots, "end", {
      state: $options.currentState
    })], 16)) : createCommentVNode("", true)], 16);
  }), 128))], 16)) : createCommentVNode("", true);
}
__name(render$k, "render$k");
script$l.render = render$k;
var theme$1 = /* @__PURE__ */ __name(function theme2(_ref) {
  var dt = _ref.dt;
  return "\n.p-datatable {\n    position: relative;\n}\n\n.p-datatable-table {\n    border-spacing: 0;\n    width: 100%;\n}\n\n.p-datatable-scrollable > .p-datatable-table-container {\n    position: relative;\n}\n\n.p-datatable-scrollable-table > .p-datatable-thead {\n    top: 0;\n    z-index: 1;\n}\n\n.p-datatable-scrollable-table > .p-datatable-frozen-tbody {\n    position: sticky;\n    z-index: 1;\n}\n\n.p-datatable-scrollable-table>.p-datatable-tfoot {\n    bottom: 0;\n    z-index: 1;\n}\n\n.p-datatable-scrollable .p-datatable-frozen-column {\n    position: sticky;\n    background: ".concat(dt("datatable.header.cell.background"), ";\n}\n\n.p-datatable-scrollable th.p-datatable-frozen-column {\n    z-index: 1;\n}\n\n.p-datatable-scrollable > .p-datatable-table-container > .p-datatable-table > .p-datatable-thead,\n.p-datatable-scrollable > .p-datatable-table-container > .p-virtualscroller > .p-datatable-table > .p-datatable-thead {\n    background: ").concat(dt("datatable.header.cell.background"), ";\n}\n\n.p-datatable-scrollable > .p-datatable-table-container > .p-datatable-table > .p-datatable-tfoot,\n.p-datatable-scrollable > .p-datatable-table-container > .p-virtualscroller > .p-datatable-table > .p-datatable-tfoot {\n    background: ").concat(dt("datatable.footer.cell.background"), ";\n}\n\n.p-datatable-flex-scrollable {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n}\n\n.p-datatable-flex-scrollable > .p-datatable-table-container {\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n    height: 100%;\n}\n\n.p-datatable-scrollable-table > .p-datatable-tbody > .p-datatable-row-group-header {\n    position: sticky;\n    z-index: 1;\n}\n\n.p-datatable-resizable-table > .p-datatable-thead > tr > th,\n.p-datatable-resizable-table > .p-datatable-tfoot > tr > td,\n.p-datatable-resizable-table > .p-datatable-tbody > tr > td {\n    overflow: hidden;\n    white-space: nowrap;\n}\n\n.p-datatable-resizable-table > .p-datatable-thead > tr > th.p-datatable-resizable-column:not(.p-datatable-frozen-column) {\n    background-clip: padding-box;\n    position: relative;\n}\n\n.p-datatable-resizable-table-fit > .p-datatable-thead > tr > th.p-datatable-resizable-column:last-child .p-datatable-column-resizer {\n    display: none;\n}\n\n.p-datatable-column-resizer {\n    display: block;\n    position: absolute;\n    top: 0;\n    right: 0;\n    margin: 0;\n    width: ").concat(dt("datatable.column.resizer.width"), ";\n    height: 100%;\n    padding: 0px;\n    cursor: col-resize;\n    border: 1px solid transparent;\n}\n\n.p-datatable-column-header-content {\n    display: flex;\n    align-items: center;\n    gap: ").concat(dt("datatable.header.cell.gap"), ";\n}\n\n.p-datatable-column-resize-indicator {\n    width: ").concat(dt("datatable.resize.indicator.width"), ";\n    position: absolute;\n    z-index: 10;\n    display: none;\n    background: ").concat(dt("datatable.resize.indicator.color"), ";\n}\n\n.p-datatable-row-reorder-indicator-up,\n.p-datatable-row-reorder-indicator-down {\n    position: absolute;\n    display: none;\n}\n\n.p-datatable-reorderable-column,\n.p-datatable-reorderable-row-handle {\n    cursor: move;\n}\n\n.p-datatable-mask {\n    position: absolute;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    z-index: 2;\n}\n\n.p-datatable-inline-filter {\n    display: flex;\n    align-items: center;\n    width: 100%;\n    gap: ").concat(dt("datatable.filter.inline.gap"), ";\n}\n\n.p-datatable-inline-filter .p-datatable-filter-element-container {\n    flex: 1 1 auto;\n    width: 1%;\n}\n\n.p-datatable-filter-overlay {\n    background: ").concat(dt("datatable.filter.overlay.select.background"), ";\n    color: ").concat(dt("datatable.filter.overlay.select.color"), ";\n    border: 1px solid ").concat(dt("datatable.filter.overlay.select.border.color"), ";\n    border-radius: ").concat(dt("datatable.filter.overlay.select.border.radius"), ";\n    box-shadow: ").concat(dt("datatable.filter.overlay.select.shadow"), ";\n    min-width: 12.5rem;\n}\n\n.p-datatable-filter-constraint-list {\n    margin: 0;\n    list-style: none;\n    display: flex;\n    flex-direction: column;\n    padding: ").concat(dt("datatable.filter.constraint.list.padding"), ";\n    gap: ").concat(dt("datatable.filter.constraint.list.gap"), ";\n}\n\n.p-datatable-filter-constraint {\n    padding: ").concat(dt("datatable.filter.constraint.padding"), ";\n    color: ").concat(dt("datatable.filter.constraint.color"), ";\n    border-radius: ").concat(dt("datatable.filter.constraint.border.radius"), ";\n    cursor: pointer;\n    transition: background ").concat(dt("datatable.transition.duration"), ", color ").concat(dt("datatable.transition.duration"), ", border-color ").concat(dt("datatable.transition.duration"), ",\n        box-shadow ").concat(dt("datatable.transition.duration"), ";\n}\n\n.p-datatable-filter-constraint-selected {\n    background: ").concat(dt("datatable.filter.constraint.selected.background"), ";\n    color: ").concat(dt("datatable.filter.constraint.selected.color"), ";\n}\n\n.p-datatable-filter-constraint:not(.p-datatable-filter-constraint-selected):not(.p-disabled):hover {\n    background: ").concat(dt("datatable.filter.constraint.focus.background"), ";\n    color: ").concat(dt("datatable.filter.constraint.focus.color"), ";\n}\n\n.p-datatable-filter-constraint:focus-visible {\n    outline: 0 none;\n    background: ").concat(dt("datatable.filter.constraint.focus.background"), ";\n    color: ").concat(dt("datatable.filter.constraint.focus.color"), ";\n}\n\n.p-datatable-filter-constraint-selected:focus-visible {\n    outline: 0 none;\n    background: ").concat(dt("datatable.filter.constraint.selected.focus.background"), ";\n    color: ").concat(dt("datatable.filter.constraint.selected.focus.color"), ";\n}\n\n.p-datatable-filter-constraint-separator {\n    border-top: 1px solid ").concat(dt("datatable.filter.constraint.separator.border.color"), ";\n}\n\n.p-datatable-popover-filter {\n    display: inline-flex;\n    margin-left: auto;\n}\n\n.p-datatable-filter-overlay-popover {\n    background: ").concat(dt("datatable.filter.overlay.popover.background"), ";\n    color: ").concat(dt("datatable.filter.overlay.popover.color"), ";\n    border: 1px solid ").concat(dt("datatable.filter.overlay.popover.border.color"), ";\n    border-radius: ").concat(dt("datatable.filter.overlay.popover.border.radius"), ";\n    box-shadow: ").concat(dt("datatable.filter.overlay.popover.shadow"), ";\n    min-width: 12.5rem;\n    padding: ").concat(dt("datatable.filter.overlay.popover.padding"), ";\n    display: flex;\n    flex-direction: column;\n    gap: ").concat(dt("datatable.filter.overlay.popover.gap"), ";\n}\n\n.p-datatable-filter-operator-dropdown {\n    width: 100%;\n}\n\n.p-datatable-filter-rule-list,\n.p-datatable-filter-rule {\n    display: flex;\n    flex-direction: column;\n    gap: ").concat(dt("datatable.filter.overlay.popover.gap"), ";\n}\n\n.p-datatable-filter-rule {\n    border-bottom: 1px solid ").concat(dt("datatable.filter.rule.border.color"), ";\n}\n\n.p-datatable-filter-rule:last-child {\n    border-bottom: 0 none;\n}\n\n.p-datatable-filter-add-rule-button {\n    width: 100%;\n}\n\n.p-datatable-filter-remove-button {\n    width: 100%;\n}\n\n.p-datatable-filter-buttonbar {\n    padding: 0;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n}\n\n.p-datatable-virtualscroller-spacer {\n    display: flex;\n}\n\n.p-datatable .p-virtualscroller .p-virtualscroller-loading {\n    transform: none !important;\n    min-height: 0;\n    position: sticky;\n    top: 0;\n    left: 0;\n}\n\n.p-datatable-paginator-top {\n    border-color: ").concat(dt("datatable.paginator.top.border.color"), ";\n    border-style: solid;\n    border-width: ").concat(dt("datatable.paginator.top.border.width"), ";\n}\n\n.p-datatable-paginator-bottom {\n    border-color: ").concat(dt("datatable.paginator.bottom.border.color"), ";\n    border-style: solid;\n    border-width: ").concat(dt("datatable.paginator.bottom.border.width"), ";\n}\n\n.p-datatable-header {\n    background: ").concat(dt("datatable.header.background"), ";\n    color: ").concat(dt("datatable.header.color"), ";\n    border-color: ").concat(dt("datatable.header.border.color"), ";\n    border-style: solid;\n    border-width: ").concat(dt("datatable.header.border.width"), ";\n    padding: ").concat(dt("datatable.header.padding"), ";\n}\n\n.p-datatable-footer {\n    background: ").concat(dt("datatable.footer.background"), ";\n    color: ").concat(dt("datatable.footer.color"), ";\n    border-color: ").concat(dt("datatable.footer.border.color"), ";\n    border-style: solid;\n    border-width: ").concat(dt("datatable.footer.border.width"), ";\n    padding: ").concat(dt("datatable.footer.padding"), ";\n}\n\n.p-datatable-header-cell {\n    padding: ").concat(dt("datatable.header.cell.padding"), ";\n    background: ").concat(dt("datatable.header.cell.background"), ";\n    border-color: ").concat(dt("datatable.header.cell.border.color"), ";\n    border-style: solid;\n    border-width: 0 0 1px 0;\n    color: ").concat(dt("datatable.header.cell.color"), ";\n    font-weight: normal;\n    text-align: left;\n    transition: background ").concat(dt("datatable.transition.duration"), ", color ").concat(dt("datatable.transition.duration"), ", border-color ").concat(dt("datatable.transition.duration"), ",\n            outline-color ").concat(dt("datatable.transition.duration"), ", box-shadow ").concat(dt("datatable.transition.duration"), ";\n}\n\n.p-datatable-column-title {\n    font-weight: ").concat(dt("datatable.column.title.font.weight"), ";\n}\n\n.p-datatable-tbody > tr {\n    outline-color: transparent;\n    background: ").concat(dt("datatable.row.background"), ";\n    color: ").concat(dt("datatable.row.color"), ";\n    transition: background ").concat(dt("datatable.transition.duration"), ", color ").concat(dt("datatable.transition.duration"), ", border-color ").concat(dt("datatable.transition.duration"), ",\n            outline-color ").concat(dt("datatable.transition.duration"), ", box-shadow ").concat(dt("datatable.transition.duration"), ";\n}\n\n.p-datatable-tbody > tr > td {\n    text-align: left;\n    border-color: ").concat(dt("datatable.body.cell.border.color"), ";\n    border-style: solid;\n    border-width: 0 0 1px 0;\n    padding: ").concat(dt("datatable.body.cell.padding"), ";\n}\n\n.p-datatable-hoverable .p-datatable-tbody > tr:not(.p-datatable-row-selected):hover {\n    background: ").concat(dt("datatable.row.hover.background"), ";\n    color: ").concat(dt("datatable.row.hover.color"), ";\n}\n\n.p-datatable-tbody > tr.p-datatable-row-selected {\n    background: ").concat(dt("datatable.row.selected.background"), ";\n    color: ").concat(dt("datatable.row.selected.color"), ";\n}\n\n.p-datatable-tbody > tr:has(+ .p-datatable-row-selected) > td {\n    border-bottom-color: ").concat(dt("datatable.body.cell.selected.border.color"), ";\n}\n\n.p-datatable-tbody > tr.p-datatable-row-selected > td {\n    border-bottom-color: ").concat(dt("datatable.body.cell.selected.border.color"), ";\n}\n\n.p-datatable-tbody > tr:focus-visible,\n.p-datatable-tbody > tr.p-datatable-contextmenu-row-selected {\n    box-shadow: ").concat(dt("datatable.body.cell.focus.ring.shadow"), ";\n    outline: ").concat(dt("datatable.body.cell.focus.ring.width"), " ").concat(dt("datatable.body.cell.focus.ring.style"), " ").concat(dt("datatable.body.cell.focus.ring.color"), ";\n    outline-offset: ").concat(dt("datatable.body.cell.focus.ring.offset"), ";\n}\n\n.p-datatable-tfoot > tr > td {\n    text-align: left;\n    padding: ").concat(dt("datatable.footer.cell.padding"), ";\n    border-color: ").concat(dt("datatable.footer.cell.border.color"), ";\n    border-style: solid;\n    border-width: 0 0 1px 0;\n    color: ").concat(dt("datatable.footer.cell.color"), ";\n    background: ").concat(dt("datatable.footer.cell.background"), ";\n}\n\n.p-datatable-column-footer {\n    font-weight: ").concat(dt("datatable.column.footer.font.weight"), ";\n}\n\n.p-datatable-sortable-column {\n    cursor: pointer;\n    user-select: none;\n    outline-color: transparent;\n}\n\n.p-datatable-column-title,\n.p-datatable-sort-icon,\n.p-datatable-sort-badge {\n    vertical-align: middle;\n}\n\n.p-datatable-sort-icon {\n    color: ").concat(dt("datatable.sort.icon.color"), ";\n    transition: color ").concat(dt("datatable.transition.duration"), ";\n}\n\n.p-datatable-sortable-column:not(.p-datatable-column-sorted):hover {\n    background: ").concat(dt("datatable.header.cell.hover.background"), ";\n    color: ").concat(dt("datatable.header.cell.hover.color"), ";\n}\n\n.p-datatable-sortable-column:not(.p-datatable-column-sorted):hover .p-datatable-sort-icon {\n    color: ").concat(dt("datatable.sort.icon.hover.color"), ";\n}\n\n.p-datatable-column-sorted {\n    background: ").concat(dt("datatable.header.cell.selected.background"), ";\n    color: ").concat(dt("datatable.header.cell.selected.color"), ";\n}\n\n.p-datatable-column-sorted .p-datatable-sort-icon {\n    color: ").concat(dt("datatable.header.cell.selected.color"), ";\n}\n\n.p-datatable-sortable-column:focus-visible {\n    box-shadow: ").concat(dt("datatable.header.cell.focus.ring.shadow"), ";\n    outline: ").concat(dt("datatable.header.cell.focus.ring.width"), " ").concat(dt("datatable.header.cell.focus.ring.style"), " ").concat(dt("datatable.header.cell.focus.ring.color"), ";\n    outline-offset: ").concat(dt("datatable.header.cell.focus.ring.offset"), ";\n}\n\n.p-datatable-hoverable .p-datatable-selectable-row {\n    cursor: pointer;\n}\n\n.p-datatable-tbody > tr.p-datatable-dragpoint-top > td {\n    box-shadow: inset 0 2px 0 0 ").concat(dt("datatable.drop.point.color"), ";\n}\n\n.p-datatable-tbody > tr.p-datatable-dragpoint-bottom > td {\n    box-shadow: inset 0 -2px 0 0 ").concat(dt("datatable.drop.point.color"), ";\n}\n\n.p-datatable-loading-icon {\n    font-size: ").concat(dt("datatable.loading.icon.size"), ";\n    width: ").concat(dt("datatable.loading.icon.size"), ";\n    height: ").concat(dt("datatable.loading.icon.size"), ";\n}\n\n.p-datatable-gridlines .p-datatable-header {\n    border-width: 1px 1px 0 1px;\n}\n\n.p-datatable-gridlines .p-datatable-footer {\n    border-width: 0 1px 1px 1px;\n}\n\n.p-datatable-gridlines .p-datatable-paginator-top {\n    border-width: 1px 1px 0 1px;\n}\n\n.p-datatable-gridlines .p-datatable-paginator-bottom {\n    border-width: 0 1px 1px 1px;\n}\n\n.p-datatable-gridlines .p-datatable-thead > tr > th {\n    border-width: 1px 0 1px 1px;\n}\n\n.p-datatable-gridlines .p-datatable-thead > tr > th:last-child {\n    border-width: 1px;\n}\n\n.p-datatable-gridlines .p-datatable-tbody > tr > td {\n    border-width: 1px 0 0 1px;\n}\n\n.p-datatable-gridlines .p-datatable-tbody > tr > td:last-child {\n    border-width: 1px 1px 0 1px;\n}\n\np-datatable-gridlines .p-datatable-tbody > tr:last-child > td {\n    border-width: 1px 0 1px 1px;\n}\n\n.p-datatable-gridlines .p-datatable-tbody > tr:last-child > td:last-child {\n    border-width: 1px;\n}\n\n.p-datatable-gridlines .p-datatable-tfoot > tr > td {\n    border-width: 1px 0 1px 1px;\n}\n\n.p-datatable-gridlines .p-datatable-tfoot > tr > td:last-child {\n    border-width: 1px 1px 1px 1px;\n}\n\n.p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td {\n    border-width: 0 0 1px 1px;\n}\n\n.p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td:last-child {\n    border-width: 0 1px 1px 1px;\n}\n\n.p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td {\n    border-width: 0 0 1px 1px;\n}\n\n.p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td:last-child {\n    border-width: 0 1px 1px 1px;\n}\n\n.p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td {\n    border-width: 0 0 0 1px;\n}\n\n.p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td:last-child {\n    border-width: 0 1px 0 1px;\n}\n\n.p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd {\n    background: ").concat(dt("datatable.row.striped.background"), ";\n}\n\n.p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd.p-datatable-row-selected {\n    background: ").concat(dt("datatable.row.selected.background"), ";\n    color: ").concat(dt("datatable.row.selected.color"), ";\n}\n\n.p-datatable.p-datatable-sm .p-datatable-header {\n    padding: 0.375rem 0.5rem;\n}\n\n.p-datatable.p-datatable-sm .p-datatable-thead > tr > th {\n    padding: 0.375rem 0.5rem;\n}\n\n.p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {\n    padding: 0.375rem 0.5rem;\n}\n\n.p-datatable.p-datatable-sm .p-datatable-tfoot > tr > td {\n    padding: 0.375rem 0.5rem;\n}\n\n.p-datatable.p-datatable-sm .p-datatable-footer {\n    padding: 0.375rem 0.5rem;\n}\n\n.p-datatable.p-datatable-lg .p-datatable-header {\n    padding: 0.9375rem 1.25rem;\n}\n\n.p-datatable.p-datatable-lg .p-datatable-thead > tr > th {\n    padding: 0.9375rem 1.25rem;\n}\n\n.p-datatable.p-datatable-lg .p-datatable-tbody>tr>td {\n    padding: 0.9375rem 1.25rem;\n}\n\n.p-datatable.p-datatable-lg .p-datatable-tfoot>tr>td {\n    padding: 0.9375rem 1.25rem;\n}\n\n.p-datatable.p-datatable-lg .p-datatable-footer {\n    padding: 0.9375rem 1.25rem;\n}\n\n.p-datatable-row-toggle-button {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    overflow: hidden;\n    position: relative;\n    width: ").concat(dt("datatable.row.toggle.button.size"), ";\n    height: ").concat(dt("datatable.row.toggle.button.size"), ";\n    color: ").concat(dt("datatable.row.toggle.button.color"), ";\n    border: 0 none;\n    background: transparent;\n    cursor: pointer;\n    border-radius: ").concat(dt("datatable.row.toggle.button.border.radius"), ";\n    transition: background ").concat(dt("datatable.transition.duration"), ", color ").concat(dt("datatable.transition.duration"), ", border-color ").concat(dt("datatable.transition.duration"), ",\n            outline-color ").concat(dt("datatable.transition.duration"), ", box-shadow ").concat(dt("datatable.transition.duration"), ";\n    outline-color: transparent;\n    user-select: none;\n}\n\n.p-datatable-row-toggle-button:enabled:hover {\n    color: ").concat(dt("datatable.row.toggle.button.hover.color"), ";\n    background: ").concat(dt("datatable.row.toggle.button.hover.background"), ";\n}\n\n.p-datatable-tbody > tr.p-datatable-row-selected .p-datatable-row-toggle-button:hover {\n    background: ").concat(dt("datatable.row.toggle.button.selected.hover.background"), ";\n    ").concat(dt("datatable.row.toggle.button.selected.hover.color"), ";\n}\n\n.p-datatable-row-toggle-button:focus-visible {\n    box-shadow: ").concat(dt("datatable.row.toggle.button.focus.ring.shadow"), ";\n    outline: ").concat(dt("datatable.row.toggle.button.focus.ring.width"), " ").concat(dt("datatable.row.toggle.button.focus.ring.style"), " ").concat(dt("datatable.row.toggle.button.focus.ring.color"), ";\n    outline-offset: ").concat(dt("datatable.row.toggle.button.focus.ring.offset"), ";\n}\n");
}, "theme");
var classes$1 = {
  root: /* @__PURE__ */ __name(function root(_ref2) {
    var props = _ref2.props;
    return ["p-datatable p-component", {
      "p-datatable-hoverable": props.rowHover || props.selectionMode,
      "p-datatable-resizable": props.resizableColumns,
      "p-datatable-resizable-fit": props.resizableColumns && props.columnResizeMode === "fit",
      "p-datatable-scrollable": props.scrollable,
      "p-datatable-flex-scrollable": props.scrollable && props.scrollHeight === "flex",
      "p-datatable-striped": props.stripedRows,
      "p-datatable-gridlines": props.showGridlines,
      "p-datatable-sm": props.size === "small",
      "p-datatable-lg": props.size === "large"
    }];
  }, "root"),
  mask: "p-datatable-mask p-overlay-mask",
  loadingIcon: "p-datatable-loading-icon",
  header: "p-datatable-header",
  pcPaginator: /* @__PURE__ */ __name(function pcPaginator(_ref3) {
    var position = _ref3.position;
    return "p-datatable-paginator-" + position;
  }, "pcPaginator"),
  tableContainer: "p-datatable-table-container",
  table: /* @__PURE__ */ __name(function table(_ref4) {
    var props = _ref4.props;
    return ["p-datatable-table", {
      "p-datatable-scrollable-table": props.scrollable,
      "p-datatable-resizable-table": props.resizableColumns,
      "p-datatable-resizable-table-fit": props.resizableColumns && props.columnResizeMode === "fit"
    }];
  }, "table"),
  thead: "p-datatable-thead",
  headerCell: /* @__PURE__ */ __name(function headerCell(_ref5) {
    var instance = _ref5.instance, props = _ref5.props, column = _ref5.column;
    return column && !instance.columnProp(column, "hidden") && (props.rowGroupMode !== "subheader" || props.groupRowsBy !== instance.columnProp(column, "field")) ? ["p-datatable-header-cell", {
      "p-datatable-frozen-column": instance.columnProp(column, "frozen")
    }] : ["p-datatable-header-cell", {
      "p-datatable-sortable-column": instance.columnProp("sortable"),
      "p-datatable-resizable-column": instance.resizableColumns,
      "p-datatable-column-sorted": instance.isColumnSorted(),
      "p-datatable-frozen-column": instance.columnProp("frozen"),
      "p-datatable-reorderable-column": props.reorderableColumns
    }];
  }, "headerCell"),
  columnResizer: "p-datatable-column-resizer",
  columnHeaderContent: "p-datatable-column-header-content",
  columnTitle: "p-datatable-column-title",
  columnFooter: "p-datatable-column-footer",
  sortIcon: "p-datatable-sort-icon",
  pcSortBadge: "p-datatable-sort-badge",
  filter: /* @__PURE__ */ __name(function filter(_ref6) {
    var props = _ref6.props;
    return ["p-datatable-filter", {
      "p-datatable-inline-filter": props.display === "row",
      "p-datatable-popover-filter": props.display === "menu"
    }];
  }, "filter"),
  filterElementContainer: "p-datatable-filter-element-container",
  pcColumnFilterButton: "p-datatable-column-filter-button",
  pcColumnFilterClearButton: "p-datatable-column-filter-clear-button",
  filterOverlay: /* @__PURE__ */ __name(function filterOverlay(_ref7) {
    _ref7.instance;
    var props = _ref7.props;
    return ["p-datatable-filter-overlay p-component", {
      "p-datatable-filter-overlay-popover": props.display === "menu"
    }];
  }, "filterOverlay"),
  filterConstraintList: "p-datatable-filter-constraint-list",
  filterConstraint: /* @__PURE__ */ __name(function filterConstraint(_ref8) {
    var instance = _ref8.instance, matchMode = _ref8.matchMode;
    return ["p-datatable-filter-constraint", {
      "p-datatable-filter-constraint-selected": matchMode && instance.isRowMatchModeSelected(matchMode.value)
    }];
  }, "filterConstraint"),
  filterConstraintSeparator: "p-datatable-filter-constraint-separator",
  filterOperator: "p-datatable-filter-operator",
  pcFilterOperatorDropdown: "p-datatable-filter-operator-dropdown",
  filterRuleList: "p-datatable-filter-rule-list",
  filterRule: "p-datatable-filter-rule",
  pcFilterConstraintDropdown: "p-datatable-filter-constraint-dropdown",
  pcFilterRemoveRuleButton: "p-datatable-filter-remove-rule-button",
  pcFilterAddRuleButton: "p-datatable-filter-add-rule-button",
  filterButtonbar: "p-datatable-filter-buttonbar",
  pcFilterClearButton: "p-datatable-filter-clear-button",
  pcFilterApplyButton: "p-datatable-filter-apply-button",
  tbody: /* @__PURE__ */ __name(function tbody(_ref9) {
    var props = _ref9.props;
    return props.frozenRow ? "p-datatable-tbody p-datatable-frozen-tbody" : "p-datatable-tbody";
  }, "tbody"),
  rowGroupHeader: "p-datatable-row-group-header",
  rowToggleButton: "p-datatable-row-toggle-button",
  rowToggleIcon: "p-datatable-row-toggle-icon",
  row: /* @__PURE__ */ __name(function row(_ref10) {
    var instance = _ref10.instance, props = _ref10.props, index = _ref10.index, columnSelectionMode = _ref10.columnSelectionMode;
    var rowStyleClass = [];
    if (props.selectionMode) {
      rowStyleClass.push("p-datatable-selectable-row");
    }
    if (props.selection) {
      rowStyleClass.push({
        "p-datatable-row-selected": columnSelectionMode ? instance.isSelected && instance.$parentInstance.$parentInstance.highlightOnSelect : instance.isSelected
      });
    }
    if (props.contextMenuSelection) {
      rowStyleClass.push({
        "p-datatable-contextmenu-row-selected": instance.isSelectedWithContextMenu
      });
    }
    rowStyleClass.push(index % 2 === 0 ? "p-row-even" : "p-row-odd");
    return rowStyleClass;
  }, "row"),
  rowExpansion: "p-datatable-row-expansion",
  rowGroupFooter: "p-datatable-row-group-footer",
  emptyMessage: "p-datatable-empty-message",
  bodyCell: /* @__PURE__ */ __name(function bodyCell(_ref11) {
    var instance = _ref11.instance;
    return [{
      "p-datatable-frozen-column": instance.columnProp("frozen")
    }];
  }, "bodyCell"),
  reorderableRowHandle: "p-datatable-reorderable-row-handle",
  pcRowEditorInit: "p-datatable-row-editor-init",
  pcRowEditorSave: "p-datatable-row-editor-save",
  pcRowEditorCancel: "p-datatable-row-editor-cancel",
  tfoot: "p-datatable-tfoot",
  footerCell: /* @__PURE__ */ __name(function footerCell(_ref12) {
    var instance = _ref12.instance;
    return [{
      "p-datatable-frozen-column": instance.columnProp("frozen")
    }];
  }, "footerCell"),
  virtualScrollerSpacer: "p-datatable-virtualscroller-spacer",
  footer: "p-datatable-footer",
  columnResizeIndicator: "p-datatable-column-resize-indicator",
  rowReorderIndicatorUp: "p-datatable-row-reorder-indicator-up",
  rowReorderIndicatorDown: "p-datatable-row-reorder-indicator-down"
};
var inlineStyles = {
  tableContainer: {
    overflow: "auto"
  },
  thead: {
    position: "sticky"
  },
  tfoot: {
    position: "sticky"
  }
};
var DataTableStyle = BaseStyle.extend({
  name: "datatable",
  theme: theme$1,
  classes: classes$1,
  inlineStyles
});
var script$k = {
  name: "PencilIcon",
  "extends": script$t
};
var _hoisted_1$c = /* @__PURE__ */ createBaseVNode("path", {
  d: "M0.609628 13.959C0.530658 13.9599 0.452305 13.9451 0.379077 13.9156C0.305849 13.8861 0.239191 13.8424 0.18294 13.787C0.118447 13.7234 0.0688234 13.6464 0.0376166 13.5614C0.00640987 13.4765 -0.00560954 13.3857 0.00241768 13.2956L0.25679 10.1501C0.267698 10.0041 0.331934 9.86709 0.437312 9.76516L9.51265 0.705715C10.0183 0.233014 10.6911 -0.0203041 11.3835 0.00127367C12.0714 0.00660201 12.7315 0.27311 13.2298 0.746671C13.7076 1.23651 13.9824 1.88848 13.9992 2.57201C14.0159 3.25554 13.7733 3.92015 13.32 4.4327L4.23648 13.5331C4.13482 13.6342 4.0017 13.6978 3.85903 13.7133L0.667067 14L0.609628 13.959ZM1.43018 10.4696L1.25787 12.714L3.50619 12.5092L12.4502 3.56444C12.6246 3.35841 12.7361 3.10674 12.7714 2.83933C12.8067 2.57193 12.7644 2.30002 12.6495 2.05591C12.5346 1.8118 12.3519 1.60575 12.1231 1.46224C11.8943 1.31873 11.6291 1.2438 11.3589 1.24633C11.1813 1.23508 11.0033 1.25975 10.8355 1.31887C10.6677 1.37798 10.5136 1.47033 10.3824 1.59036L1.43018 10.4696Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$a = [_hoisted_1$c];
function render$j(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_2$a, 16);
}
__name(render$j, "render$j");
script$k.render = render$j;
var theme3 = /* @__PURE__ */ __name(function theme4(_ref) {
  var dt = _ref.dt;
  return "\n.p-radiobutton {\n    position: relative;\n    display: inline-flex;\n    user-select: none;\n    vertical-align: bottom;\n    width: ".concat(dt("radiobutton.width"), ";\n    height: ").concat(dt("radiobutton.height"), ";\n}\n\n.p-radiobutton-input {\n    cursor: pointer;\n    appearance: none;\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    padding: 0;\n    margin: 0;\n    opacity: 0;\n    z-index: 1;\n    outline: 0 none;\n    border: 1px solid transparent;\n    border-radius: 50%;\n}\n\n.p-radiobutton-box {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    border-radius: 50%;\n    border: 1px solid ").concat(dt("radiobutton.border.color"), ";\n    background: ").concat(dt("radiobutton.background"), ";\n    width: ").concat(dt("radiobutton.width"), ";\n    height: ").concat(dt("radiobutton.height"), ";\n    transition: background ").concat(dt("radiobutton.transition.duration"), ", color ").concat(dt("radiobutton.transition.duration"), ", border-color ").concat(dt("radiobutton.transition.duration"), ", box-shadow ").concat(dt("radiobutton.transition.duration"), ", outline-color ").concat(dt("radiobutton.transition.duration"), ";\n    outline-color: transparent;\n    box-shadow: ").concat(dt("radiobutton.shadow"), ";\n}\n\n.p-radiobutton-icon {\n    transition-duration: ").concat(dt("radiobutton.transition.duration"), ";\n    background: transparent;\n    font-size: ").concat(dt("radiobutton.icon.size"), ";\n    width: ").concat(dt("radiobutton.icon.size"), ";\n    height: ").concat(dt("radiobutton.icon.size"), ";\n    border-radius: 50%;\n    backface-visibility: hidden;\n    transform: translateZ(0) scale(0.1);\n}\n\n.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:hover) .p-radiobutton-box {\n    border-color: ").concat(dt("radiobutton.hover.border.color"), ";\n}\n\n.p-radiobutton-checked .p-radiobutton-box {\n    border-color: ").concat(dt("radiobutton.checked.border.color"), ";\n    background: ").concat(dt("radiobutton.checked.background"), ";\n}\n\n.p-radiobutton-checked .p-radiobutton-box .p-radiobutton-icon {\n    background: ").concat(dt("radiobutton.icon.checked.color"), ";\n    transform: translateZ(0) scale(1, 1);\n    visibility: visible;\n}\n\n.p-radiobutton-checked:not(.p-disabled):has(.p-radiobutton-input:hover) .p-radiobutton-box {\n    border-color: ").concat(dt("radiobutton.checked.hover.border.color"), ";\n    background: ").concat(dt("radiobutton.checked.hover.background"), ";\n}\n\n.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:hover).p-radiobutton-checked .p-radiobutton-box .p-radiobutton-icon {\n    background: ").concat(dt("radiobutton.icon.checked.hover.color"), ";\n}\n\n.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:focus-visible) .p-radiobutton-box {\n    border-color: ").concat(dt("radiobutton.focus.border.color"), ";\n    box-shadow: ").concat(dt("radiobutton.focus.ring.shadow"), ";\n    outline: ").concat(dt("radiobutton.focus.ring.width"), " ").concat(dt("radiobutton.focus.ring.style"), " ").concat(dt("radiobutton.focus.ring.color"), ";\n    outline-offset: ").concat(dt("radiobutton.focus.ring.offset"), ";\n}\n\n.p-radiobutton-checked:not(.p-disabled):has(.p-radiobutton-input:focus-visible) .p-radiobutton-box {\n    border-color: ").concat(dt("radiobutton.checked.focus.border.color"), ";\n}\n\n.p-radiobutton.p-invalid > .p-radiobutton-box {\n    border-color: ").concat(dt("radiobutton.invalid.border.color"), ";\n}\n\n.p-radiobutton.p-variant-filled .p-radiobutton-box {\n    background: ").concat(dt("radiobutton.filled.background"), ";\n}\n\n.p-radiobutton.p-variant-filled.p-radiobutton-checked .p-radiobutton-box {\n    background: ").concat(dt("radiobutton.checked.background"), ";\n}\n\n.p-radiobutton.p-variant-filled:not(.p-disabled):has(.p-radiobutton-input:hover).p-radiobutton-checked .p-radiobutton-box {\n    background: ").concat(dt("radiobutton.checked.hover.background"), ";\n}\n\n.p-radiobutton.p-disabled {\n    opacity: 1;\n}\n\n.p-radiobutton.p-disabled .p-radiobutton-box {\n    background: ").concat(dt("radiobutton.disabled.background"), ";\n    border-color: ").concat(dt("radiobutton.checked.disabled.border.color"), ";\n}\n\n.p-radiobutton-checked.p-disabled .p-radiobutton-box .p-radiobutton-icon {\n    background: ").concat(dt("radiobutton.icon.disabled.color"), ";\n}\n");
}, "theme");
var classes = {
  root: /* @__PURE__ */ __name(function root2(_ref2) {
    var instance = _ref2.instance, props = _ref2.props;
    return ["p-radiobutton p-component", {
      "p-radiobutton-checked": instance.checked,
      "p-disabled": props.disabled,
      "p-invalid": props.invalid,
      "p-variant-filled": props.variant ? props.variant === "filled" : instance.$primevue.config.inputStyle === "filled" || instance.$primevue.config.inputVariant === "filled"
    }];
  }, "root"),
  box: "p-radiobutton-box",
  input: "p-radiobutton-input",
  icon: "p-radiobutton-icon"
};
var RadioButtonStyle = BaseStyle.extend({
  name: "radiobutton",
  theme: theme3,
  classes
});
var script$1$1 = {
  name: "BaseRadioButton",
  "extends": script$s,
  props: {
    value: null,
    modelValue: null,
    binary: Boolean,
    name: {
      type: String,
      "default": null
    },
    variant: {
      type: String,
      "default": null
    },
    invalid: {
      type: Boolean,
      "default": false
    },
    disabled: {
      type: Boolean,
      "default": false
    },
    readonly: {
      type: Boolean,
      "default": false
    },
    tabindex: {
      type: Number,
      "default": null
    },
    inputId: {
      type: String,
      "default": null
    },
    inputClass: {
      type: [String, Object],
      "default": null
    },
    inputStyle: {
      type: Object,
      "default": null
    },
    ariaLabelledby: {
      type: String,
      "default": null
    },
    ariaLabel: {
      type: String,
      "default": null
    }
  },
  style: RadioButtonStyle,
  provide: /* @__PURE__ */ __name(function provide3() {
    return {
      $pcRadioButton: this,
      $parentInstance: this
    };
  }, "provide")
};
var script$j = {
  name: "RadioButton",
  "extends": script$1$1,
  inheritAttrs: false,
  emits: ["update:modelValue", "change", "focus", "blur"],
  methods: {
    getPTOptions: /* @__PURE__ */ __name(function getPTOptions6(key) {
      var _ptm = key === "root" ? this.ptmi : this.ptm;
      return _ptm(key, {
        context: {
          checked: this.checked,
          disabled: this.disabled
        }
      });
    }, "getPTOptions"),
    onChange: /* @__PURE__ */ __name(function onChange4(event2) {
      if (!this.disabled && !this.readonly) {
        var newModelValue = this.binary ? !this.checked : this.value;
        this.$emit("update:modelValue", newModelValue);
        this.$emit("change", event2);
      }
    }, "onChange"),
    onFocus: /* @__PURE__ */ __name(function onFocus(event2) {
      this.$emit("focus", event2);
    }, "onFocus"),
    onBlur: /* @__PURE__ */ __name(function onBlur(event2) {
      this.$emit("blur", event2);
    }, "onBlur")
  },
  computed: {
    checked: /* @__PURE__ */ __name(function checked() {
      return this.modelValue != null && (this.binary ? !!this.modelValue : equals(this.modelValue, this.value));
    }, "checked")
  }
};
var _hoisted_1$b = ["data-p-checked", "data-p-disabled"];
var _hoisted_2$9 = ["id", "value", "name", "checked", "tabindex", "disabled", "readonly", "aria-labelledby", "aria-label", "aria-invalid"];
function render$i(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx("root")
  }, $options.getPTOptions("root"), {
    "data-p-checked": $options.checked,
    "data-p-disabled": _ctx.disabled
  }), [createBaseVNode("input", mergeProps({
    id: _ctx.inputId,
    type: "radio",
    "class": [_ctx.cx("input"), _ctx.inputClass],
    style: _ctx.inputStyle,
    value: _ctx.value,
    name: _ctx.name,
    checked: $options.checked,
    tabindex: _ctx.tabindex,
    disabled: _ctx.disabled,
    readonly: _ctx.readonly,
    "aria-labelledby": _ctx.ariaLabelledby,
    "aria-label": _ctx.ariaLabel,
    "aria-invalid": _ctx.invalid || void 0,
    onFocus: _cache[0] || (_cache[0] = function() {
      return $options.onFocus && $options.onFocus.apply($options, arguments);
    }),
    onBlur: _cache[1] || (_cache[1] = function() {
      return $options.onBlur && $options.onBlur.apply($options, arguments);
    }),
    onChange: _cache[2] || (_cache[2] = function() {
      return $options.onChange && $options.onChange.apply($options, arguments);
    })
  }, $options.getPTOptions("input")), null, 16, _hoisted_2$9), createBaseVNode("div", mergeProps({
    "class": _ctx.cx("box")
  }, $options.getPTOptions("box")), [createBaseVNode("div", mergeProps({
    "class": _ctx.cx("icon")
  }, $options.getPTOptions("icon")), null, 16)], 16)], 16, _hoisted_1$b);
}
__name(render$i, "render$i");
script$j.render = render$i;
var script$i = {
  name: "FilterIcon",
  "extends": script$t
};
var _hoisted_1$a = /* @__PURE__ */ createBaseVNode("path", {
  d: "M8.64708 14H5.35296C5.18981 13.9979 5.03395 13.9321 4.91858 13.8167C4.8032 13.7014 4.73745 13.5455 4.73531 13.3824V7L0.329431 0.98C0.259794 0.889466 0.217389 0.780968 0.20718 0.667208C0.19697 0.553448 0.219379 0.439133 0.271783 0.337647C0.324282 0.236453 0.403423 0.151519 0.500663 0.0920138C0.597903 0.0325088 0.709548 0.000692754 0.823548 0H13.1765C13.2905 0.000692754 13.4021 0.0325088 13.4994 0.0920138C13.5966 0.151519 13.6758 0.236453 13.7283 0.337647C13.7807 0.439133 13.8031 0.553448 13.7929 0.667208C13.7826 0.780968 13.7402 0.889466 13.6706 0.98L9.26472 7V13.3824C9.26259 13.5455 9.19683 13.7014 9.08146 13.8167C8.96609 13.9321 8.81022 13.9979 8.64708 14ZM5.97061 12.7647H8.02943V6.79412C8.02878 6.66289 8.07229 6.53527 8.15296 6.43177L11.9412 1.23529H2.05884L5.86355 6.43177C5.94422 6.53527 5.98773 6.66289 5.98708 6.79412L5.97061 12.7647Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$8 = [_hoisted_1$a];
function render$h(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_2$8, 16);
}
__name(render$h, "render$h");
script$i.render = render$h;
var script$h = {
  name: "FilterSlashIcon",
  "extends": script$t
};
var _hoisted_1$9 = /* @__PURE__ */ createBaseVNode("path", {
  "fill-rule": "evenodd",
  "clip-rule": "evenodd",
  d: "M13.4994 0.0920138C13.5967 0.151519 13.6758 0.236453 13.7283 0.337647C13.7807 0.439133 13.8031 0.553448 13.7929 0.667208C13.7827 0.780968 13.7403 0.889466 13.6707 0.98L11.406 4.06823C11.3099 4.19928 11.1656 4.28679 11.005 4.3115C10.8444 4.33621 10.6805 4.2961 10.5495 4.2C10.4184 4.1039 10.3309 3.95967 10.3062 3.79905C10.2815 3.63843 10.3216 3.47458 10.4177 3.34353L11.9412 1.23529H7.41184C7.24803 1.23529 7.09093 1.17022 6.97509 1.05439C6.85926 0.938558 6.79419 0.781457 6.79419 0.617647C6.79419 0.453837 6.85926 0.296736 6.97509 0.180905C7.09093 0.0650733 7.24803 0 7.41184 0H13.1765C13.2905 0.000692754 13.4022 0.0325088 13.4994 0.0920138ZM4.20008 0.181168H4.24126L13.2013 9.03411C13.3169 9.14992 13.3819 9.3069 13.3819 9.47058C13.3819 9.63426 13.3169 9.79124 13.2013 9.90705C13.1445 9.96517 13.0766 10.0112 13.0016 10.0423C12.9266 10.0735 12.846 10.0891 12.7648 10.0882C12.6836 10.0886 12.6032 10.0728 12.5283 10.0417C12.4533 10.0106 12.3853 9.96479 12.3283 9.90705L9.3142 6.92587L9.26479 6.99999V13.3823C9.26265 13.5455 9.19689 13.7014 9.08152 13.8167C8.96615 13.9321 8.81029 13.9979 8.64714 14H5.35302C5.18987 13.9979 5.03401 13.9321 4.91864 13.8167C4.80327 13.7014 4.73751 13.5455 4.73537 13.3823V6.99999L0.329492 1.02117C0.259855 0.930634 0.21745 0.822137 0.207241 0.708376C0.197031 0.594616 0.21944 0.480301 0.271844 0.378815C0.324343 0.277621 0.403484 0.192687 0.500724 0.133182C0.597964 0.073677 0.709609 0.041861 0.823609 0.0411682H3.86243C3.92448 0.0461551 3.9855 0.060022 4.04361 0.0823446C4.10037 0.10735 4.15311 0.140655 4.20008 0.181168ZM8.02949 6.79411C8.02884 6.66289 8.07235 6.53526 8.15302 6.43176L8.42478 6.05293L3.55773 1.23529H2.0589L5.84714 6.43176C5.92781 6.53526 5.97132 6.66289 5.97067 6.79411V12.7647H8.02949V6.79411Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$7 = [_hoisted_1$9];
function render$g(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_2$7, 16);
}
__name(render$g, "render$g");
script$h.render = render$g;
var script$g = {
  name: "TrashIcon",
  "extends": script$t
};
var _hoisted_1$8 = /* @__PURE__ */ createBaseVNode("path", {
  "fill-rule": "evenodd",
  "clip-rule": "evenodd",
  d: "M3.44802 13.9955H10.552C10.8056 14.0129 11.06 13.9797 11.3006 13.898C11.5412 13.8163 11.7632 13.6877 11.9537 13.5196C12.1442 13.3515 12.2995 13.1473 12.4104 12.9188C12.5213 12.6903 12.5858 12.442 12.6 12.1884V4.36041H13.4C13.5591 4.36041 13.7117 4.29722 13.8243 4.18476C13.9368 4.07229 14 3.91976 14 3.76071C14 3.60166 13.9368 3.44912 13.8243 3.33666C13.7117 3.22419 13.5591 3.16101 13.4 3.16101H12.0537C12.0203 3.1557 11.9863 3.15299 11.952 3.15299C11.9178 3.15299 11.8838 3.1557 11.8503 3.16101H11.2285C11.2421 3.10893 11.2487 3.05513 11.248 3.00106V1.80966C11.2171 1.30262 10.9871 0.828306 10.608 0.48989C10.229 0.151475 9.73159 -0.0236625 9.22402 0.00257442H4.77602C4.27251 -0.0171866 3.78126 0.160868 3.40746 0.498617C3.03365 0.836366 2.807 1.30697 2.77602 1.80966V3.00106C2.77602 3.0556 2.78346 3.10936 2.79776 3.16101H0.6C0.521207 3.16101 0.443185 3.17652 0.37039 3.20666C0.297595 3.2368 0.231451 3.28097 0.175736 3.33666C0.120021 3.39235 0.0758251 3.45846 0.0456722 3.53121C0.0155194 3.60397 0 3.68196 0 3.76071C0 3.83946 0.0155194 3.91744 0.0456722 3.9902C0.0758251 4.06296 0.120021 4.12907 0.175736 4.18476C0.231451 4.24045 0.297595 4.28462 0.37039 4.31476C0.443185 4.3449 0.521207 4.36041 0.6 4.36041H1.40002V12.1884C1.41426 12.442 1.47871 12.6903 1.58965 12.9188C1.7006 13.1473 1.85582 13.3515 2.04633 13.5196C2.23683 13.6877 2.45882 13.8163 2.69944 13.898C2.94005 13.9797 3.1945 14.0129 3.44802 13.9955ZM2.60002 4.36041H11.304V12.1884C11.304 12.5163 10.952 12.7961 10.504 12.7961H3.40002C2.97602 12.7961 2.60002 12.5163 2.60002 12.1884V4.36041ZM3.95429 3.16101C3.96859 3.10936 3.97602 3.0556 3.97602 3.00106V1.80966C3.97602 1.48183 4.33602 1.20197 4.77602 1.20197H9.24802C9.66403 1.20197 10.048 1.48183 10.048 1.80966V3.00106C10.0473 3.05515 10.054 3.10896 10.0678 3.16101H3.95429ZM5.57571 10.997C5.41731 10.995 5.26597 10.9311 5.15395 10.8191C5.04193 10.7071 4.97808 10.5558 4.97601 10.3973V6.77517C4.97601 6.61612 5.0392 6.46359 5.15166 6.35112C5.26413 6.23866 5.41666 6.17548 5.57571 6.17548C5.73476 6.17548 5.8873 6.23866 5.99976 6.35112C6.11223 6.46359 6.17541 6.61612 6.17541 6.77517V10.3894C6.17647 10.4688 6.16174 10.5476 6.13208 10.6213C6.10241 10.695 6.05841 10.762 6.00261 10.8186C5.94682 10.8751 5.88035 10.92 5.80707 10.9506C5.73378 10.9813 5.65514 10.9971 5.57571 10.997ZM7.99968 10.8214C8.11215 10.9339 8.26468 10.997 8.42373 10.997C8.58351 10.9949 8.73604 10.93 8.84828 10.8163C8.96052 10.7025 9.02345 10.5491 9.02343 10.3894V6.77517C9.02343 6.61612 8.96025 6.46359 8.84778 6.35112C8.73532 6.23866 8.58278 6.17548 8.42373 6.17548C8.26468 6.17548 8.11215 6.23866 7.99968 6.35112C7.88722 6.46359 7.82404 6.61612 7.82404 6.77517V10.3973C7.82404 10.5564 7.88722 10.7089 7.99968 10.8214Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$6 = [_hoisted_1$8];
function render$f(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_2$6, 16);
}
__name(render$f, "render$f");
script$g.render = render$f;
var script$f = {
  name: "SortAltIcon",
  "extends": script$t
};
var _hoisted_1$7 = /* @__PURE__ */ createBaseVNode("path", {
  d: "M5.64515 3.61291C5.47353 3.61291 5.30192 3.54968 5.16644 3.4142L3.38708 1.63484L1.60773 3.4142C1.34579 3.67613 0.912244 3.67613 0.650309 3.4142C0.388374 3.15226 0.388374 2.71871 0.650309 2.45678L2.90837 0.198712C3.17031 -0.0632236 3.60386 -0.0632236 3.86579 0.198712L6.12386 2.45678C6.38579 2.71871 6.38579 3.15226 6.12386 3.4142C5.98837 3.54968 5.81676 3.61291 5.64515 3.61291Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$5 = /* @__PURE__ */ createBaseVNode("path", {
  d: "M3.38714 14C3.01681 14 2.70972 13.6929 2.70972 13.3226V0.677419C2.70972 0.307097 3.01681 0 3.38714 0C3.75746 0 4.06456 0.307097 4.06456 0.677419V13.3226C4.06456 13.6929 3.75746 14 3.38714 14Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_3$1 = /* @__PURE__ */ createBaseVNode("path", {
  d: "M10.6129 14C10.4413 14 10.2697 13.9368 10.1342 13.8013L7.87611 11.5432C7.61418 11.2813 7.61418 10.8477 7.87611 10.5858C8.13805 10.3239 8.5716 10.3239 8.83353 10.5858L10.6129 12.3652L12.3922 10.5858C12.6542 10.3239 13.0877 10.3239 13.3497 10.5858C13.6116 10.8477 13.6116 11.2813 13.3497 11.5432L11.0916 13.8013C10.9561 13.9368 10.7845 14 10.6129 14Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_4$1 = /* @__PURE__ */ createBaseVNode("path", {
  d: "M10.6129 14C10.2426 14 9.93552 13.6929 9.93552 13.3226V0.677419C9.93552 0.307097 10.2426 0 10.6129 0C10.9833 0 11.2904 0.307097 11.2904 0.677419V13.3226C11.2904 13.6929 10.9832 14 10.6129 14Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_5$1 = [_hoisted_1$7, _hoisted_2$5, _hoisted_3$1, _hoisted_4$1];
function render$e(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_5$1, 16);
}
__name(render$e, "render$e");
script$f.render = render$e;
var script$e = {
  name: "SortAmountDownIcon",
  "extends": script$t
};
var _hoisted_1$6 = /* @__PURE__ */ createBaseVNode("path", {
  d: "M4.93953 10.5858L3.83759 11.6877V0.677419C3.83759 0.307097 3.53049 0 3.16017 0C2.78985 0 2.48275 0.307097 2.48275 0.677419V11.6877L1.38082 10.5858C1.11888 10.3239 0.685331 10.3239 0.423396 10.5858C0.16146 10.8477 0.16146 11.2813 0.423396 11.5432L2.68146 13.8013C2.74469 13.8645 2.81694 13.9097 2.89823 13.9458C2.97952 13.9819 3.06985 14 3.16017 14C3.25049 14 3.33178 13.9819 3.42211 13.9458C3.5034 13.9097 3.57565 13.8645 3.63888 13.8013L5.89694 11.5432C6.15888 11.2813 6.15888 10.8477 5.89694 10.5858C5.63501 10.3239 5.20146 10.3239 4.93953 10.5858ZM13.0957 0H7.22468C6.85436 0 6.54726 0.307097 6.54726 0.677419C6.54726 1.04774 6.85436 1.35484 7.22468 1.35484H13.0957C13.466 1.35484 13.7731 1.04774 13.7731 0.677419C13.7731 0.307097 13.466 0 13.0957 0ZM7.22468 5.41935H9.48275C9.85307 5.41935 10.1602 5.72645 10.1602 6.09677C10.1602 6.4671 9.85307 6.77419 9.48275 6.77419H7.22468C6.85436 6.77419 6.54726 6.4671 6.54726 6.09677C6.54726 5.72645 6.85436 5.41935 7.22468 5.41935ZM7.6763 8.12903H7.22468C6.85436 8.12903 6.54726 8.43613 6.54726 8.80645C6.54726 9.17677 6.85436 9.48387 7.22468 9.48387H7.6763C8.04662 9.48387 8.35372 9.17677 8.35372 8.80645C8.35372 8.43613 8.04662 8.12903 7.6763 8.12903ZM7.22468 2.70968H11.2892C11.6595 2.70968 11.9666 3.01677 11.9666 3.3871C11.9666 3.75742 11.6595 4.06452 11.2892 4.06452H7.22468C6.85436 4.06452 6.54726 3.75742 6.54726 3.3871C6.54726 3.01677 6.85436 2.70968 7.22468 2.70968Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$4 = [_hoisted_1$6];
function render$d(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_2$4, 16);
}
__name(render$d, "render$d");
script$e.render = render$d;
var script$d = {
  name: "SortAmountUpAltIcon",
  "extends": script$t
};
var _hoisted_1$5 = /* @__PURE__ */ createBaseVNode("path", {
  d: "M3.63435 0.19871C3.57113 0.135484 3.49887 0.0903226 3.41758 0.0541935C3.255 -0.0180645 3.06532 -0.0180645 2.90274 0.0541935C2.82145 0.0903226 2.74919 0.135484 2.68597 0.19871L0.427901 2.45677C0.165965 2.71871 0.165965 3.15226 0.427901 3.41419C0.689836 3.67613 1.12338 3.67613 1.38532 3.41419L2.48726 2.31226V13.3226C2.48726 13.6929 2.79435 14 3.16467 14C3.535 14 3.84209 13.6929 3.84209 13.3226V2.31226L4.94403 3.41419C5.07951 3.54968 5.25113 3.6129 5.42274 3.6129C5.59435 3.6129 5.76597 3.54968 5.90145 3.41419C6.16338 3.15226 6.16338 2.71871 5.90145 2.45677L3.64338 0.19871H3.63435ZM13.7685 13.3226C13.7685 12.9523 13.4615 12.6452 13.0911 12.6452H7.22016C6.84984 12.6452 6.54274 12.9523 6.54274 13.3226C6.54274 13.6929 6.84984 14 7.22016 14H13.0911C13.4615 14 13.7685 13.6929 13.7685 13.3226ZM7.22016 8.58064C6.84984 8.58064 6.54274 8.27355 6.54274 7.90323C6.54274 7.5329 6.84984 7.22581 7.22016 7.22581H9.47823C9.84855 7.22581 10.1556 7.5329 10.1556 7.90323C10.1556 8.27355 9.84855 8.58064 9.47823 8.58064H7.22016ZM7.22016 5.87097H7.67177C8.0421 5.87097 8.34919 5.56387 8.34919 5.19355C8.34919 4.82323 8.0421 4.51613 7.67177 4.51613H7.22016C6.84984 4.51613 6.54274 4.82323 6.54274 5.19355C6.54274 5.56387 6.84984 5.87097 7.22016 5.87097ZM11.2847 11.2903H7.22016C6.84984 11.2903 6.54274 10.9832 6.54274 10.6129C6.54274 10.2426 6.84984 9.93548 7.22016 9.93548H11.2847C11.655 9.93548 11.9621 10.2426 11.9621 10.6129C11.9621 10.9832 11.655 11.2903 11.2847 11.2903Z",
  fill: "currentColor"
}, null, -1);
var _hoisted_2$3 = [_hoisted_1$5];
function render$c(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("svg", mergeProps({
    width: "14",
    height: "14",
    viewBox: "0 0 14 14",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, _ctx.pti()), _hoisted_2$3, 16);
}
__name(render$c, "render$c");
script$d.render = render$c;
var script$c = {
  name: "BaseDataTable",
  "extends": script$s,
  props: {
    value: {
      type: Array,
      "default": null
    },
    dataKey: {
      type: [String, Function],
      "default": null
    },
    rows: {
      type: Number,
      "default": 0
    },
    first: {
      type: Number,
      "default": 0
    },
    totalRecords: {
      type: Number,
      "default": 0
    },
    paginator: {
      type: Boolean,
      "default": false
    },
    paginatorPosition: {
      type: String,
      "default": "bottom"
    },
    alwaysShowPaginator: {
      type: Boolean,
      "default": true
    },
    paginatorTemplate: {
      type: [Object, String],
      "default": "FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
    },
    pageLinkSize: {
      type: Number,
      "default": 5
    },
    rowsPerPageOptions: {
      type: Array,
      "default": null
    },
    currentPageReportTemplate: {
      type: String,
      "default": "({currentPage} of {totalPages})"
    },
    lazy: {
      type: Boolean,
      "default": false
    },
    loading: {
      type: Boolean,
      "default": false
    },
    loadingIcon: {
      type: String,
      "default": void 0
    },
    sortField: {
      type: [String, Function],
      "default": null
    },
    sortOrder: {
      type: Number,
      "default": null
    },
    defaultSortOrder: {
      type: Number,
      "default": 1
    },
    nullSortOrder: {
      type: Number,
      "default": 1
    },
    multiSortMeta: {
      type: Array,
      "default": null
    },
    sortMode: {
      type: String,
      "default": "single"
    },
    removableSort: {
      type: Boolean,
      "default": false
    },
    filters: {
      type: Object,
      "default": null
    },
    filterDisplay: {
      type: String,
      "default": null
    },
    globalFilterFields: {
      type: Array,
      "default": null
    },
    filterLocale: {
      type: String,
      "default": void 0
    },
    selection: {
      type: [Array, Object],
      "default": null
    },
    selectionMode: {
      type: String,
      "default": null
    },
    compareSelectionBy: {
      type: String,
      "default": "deepEquals"
    },
    metaKeySelection: {
      type: Boolean,
      "default": false
    },
    contextMenu: {
      type: Boolean,
      "default": false
    },
    contextMenuSelection: {
      type: Object,
      "default": null
    },
    selectAll: {
      type: Boolean,
      "default": null
    },
    rowHover: {
      type: Boolean,
      "default": false
    },
    csvSeparator: {
      type: String,
      "default": ","
    },
    exportFilename: {
      type: String,
      "default": "download"
    },
    exportFunction: {
      type: Function,
      "default": null
    },
    resizableColumns: {
      type: Boolean,
      "default": false
    },
    columnResizeMode: {
      type: String,
      "default": "fit"
    },
    reorderableColumns: {
      type: Boolean,
      "default": false
    },
    expandedRows: {
      type: [Array, Object],
      "default": null
    },
    expandedRowIcon: {
      type: String,
      "default": void 0
    },
    collapsedRowIcon: {
      type: String,
      "default": void 0
    },
    rowGroupMode: {
      type: String,
      "default": null
    },
    groupRowsBy: {
      type: [Array, String, Function],
      "default": null
    },
    expandableRowGroups: {
      type: Boolean,
      "default": false
    },
    expandedRowGroups: {
      type: Array,
      "default": null
    },
    stateStorage: {
      type: String,
      "default": "session"
    },
    stateKey: {
      type: String,
      "default": null
    },
    editMode: {
      type: String,
      "default": null
    },
    editingRows: {
      type: Array,
      "default": null
    },
    rowClass: {
      type: Function,
      "default": null
    },
    rowStyle: {
      type: Function,
      "default": null
    },
    scrollable: {
      type: Boolean,
      "default": false
    },
    virtualScrollerOptions: {
      type: Object,
      "default": null
    },
    scrollHeight: {
      type: String,
      "default": null
    },
    frozenValue: {
      type: Array,
      "default": null
    },
    breakpoint: {
      type: String,
      "default": "960px"
    },
    showGridlines: {
      type: Boolean,
      "default": false
    },
    stripedRows: {
      type: Boolean,
      "default": false
    },
    highlightOnSelect: {
      type: Boolean,
      "default": false
    },
    size: {
      type: String,
      "default": null
    },
    tableStyle: {
      type: null,
      "default": null
    },
    tableClass: {
      type: [String, Object],
      "default": null
    },
    tableProps: {
      type: Object,
      "default": null
    },
    filterInputProps: {
      type: null,
      "default": null
    },
    filterButtonProps: {
      type: Object,
      "default": /* @__PURE__ */ __name(function _default2() {
        return {
          filter: {
            severity: "secondary",
            text: true,
            rounded: true
          },
          inline: {
            clear: {
              severity: "secondary",
              text: true,
              rounded: true
            }
          },
          popover: {
            addRule: {
              severity: "info",
              text: true,
              size: "small"
            },
            removeRule: {
              severity: "danger",
              text: true,
              size: "small"
            },
            apply: {
              size: "small"
            },
            clear: {
              outlined: true,
              size: "small"
            }
          }
        };
      }, "_default")
    },
    editButtonProps: {
      type: Object,
      "default": /* @__PURE__ */ __name(function _default3() {
        return {
          init: {
            severity: "secondary",
            text: true,
            rounded: true
          },
          save: {
            severity: "secondary",
            text: true,
            rounded: true
          },
          cancel: {
            severity: "secondary",
            text: true,
            rounded: true
          }
        };
      }, "_default")
    }
  },
  style: DataTableStyle,
  provide: /* @__PURE__ */ __name(function provide4() {
    return {
      $pcDataTable: this,
      $parentInstance: this
    };
  }, "provide")
};
var script$b = {
  name: "RowCheckbox",
  hostName: "DataTable",
  "extends": script$s,
  emits: ["change"],
  props: {
    value: null,
    checked: null,
    column: null,
    rowCheckboxIconTemplate: {
      type: Function,
      "default": null
    },
    index: {
      type: Number,
      "default": null
    }
  },
  methods: {
    getColumnPT: /* @__PURE__ */ __name(function getColumnPT(key) {
      var columnMetaData = {
        props: this.column.props,
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          index: this.index,
          checked: this.checked,
          disabled: this.$attrs.disabled
        }
      };
      return mergeProps(this.ptm("column.".concat(key), {
        column: columnMetaData
      }), this.ptm("column.".concat(key), columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));
    }, "getColumnPT"),
    getColumnProp: /* @__PURE__ */ __name(function getColumnProp() {
      return this.column.props && this.column.props.pt ? this.column.props.pt : void 0;
    }, "getColumnProp"),
    onChange: /* @__PURE__ */ __name(function onChange5(event2) {
      if (!this.$attrs.disabled) {
        this.$emit("change", {
          originalEvent: event2,
          data: this.value
        });
      }
    }, "onChange")
  },
  computed: {
    checkboxAriaLabel: /* @__PURE__ */ __name(function checkboxAriaLabel() {
      return this.$primevue.config.locale.aria ? this.checked ? this.$primevue.config.locale.aria.selectRow : this.$primevue.config.locale.aria.unselectRow : void 0;
    }, "checkboxAriaLabel")
  },
  components: {
    CheckIcon: script$x,
    Checkbox: script$y
  }
};
function render$b(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_CheckIcon = resolveComponent("CheckIcon");
  var _component_Checkbox = resolveComponent("Checkbox");
  return openBlock(), createBlock(_component_Checkbox, {
    modelValue: $props.checked,
    binary: true,
    disabled: _ctx.$attrs.disabled,
    "aria-label": $options.checkboxAriaLabel,
    onChange: $options.onChange,
    unstyled: _ctx.unstyled,
    pt: $options.getColumnPT("pcRowCheckbox")
  }, {
    icon: withCtx(function(slotProps) {
      return [$props.rowCheckboxIconTemplate ? (openBlock(), createBlock(resolveDynamicComponent($props.rowCheckboxIconTemplate), {
        key: 0,
        checked: slotProps.checked,
        "class": normalizeClass(slotProps["class"])
      }, null, 8, ["checked", "class"])) : !$props.rowCheckboxIconTemplate && slotProps.checked ? (openBlock(), createBlock(_component_CheckIcon, mergeProps({
        key: 1,
        "class": slotProps["class"]
      }, $options.getColumnPT("pcRowCheckbox")["icon"]), null, 16, ["class"])) : createCommentVNode("", true)];
    }),
    _: 1
  }, 8, ["modelValue", "disabled", "aria-label", "onChange", "unstyled", "pt"]);
}
__name(render$b, "render$b");
script$b.render = render$b;
var script$a = {
  name: "RowRadioButton",
  hostName: "DataTable",
  "extends": script$s,
  emits: ["change"],
  props: {
    value: null,
    checked: null,
    name: null,
    column: null,
    index: {
      type: Number,
      "default": null
    }
  },
  methods: {
    getColumnPT: /* @__PURE__ */ __name(function getColumnPT2(key) {
      var columnMetaData = {
        props: this.column.props,
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          index: this.index,
          checked: this.checked,
          disabled: this.$attrs.disabled
        }
      };
      return mergeProps(this.ptm("column.".concat(key), {
        column: columnMetaData
      }), this.ptm("column.".concat(key), columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));
    }, "getColumnPT"),
    getColumnProp: /* @__PURE__ */ __name(function getColumnProp2() {
      return this.column.props && this.column.props.pt ? this.column.props.pt : void 0;
    }, "getColumnProp"),
    onChange: /* @__PURE__ */ __name(function onChange6(event2) {
      if (!this.$attrs.disabled) {
        this.$emit("change", {
          originalEvent: event2,
          data: this.value
        });
      }
    }, "onChange")
  },
  components: {
    RadioButton: script$j
  }
};
function render$a(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_RadioButton = resolveComponent("RadioButton");
  return openBlock(), createBlock(_component_RadioButton, {
    modelValue: $props.checked,
    binary: true,
    disabled: _ctx.$attrs.disabled,
    name: $props.name,
    onChange: $options.onChange,
    unstyled: _ctx.unstyled,
    pt: $options.getColumnPT("pcRowRadiobutton")
  }, null, 8, ["modelValue", "disabled", "name", "onChange", "unstyled", "pt"]);
}
__name(render$a, "render$a");
script$a.render = render$a;
var script$9 = {
  name: "BodyCell",
  hostName: "DataTable",
  "extends": script$s,
  emits: ["cell-edit-init", "cell-edit-complete", "cell-edit-cancel", "row-edit-init", "row-edit-save", "row-edit-cancel", "row-toggle", "radio-change", "checkbox-change", "editing-meta-change"],
  props: {
    rowData: {
      type: Object,
      "default": null
    },
    column: {
      type: Object,
      "default": null
    },
    frozenRow: {
      type: Boolean,
      "default": false
    },
    rowIndex: {
      type: Number,
      "default": null
    },
    index: {
      type: Number,
      "default": null
    },
    isRowExpanded: {
      type: Boolean,
      "default": false
    },
    selected: {
      type: Boolean,
      "default": false
    },
    editing: {
      type: Boolean,
      "default": false
    },
    editingMeta: {
      type: Object,
      "default": null
    },
    editMode: {
      type: String,
      "default": null
    },
    virtualScrollerContentProps: {
      type: Object,
      "default": null
    },
    ariaControls: {
      type: String,
      "default": null
    },
    name: {
      type: String,
      "default": null
    },
    expandedRowIcon: {
      type: String,
      "default": null
    },
    collapsedRowIcon: {
      type: String,
      "default": null
    },
    editButtonProps: {
      type: Object,
      "default": null
    }
  },
  documentEditListener: null,
  selfClick: false,
  overlayEventListener: null,
  data: /* @__PURE__ */ __name(function data3() {
    return {
      d_editing: this.editing,
      styleObject: {}
    };
  }, "data"),
  watch: {
    editing: /* @__PURE__ */ __name(function editing(newValue) {
      this.d_editing = newValue;
    }, "editing"),
    "$data.d_editing": /* @__PURE__ */ __name(function $dataD_editing(newValue) {
      this.$emit("editing-meta-change", {
        data: this.rowData,
        field: this.field || "field_".concat(this.index),
        index: this.rowIndex,
        editing: newValue
      });
    }, "$dataD_editing")
  },
  mounted: /* @__PURE__ */ __name(function mounted3() {
    if (this.columnProp("frozen")) {
      this.updateStickyPosition();
    }
  }, "mounted"),
  updated: /* @__PURE__ */ __name(function updated() {
    var _this = this;
    if (this.columnProp("frozen")) {
      this.updateStickyPosition();
    }
    if (this.d_editing && (this.editMode === "cell" || this.editMode === "row" && this.columnProp("rowEditor"))) {
      setTimeout(function() {
        var focusableEl = getFirstFocusableElement(_this.$el);
        focusableEl && focusableEl.focus();
      }, 1);
    }
  }, "updated"),
  beforeUnmount: /* @__PURE__ */ __name(function beforeUnmount() {
    if (this.overlayEventListener) {
      OverlayEventBus.off("overlay-click", this.overlayEventListener);
      this.overlayEventListener = null;
    }
  }, "beforeUnmount"),
  methods: {
    columnProp: /* @__PURE__ */ __name(function columnProp(prop) {
      return getVNodeProp(this.column, prop);
    }, "columnProp"),
    getColumnPT: /* @__PURE__ */ __name(function getColumnPT3(key) {
      var _this$$parentInstance, _this$$parentInstance2;
      var columnMetaData = {
        props: this.column.props,
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          index: this.index,
          size: (_this$$parentInstance = this.$parentInstance) === null || _this$$parentInstance === void 0 || (_this$$parentInstance = _this$$parentInstance.$parentInstance) === null || _this$$parentInstance === void 0 ? void 0 : _this$$parentInstance.size,
          showGridlines: (_this$$parentInstance2 = this.$parentInstance) === null || _this$$parentInstance2 === void 0 || (_this$$parentInstance2 = _this$$parentInstance2.$parentInstance) === null || _this$$parentInstance2 === void 0 ? void 0 : _this$$parentInstance2.showGridlines
        }
      };
      return mergeProps(this.ptm("column.".concat(key), {
        column: columnMetaData
      }), this.ptm("column.".concat(key), columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));
    }, "getColumnPT"),
    getColumnProp: /* @__PURE__ */ __name(function getColumnProp3() {
      return this.column.props && this.column.props.pt ? this.column.props.pt : void 0;
    }, "getColumnProp"),
    resolveFieldData: /* @__PURE__ */ __name(function resolveFieldData$1() {
      return resolveFieldData(this.rowData, this.field);
    }, "resolveFieldData$1"),
    toggleRow: /* @__PURE__ */ __name(function toggleRow(event2) {
      this.$emit("row-toggle", {
        originalEvent: event2,
        data: this.rowData
      });
    }, "toggleRow"),
    toggleRowWithRadio: /* @__PURE__ */ __name(function toggleRowWithRadio(event2, index) {
      this.$emit("radio-change", {
        originalEvent: event2.originalEvent,
        index,
        data: event2.data
      });
    }, "toggleRowWithRadio"),
    toggleRowWithCheckbox: /* @__PURE__ */ __name(function toggleRowWithCheckbox(event2, index) {
      this.$emit("checkbox-change", {
        originalEvent: event2.originalEvent,
        index,
        data: event2.data
      });
    }, "toggleRowWithCheckbox"),
    isEditable: /* @__PURE__ */ __name(function isEditable() {
      return this.column.children && this.column.children.editor != null;
    }, "isEditable"),
    bindDocumentEditListener: /* @__PURE__ */ __name(function bindDocumentEditListener() {
      var _this2 = this;
      if (!this.documentEditListener) {
        this.documentEditListener = function(event2) {
          if (!_this2.selfClick) {
            _this2.completeEdit(event2, "outside");
          }
          _this2.selfClick = false;
        };
        document.addEventListener("click", this.documentEditListener);
      }
    }, "bindDocumentEditListener"),
    unbindDocumentEditListener: /* @__PURE__ */ __name(function unbindDocumentEditListener() {
      if (this.documentEditListener) {
        document.removeEventListener("click", this.documentEditListener);
        this.documentEditListener = null;
        this.selfClick = false;
      }
    }, "unbindDocumentEditListener"),
    switchCellToViewMode: /* @__PURE__ */ __name(function switchCellToViewMode() {
      this.d_editing = false;
      this.unbindDocumentEditListener();
      OverlayEventBus.off("overlay-click", this.overlayEventListener);
      this.overlayEventListener = null;
    }, "switchCellToViewMode"),
    onClick: /* @__PURE__ */ __name(function onClick(event2) {
      var _this3 = this;
      if (this.editMode === "cell" && this.isEditable()) {
        this.selfClick = true;
        if (!this.d_editing) {
          this.d_editing = true;
          this.bindDocumentEditListener();
          this.$emit("cell-edit-init", {
            originalEvent: event2,
            data: this.rowData,
            field: this.field,
            index: this.rowIndex
          });
          this.overlayEventListener = function(e) {
            if (_this3.$el && _this3.$el.contains(e.target)) {
              _this3.selfClick = true;
            }
          };
          OverlayEventBus.on("overlay-click", this.overlayEventListener);
        }
      }
    }, "onClick"),
    completeEdit: /* @__PURE__ */ __name(function completeEdit(event2, type) {
      var completeEvent = {
        originalEvent: event2,
        data: this.rowData,
        newData: this.editingRowData,
        value: this.rowData[this.field],
        newValue: this.editingRowData[this.field],
        field: this.field,
        index: this.rowIndex,
        type,
        defaultPrevented: false,
        preventDefault: /* @__PURE__ */ __name(function preventDefault() {
          this.defaultPrevented = true;
        }, "preventDefault")
      };
      this.$emit("cell-edit-complete", completeEvent);
      if (!completeEvent.defaultPrevented) {
        this.switchCellToViewMode();
      }
    }, "completeEdit"),
    onKeyDown: /* @__PURE__ */ __name(function onKeyDown(event2) {
      if (this.editMode === "cell") {
        switch (event2.code) {
          case "Enter":
          case "NumpadEnter":
            this.completeEdit(event2, "enter");
            break;
          case "Escape":
            this.switchCellToViewMode();
            this.$emit("cell-edit-cancel", {
              originalEvent: event2,
              data: this.rowData,
              field: this.field,
              index: this.rowIndex
            });
            break;
          case "Tab":
            this.completeEdit(event2, "tab");
            if (event2.shiftKey) this.moveToPreviousCell(event2);
            else this.moveToNextCell(event2);
            break;
        }
      }
    }, "onKeyDown"),
    moveToPreviousCell: /* @__PURE__ */ __name(function moveToPreviousCell(event2) {
      var currentCell = this.findCell(event2.target);
      var targetCell = this.findPreviousEditableColumn(currentCell);
      if (targetCell) {
        invokeElementMethod(targetCell, "click");
        event2.preventDefault();
      }
    }, "moveToPreviousCell"),
    moveToNextCell: /* @__PURE__ */ __name(function moveToNextCell(event2) {
      var currentCell = this.findCell(event2.target);
      var targetCell = this.findNextEditableColumn(currentCell);
      if (targetCell) {
        invokeElementMethod(targetCell, "click");
        event2.preventDefault();
      }
    }, "moveToNextCell"),
    findCell: /* @__PURE__ */ __name(function findCell(element) {
      if (element) {
        var cell = element;
        while (cell && !getAttribute(cell, "data-p-cell-editing")) {
          cell = cell.parentElement;
        }
        return cell;
      } else {
        return null;
      }
    }, "findCell"),
    findPreviousEditableColumn: /* @__PURE__ */ __name(function findPreviousEditableColumn(cell) {
      var prevCell = cell.previousElementSibling;
      if (!prevCell) {
        var previousRow = cell.parentElement.previousElementSibling;
        if (previousRow) {
          prevCell = previousRow.lastElementChild;
        }
      }
      if (prevCell) {
        if (getAttribute(prevCell, "data-p-editable-column")) return prevCell;
        else return this.findPreviousEditableColumn(prevCell);
      } else {
        return null;
      }
    }, "findPreviousEditableColumn"),
    findNextEditableColumn: /* @__PURE__ */ __name(function findNextEditableColumn(cell) {
      var nextCell = cell.nextElementSibling;
      if (!nextCell) {
        var nextRow = cell.parentElement.nextElementSibling;
        if (nextRow) {
          nextCell = nextRow.firstElementChild;
        }
      }
      if (nextCell) {
        if (getAttribute(nextCell, "data-p-editable-column")) return nextCell;
        else return this.findNextEditableColumn(nextCell);
      } else {
        return null;
      }
    }, "findNextEditableColumn"),
    onRowEditInit: /* @__PURE__ */ __name(function onRowEditInit(event2) {
      this.$emit("row-edit-init", {
        originalEvent: event2,
        data: this.rowData,
        newData: this.editingRowData,
        field: this.field,
        index: this.rowIndex
      });
    }, "onRowEditInit"),
    onRowEditSave: /* @__PURE__ */ __name(function onRowEditSave(event2) {
      this.$emit("row-edit-save", {
        originalEvent: event2,
        data: this.rowData,
        newData: this.editingRowData,
        field: this.field,
        index: this.rowIndex
      });
    }, "onRowEditSave"),
    onRowEditCancel: /* @__PURE__ */ __name(function onRowEditCancel(event2) {
      this.$emit("row-edit-cancel", {
        originalEvent: event2,
        data: this.rowData,
        newData: this.editingRowData,
        field: this.field,
        index: this.rowIndex
      });
    }, "onRowEditCancel"),
    editorInitCallback: /* @__PURE__ */ __name(function editorInitCallback(event2) {
      this.$emit("row-edit-init", {
        originalEvent: event2,
        data: this.rowData,
        newData: this.editingRowData,
        field: this.field,
        index: this.rowIndex
      });
    }, "editorInitCallback"),
    editorSaveCallback: /* @__PURE__ */ __name(function editorSaveCallback(event2) {
      if (this.editMode === "row") {
        this.$emit("row-edit-save", {
          originalEvent: event2,
          data: this.rowData,
          newData: this.editingRowData,
          field: this.field,
          index: this.rowIndex
        });
      } else {
        this.completeEdit(event2, "enter");
      }
    }, "editorSaveCallback"),
    editorCancelCallback: /* @__PURE__ */ __name(function editorCancelCallback(event2) {
      if (this.editMode === "row") {
        this.$emit("row-edit-cancel", {
          originalEvent: event2,
          data: this.rowData,
          newData: this.editingRowData,
          field: this.field,
          index: this.rowIndex
        });
      } else {
        this.switchCellToViewMode();
        this.$emit("cell-edit-cancel", {
          originalEvent: event2,
          data: this.rowData,
          field: this.field,
          index: this.rowIndex
        });
      }
    }, "editorCancelCallback"),
    updateStickyPosition: /* @__PURE__ */ __name(function updateStickyPosition() {
      if (this.columnProp("frozen")) {
        var align = this.columnProp("alignFrozen");
        if (align === "right") {
          var right = 0;
          var next2 = getNextElementSibling(this.$el, '[data-p-frozen-column="true"]');
          if (next2) {
            right = getOuterWidth(next2) + parseFloat(next2.style.right || 0);
          }
          this.styleObject.right = right + "px";
        } else {
          var left = 0;
          var prev2 = getPreviousElementSibling(this.$el, '[data-p-frozen-column="true"]');
          if (prev2) {
            left = getOuterWidth(prev2) + parseFloat(prev2.style.left || 0);
          }
          this.styleObject.left = left + "px";
        }
      }
    }, "updateStickyPosition"),
    getVirtualScrollerProp: /* @__PURE__ */ __name(function getVirtualScrollerProp(option) {
      return this.virtualScrollerContentProps ? this.virtualScrollerContentProps[option] : null;
    }, "getVirtualScrollerProp")
  },
  computed: {
    editingRowData: /* @__PURE__ */ __name(function editingRowData() {
      return this.editingMeta[this.rowIndex] ? this.editingMeta[this.rowIndex].data : this.rowData;
    }, "editingRowData"),
    field: /* @__PURE__ */ __name(function field() {
      return this.columnProp("field");
    }, "field"),
    containerClass: /* @__PURE__ */ __name(function containerClass() {
      return [this.columnProp("bodyClass"), this.columnProp("class"), this.cx("bodyCell")];
    }, "containerClass"),
    containerStyle: /* @__PURE__ */ __name(function containerStyle() {
      var bodyStyle = this.columnProp("bodyStyle");
      var columnStyle = this.columnProp("style");
      return this.columnProp("frozen") ? [columnStyle, bodyStyle, this.styleObject] : [columnStyle, bodyStyle];
    }, "containerStyle"),
    loading: /* @__PURE__ */ __name(function loading() {
      return this.getVirtualScrollerProp("loading");
    }, "loading"),
    loadingOptions: /* @__PURE__ */ __name(function loadingOptions() {
      var getLoaderOptions = this.getVirtualScrollerProp("getLoaderOptions");
      return getLoaderOptions && getLoaderOptions(this.rowIndex, {
        cellIndex: this.index,
        cellFirst: this.index === 0,
        cellLast: this.index === this.getVirtualScrollerProp("columns").length - 1,
        cellEven: this.index % 2 === 0,
        cellOdd: this.index % 2 !== 0,
        column: this.column,
        field: this.field
      });
    }, "loadingOptions"),
    expandButtonAriaLabel: /* @__PURE__ */ __name(function expandButtonAriaLabel() {
      return this.$primevue.config.locale.aria ? this.isRowExpanded ? this.$primevue.config.locale.aria.expandRow : this.$primevue.config.locale.aria.collapseRow : void 0;
    }, "expandButtonAriaLabel"),
    initButtonAriaLabel: /* @__PURE__ */ __name(function initButtonAriaLabel() {
      return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.editRow : void 0;
    }, "initButtonAriaLabel"),
    saveButtonAriaLabel: /* @__PURE__ */ __name(function saveButtonAriaLabel() {
      return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.saveEdit : void 0;
    }, "saveButtonAriaLabel"),
    cancelButtonAriaLabel: /* @__PURE__ */ __name(function cancelButtonAriaLabel() {
      return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.cancelEdit : void 0;
    }, "cancelButtonAriaLabel")
  },
  components: {
    DTRadioButton: script$a,
    DTCheckbox: script$b,
    Button: script$z,
    ChevronDownIcon: script$A,
    ChevronRightIcon: script$B,
    BarsIcon: script$C,
    PencilIcon: script$k,
    CheckIcon: script$x,
    TimesIcon: script$D
  },
  directives: {
    ripple: Ripple
  }
};
function _typeof$a(o) {
  "@babel/helpers - typeof";
  return _typeof$a = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$a(o);
}
__name(_typeof$a, "_typeof$a");
function ownKeys$a(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
__name(ownKeys$a, "ownKeys$a");
function _objectSpread$a(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys$a(Object(t), true).forEach(function(r2) {
      _defineProperty$a(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$a(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
__name(_objectSpread$a, "_objectSpread$a");
function _defineProperty$a(e, r, t) {
  return (r = _toPropertyKey$a(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty$a, "_defineProperty$a");
function _toPropertyKey$a(t) {
  var i = _toPrimitive$a(t, "string");
  return "symbol" == _typeof$a(i) ? i : i + "";
}
__name(_toPropertyKey$a, "_toPropertyKey$a");
function _toPrimitive$a(t, r) {
  if ("object" != _typeof$a(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof$a(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive$a, "_toPrimitive$a");
var _hoisted_1$4 = ["colspan", "rowspan", "data-p-selection-column", "data-p-editable-column", "data-p-cell-editing", "data-p-frozen-column"];
var _hoisted_2$2 = ["aria-expanded", "aria-controls", "aria-label"];
function render$9(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_DTRadioButton = resolveComponent("DTRadioButton");
  var _component_DTCheckbox = resolveComponent("DTCheckbox");
  var _component_BarsIcon = resolveComponent("BarsIcon");
  var _component_ChevronDownIcon = resolveComponent("ChevronDownIcon");
  var _component_ChevronRightIcon = resolveComponent("ChevronRightIcon");
  var _component_Button = resolveComponent("Button");
  var _directive_ripple = resolveDirective("ripple");
  return $options.loading ? (openBlock(), createElementBlock("td", mergeProps({
    key: 0,
    style: $options.containerStyle,
    "class": $options.containerClass,
    role: "cell"
  }, _objectSpread$a(_objectSpread$a({}, $options.getColumnPT("root")), $options.getColumnPT("bodyCell"))), [(openBlock(), createBlock(resolveDynamicComponent($props.column.children.loading), {
    data: $props.rowData,
    column: $props.column,
    field: $options.field,
    index: $props.rowIndex,
    frozenRow: $props.frozenRow,
    loadingOptions: $options.loadingOptions
  }, null, 8, ["data", "column", "field", "index", "frozenRow", "loadingOptions"]))], 16)) : (openBlock(), createElementBlock("td", mergeProps({
    key: 1,
    style: $options.containerStyle,
    "class": $options.containerClass,
    colspan: $options.columnProp("colspan"),
    rowspan: $options.columnProp("rowspan"),
    onClick: _cache[3] || (_cache[3] = function() {
      return $options.onClick && $options.onClick.apply($options, arguments);
    }),
    onKeydown: _cache[4] || (_cache[4] = function() {
      return $options.onKeyDown && $options.onKeyDown.apply($options, arguments);
    }),
    role: "cell"
  }, _objectSpread$a(_objectSpread$a({}, $options.getColumnPT("root")), $options.getColumnPT("bodyCell")), {
    "data-p-selection-column": $options.columnProp("selectionMode") != null,
    "data-p-editable-column": $options.isEditable(),
    "data-p-cell-editing": $data.d_editing,
    "data-p-frozen-column": $options.columnProp("frozen")
  }), [$props.column.children && $props.column.children.body && !$data.d_editing ? (openBlock(), createBlock(resolveDynamicComponent($props.column.children.body), {
    key: 0,
    data: $props.rowData,
    column: $props.column,
    field: $options.field,
    index: $props.rowIndex,
    frozenRow: $props.frozenRow,
    editorInitCallback: $options.editorInitCallback,
    rowTogglerCallback: $options.toggleRow
  }, null, 8, ["data", "column", "field", "index", "frozenRow", "editorInitCallback", "rowTogglerCallback"])) : $props.column.children && $props.column.children.editor && $data.d_editing ? (openBlock(), createBlock(resolveDynamicComponent($props.column.children.editor), {
    key: 1,
    data: $options.editingRowData,
    column: $props.column,
    field: $options.field,
    index: $props.rowIndex,
    frozenRow: $props.frozenRow,
    editorSaveCallback: $options.editorSaveCallback,
    editorCancelCallback: $options.editorCancelCallback
  }, null, 8, ["data", "column", "field", "index", "frozenRow", "editorSaveCallback", "editorCancelCallback"])) : $props.column.children && $props.column.children.body && !$props.column.children.editor && $data.d_editing ? (openBlock(), createBlock(resolveDynamicComponent($props.column.children.body), {
    key: 2,
    data: $options.editingRowData,
    column: $props.column,
    field: $options.field,
    index: $props.rowIndex,
    frozenRow: $props.frozenRow
  }, null, 8, ["data", "column", "field", "index", "frozenRow"])) : $options.columnProp("selectionMode") ? (openBlock(), createElementBlock(Fragment, {
    key: 3
  }, [$options.columnProp("selectionMode") === "single" ? (openBlock(), createBlock(_component_DTRadioButton, {
    key: 0,
    value: $props.rowData,
    name: $props.name,
    checked: $props.selected,
    onChange: _cache[0] || (_cache[0] = function($event) {
      return $options.toggleRowWithRadio($event, $props.rowIndex);
    }),
    column: $props.column,
    index: $props.index,
    unstyled: _ctx.unstyled,
    pt: _ctx.pt
  }, null, 8, ["value", "name", "checked", "column", "index", "unstyled", "pt"])) : $options.columnProp("selectionMode") === "multiple" ? (openBlock(), createBlock(_component_DTCheckbox, {
    key: 1,
    value: $props.rowData,
    checked: $props.selected,
    rowCheckboxIconTemplate: $props.column.children && $props.column.children.rowcheckboxicon,
    "aria-selected": $props.selected ? true : void 0,
    onChange: _cache[1] || (_cache[1] = function($event) {
      return $options.toggleRowWithCheckbox($event, $props.rowIndex);
    }),
    column: $props.column,
    index: $props.index,
    unstyled: _ctx.unstyled,
    pt: _ctx.pt
  }, null, 8, ["value", "checked", "rowCheckboxIconTemplate", "aria-selected", "column", "index", "unstyled", "pt"])) : createCommentVNode("", true)], 64)) : $options.columnProp("rowReorder") ? (openBlock(), createElementBlock(Fragment, {
    key: 4
  }, [$props.column.children && $props.column.children.rowreordericon ? (openBlock(), createBlock(resolveDynamicComponent($props.column.children.rowreordericon), {
    key: 0,
    "class": normalizeClass(_ctx.cx("reorderableRowHandle"))
  }, null, 8, ["class"])) : $options.columnProp("rowReorderIcon") ? (openBlock(), createElementBlock("i", mergeProps({
    key: 1,
    "class": [_ctx.cx("reorderableRowHandle"), $options.columnProp("rowReorderIcon")]
  }, $options.getColumnPT("reorderableRowHandle")), null, 16)) : (openBlock(), createBlock(_component_BarsIcon, mergeProps({
    key: 2,
    "class": _ctx.cx("reorderableRowHandle")
  }, $options.getColumnPT("reorderableRowHandle")), null, 16, ["class"]))], 64)) : $options.columnProp("expander") ? withDirectives((openBlock(), createElementBlock("button", mergeProps({
    key: 5,
    "class": _ctx.cx("rowToggleButton"),
    type: "button",
    "aria-expanded": $props.isRowExpanded,
    "aria-controls": $props.ariaControls,
    "aria-label": $options.expandButtonAriaLabel,
    onClick: _cache[2] || (_cache[2] = function() {
      return $options.toggleRow && $options.toggleRow.apply($options, arguments);
    })
  }, $options.getColumnPT("rowToggleButton"), {
    "data-pc-group-section": "rowactionbutton"
  }), [$props.column.children && $props.column.children.rowtogglericon ? (openBlock(), createBlock(resolveDynamicComponent($props.column.children.rowtogglericon), {
    key: 0,
    "class": normalizeClass(_ctx.cx("rowToggleIcon")),
    rowExpanded: $props.isRowExpanded
  }, null, 8, ["class", "rowExpanded"])) : (openBlock(), createElementBlock(Fragment, {
    key: 1
  }, [$props.isRowExpanded && $props.expandedRowIcon ? (openBlock(), createElementBlock("span", {
    key: 0,
    "class": normalizeClass([_ctx.cx("rowToggleIcon"), $props.expandedRowIcon])
  }, null, 2)) : $props.isRowExpanded && !$props.expandedRowIcon ? (openBlock(), createBlock(_component_ChevronDownIcon, mergeProps({
    key: 1,
    "class": _ctx.cx("rowToggleIcon")
  }, $options.getColumnPT("rowToggleIcon")), null, 16, ["class"])) : !$props.isRowExpanded && $props.collapsedRowIcon ? (openBlock(), createElementBlock("span", {
    key: 2,
    "class": normalizeClass([_ctx.cx("rowToggleIcon"), $props.collapsedRowIcon])
  }, null, 2)) : !$props.isRowExpanded && !$props.collapsedRowIcon ? (openBlock(), createBlock(_component_ChevronRightIcon, mergeProps({
    key: 3,
    "class": _ctx.cx("rowToggleIcon")
  }, $options.getColumnPT("rowToggleIcon")), null, 16, ["class"])) : createCommentVNode("", true)], 64))], 16, _hoisted_2$2)), [[_directive_ripple]]) : $props.editMode === "row" && $options.columnProp("rowEditor") ? (openBlock(), createElementBlock(Fragment, {
    key: 6
  }, [!$data.d_editing ? (openBlock(), createBlock(_component_Button, mergeProps({
    key: 0,
    "class": _ctx.cx("pcRowEditorInit"),
    "aria-label": $options.initButtonAriaLabel,
    unstyled: _ctx.unstyled,
    onClick: $options.onRowEditInit
  }, $props.editButtonProps.init, {
    pt: $options.getColumnPT("pcRowEditorInit"),
    "data-pc-group-section": "rowactionbutton"
  }), {
    icon: withCtx(function(slotProps) {
      return [(openBlock(), createBlock(resolveDynamicComponent($props.column.children && $props.column.children.roweditoriniticon || "PencilIcon"), mergeProps({
        "class": slotProps["class"]
      }, $options.getColumnPT("pcRowEditorInit")["icon"]), null, 16, ["class"]))];
    }),
    _: 1
  }, 16, ["class", "aria-label", "unstyled", "onClick", "pt"])) : createCommentVNode("", true), $data.d_editing ? (openBlock(), createBlock(_component_Button, mergeProps({
    key: 1,
    "class": _ctx.cx("pcRowEditorSave"),
    "aria-label": $options.saveButtonAriaLabel,
    unstyled: _ctx.unstyled,
    onClick: $options.onRowEditSave
  }, $props.editButtonProps.save, {
    pt: $options.getColumnPT("pcRowEditorSave"),
    "data-pc-group-section": "rowactionbutton"
  }), {
    icon: withCtx(function(slotProps) {
      return [(openBlock(), createBlock(resolveDynamicComponent($props.column.children && $props.column.children.roweditorsaveicon || "CheckIcon"), mergeProps({
        "class": slotProps["class"]
      }, $options.getColumnPT("pcRowEditorSave")["icon"]), null, 16, ["class"]))];
    }),
    _: 1
  }, 16, ["class", "aria-label", "unstyled", "onClick", "pt"])) : createCommentVNode("", true), $data.d_editing ? (openBlock(), createBlock(_component_Button, mergeProps({
    key: 2,
    "class": _ctx.cx("pcRowEditorCancel"),
    "aria-label": $options.cancelButtonAriaLabel,
    unstyled: _ctx.unstyled,
    onClick: $options.onRowEditCancel
  }, $props.editButtonProps.cancel, {
    pt: $options.getColumnPT("pcRowEditorCancel"),
    "data-pc-group-section": "rowactionbutton"
  }), {
    icon: withCtx(function(slotProps) {
      return [(openBlock(), createBlock(resolveDynamicComponent($props.column.children && $props.column.children.roweditorcancelicon || "TimesIcon"), mergeProps({
        "class": slotProps["class"]
      }, $options.getColumnPT("pcRowEditorCancel")["icon"]), null, 16, ["class"]))];
    }),
    _: 1
  }, 16, ["class", "aria-label", "unstyled", "onClick", "pt"])) : createCommentVNode("", true)], 64)) : (openBlock(), createElementBlock(Fragment, {
    key: 7
  }, [createTextVNode(toDisplayString($options.resolveFieldData()), 1)], 64))], 16, _hoisted_1$4));
}
__name(render$9, "render$9");
script$9.render = render$9;
function _typeof$9(o) {
  "@babel/helpers - typeof";
  return _typeof$9 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$9(o);
}
__name(_typeof$9, "_typeof$9");
function _createForOfIteratorHelper$2(r, e) {
  var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
  if (!t) {
    if (Array.isArray(r) || (t = _unsupportedIterableToArray$2(r)) || e) {
      t && (r = t);
      var _n = 0, F = /* @__PURE__ */ __name(function F2() {
      }, "F");
      return { s: F, n: /* @__PURE__ */ __name(function n() {
        return _n >= r.length ? { done: true } : { done: false, value: r[_n++] };
      }, "n"), e: /* @__PURE__ */ __name(function e2(r2) {
        throw r2;
      }, "e"), f: F };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var o, a = true, u = false;
  return { s: /* @__PURE__ */ __name(function s() {
    t = t.call(r);
  }, "s"), n: /* @__PURE__ */ __name(function n() {
    var r2 = t.next();
    return a = r2.done, r2;
  }, "n"), e: /* @__PURE__ */ __name(function e2(r2) {
    u = true, o = r2;
  }, "e"), f: /* @__PURE__ */ __name(function f() {
    try {
      a || null == t["return"] || t["return"]();
    } finally {
      if (u) throw o;
    }
  }, "f") };
}
__name(_createForOfIteratorHelper$2, "_createForOfIteratorHelper$2");
function _unsupportedIterableToArray$2(r, a) {
  if (r) {
    if ("string" == typeof r) return _arrayLikeToArray$2(r, a);
    var t = {}.toString.call(r).slice(8, -1);
    return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray$2(r, a) : void 0;
  }
}
__name(_unsupportedIterableToArray$2, "_unsupportedIterableToArray$2");
function _arrayLikeToArray$2(r, a) {
  (null == a || a > r.length) && (a = r.length);
  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
  return n;
}
__name(_arrayLikeToArray$2, "_arrayLikeToArray$2");
function ownKeys$9(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
__name(ownKeys$9, "ownKeys$9");
function _objectSpread$9(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys$9(Object(t), true).forEach(function(r2) {
      _defineProperty$9(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$9(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
__name(_objectSpread$9, "_objectSpread$9");
function _defineProperty$9(e, r, t) {
  return (r = _toPropertyKey$9(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty$9, "_defineProperty$9");
function _toPropertyKey$9(t) {
  var i = _toPrimitive$9(t, "string");
  return "symbol" == _typeof$9(i) ? i : i + "";
}
__name(_toPropertyKey$9, "_toPropertyKey$9");
function _toPrimitive$9(t, r) {
  if ("object" != _typeof$9(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof$9(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive$9, "_toPrimitive$9");
var script$8 = {
  name: "BodyRow",
  hostName: "DataTable",
  "extends": script$s,
  emits: ["rowgroup-toggle", "row-click", "row-dblclick", "row-rightclick", "row-touchend", "row-keydown", "row-mousedown", "row-dragstart", "row-dragover", "row-dragleave", "row-dragend", "row-drop", "row-toggle", "radio-change", "checkbox-change", "cell-edit-init", "cell-edit-complete", "cell-edit-cancel", "row-edit-init", "row-edit-save", "row-edit-cancel", "editing-meta-change"],
  props: {
    rowData: {
      type: Object,
      "default": null
    },
    index: {
      type: Number,
      "default": 0
    },
    value: {
      type: Array,
      "default": null
    },
    columns: {
      type: null,
      "default": null
    },
    frozenRow: {
      type: Boolean,
      "default": false
    },
    empty: {
      type: Boolean,
      "default": false
    },
    rowGroupMode: {
      type: String,
      "default": null
    },
    groupRowsBy: {
      type: [Array, String, Function],
      "default": null
    },
    expandableRowGroups: {
      type: Boolean,
      "default": false
    },
    expandedRowGroups: {
      type: Array,
      "default": null
    },
    first: {
      type: Number,
      "default": 0
    },
    dataKey: {
      type: [String, Function],
      "default": null
    },
    expandedRowIcon: {
      type: String,
      "default": null
    },
    collapsedRowIcon: {
      type: String,
      "default": null
    },
    expandedRows: {
      type: [Array, Object],
      "default": null
    },
    selection: {
      type: [Array, Object],
      "default": null
    },
    selectionKeys: {
      type: null,
      "default": null
    },
    selectionMode: {
      type: String,
      "default": null
    },
    contextMenu: {
      type: Boolean,
      "default": false
    },
    contextMenuSelection: {
      type: Object,
      "default": null
    },
    rowClass: {
      type: null,
      "default": null
    },
    rowStyle: {
      type: null,
      "default": null
    },
    rowGroupHeaderStyle: {
      type: null,
      "default": null
    },
    editMode: {
      type: String,
      "default": null
    },
    compareSelectionBy: {
      type: String,
      "default": "deepEquals"
    },
    editingRows: {
      type: Array,
      "default": null
    },
    editingRowKeys: {
      type: null,
      "default": null
    },
    editingMeta: {
      type: Object,
      "default": null
    },
    templates: {
      type: null,
      "default": null
    },
    scrollable: {
      type: Boolean,
      "default": false
    },
    editButtonProps: {
      type: Object,
      "default": null
    },
    virtualScrollerContentProps: {
      type: Object,
      "default": null
    },
    isVirtualScrollerDisabled: {
      type: Boolean,
      "default": false
    },
    expandedRowId: {
      type: String,
      "default": null
    },
    nameAttributeSelector: {
      type: String,
      "default": null
    }
  },
  data: /* @__PURE__ */ __name(function data4() {
    return {
      d_rowExpanded: false
    };
  }, "data"),
  watch: {
    expandedRows: {
      deep: true,
      immediate: true,
      handler: /* @__PURE__ */ __name(function handler(newValue) {
        var _this = this;
        this.d_rowExpanded = this.dataKey ? (newValue === null || newValue === void 0 ? void 0 : newValue[resolveFieldData(this.rowData, this.dataKey)]) !== void 0 : newValue === null || newValue === void 0 ? void 0 : newValue.some(function(d) {
          return _this.equals(_this.rowData, d);
        });
      }, "handler")
    }
  },
  methods: {
    columnProp: /* @__PURE__ */ __name(function columnProp2(col, prop) {
      return getVNodeProp(col, prop);
    }, "columnProp"),
    //@todo - update this method
    getColumnPT: /* @__PURE__ */ __name(function getColumnPT4(key) {
      var columnMetaData = {
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        }
      };
      return mergeProps(this.ptm("column.".concat(key), {
        column: columnMetaData
      }), this.ptm("column.".concat(key), columnMetaData), this.ptmo(this.columnProp({}, "pt"), key, columnMetaData));
    }, "getColumnPT"),
    //@todo - update this method
    getBodyRowPTOptions: /* @__PURE__ */ __name(function getBodyRowPTOptions(key) {
      var _this$$parentInstance;
      var datatable = (_this$$parentInstance = this.$parentInstance) === null || _this$$parentInstance === void 0 ? void 0 : _this$$parentInstance.$parentInstance;
      return this.ptm(key, {
        context: {
          index: this.rowIndex,
          selectable: (datatable === null || datatable === void 0 ? void 0 : datatable.rowHover) || (datatable === null || datatable === void 0 ? void 0 : datatable.selectionMode),
          selected: this.isSelected,
          stripedRows: (datatable === null || datatable === void 0 ? void 0 : datatable.stripedRows) || false
        }
      });
    }, "getBodyRowPTOptions"),
    shouldRenderBodyCell: /* @__PURE__ */ __name(function shouldRenderBodyCell(column) {
      var isHidden = this.columnProp(column, "hidden");
      if (this.rowGroupMode && !isHidden) {
        var field2 = this.columnProp(column, "field");
        if (this.rowGroupMode === "subheader") {
          return this.groupRowsBy !== field2;
        } else if (this.rowGroupMode === "rowspan") {
          if (this.isGrouped(column)) {
            var prevRowData = this.value[this.rowIndex - 1];
            if (prevRowData) {
              var currentRowFieldData = resolveFieldData(this.value[this.rowIndex], field2);
              var previousRowFieldData = resolveFieldData(prevRowData, field2);
              return currentRowFieldData !== previousRowFieldData;
            } else {
              return true;
            }
          } else {
            return true;
          }
        }
      } else {
        return !isHidden;
      }
    }, "shouldRenderBodyCell"),
    calculateRowGroupSize: /* @__PURE__ */ __name(function calculateRowGroupSize(column) {
      if (this.isGrouped(column)) {
        var index = this.rowIndex;
        var field2 = this.columnProp(column, "field");
        var currentRowFieldData = resolveFieldData(this.value[index], field2);
        var nextRowFieldData = currentRowFieldData;
        var groupRowSpan = 0;
        while (currentRowFieldData === nextRowFieldData) {
          groupRowSpan++;
          var nextRowData = this.value[++index];
          if (nextRowData) {
            nextRowFieldData = resolveFieldData(nextRowData, field2);
          } else {
            break;
          }
        }
        return groupRowSpan === 1 ? null : groupRowSpan;
      } else {
        return null;
      }
    }, "calculateRowGroupSize"),
    isGrouped: /* @__PURE__ */ __name(function isGrouped(column) {
      var field2 = this.columnProp(column, "field");
      if (this.groupRowsBy && field2) {
        if (Array.isArray(this.groupRowsBy)) return this.groupRowsBy.indexOf(field2) > -1;
        else return this.groupRowsBy === field2;
      } else {
        return false;
      }
    }, "isGrouped"),
    findIndexInSelection: /* @__PURE__ */ __name(function findIndexInSelection(data12) {
      return this.findIndex(data12, this.selection);
    }, "findIndexInSelection"),
    findIndex: /* @__PURE__ */ __name(function findIndex(data12, collection) {
      var index = -1;
      if (collection && collection.length) {
        for (var i = 0; i < collection.length; i++) {
          if (this.equals(data12, collection[i])) {
            index = i;
            break;
          }
        }
      }
      return index;
    }, "findIndex"),
    equals: /* @__PURE__ */ __name(function equals$1(data1, data22) {
      return this.compareSelectionBy === "equals" ? data1 === data22 : equals(data1, data22, this.dataKey);
    }, "equals$1"),
    onRowGroupToggle: /* @__PURE__ */ __name(function onRowGroupToggle(event2) {
      this.$emit("rowgroup-toggle", {
        originalEvent: event2,
        data: this.rowData
      });
    }, "onRowGroupToggle"),
    onRowClick: /* @__PURE__ */ __name(function onRowClick(event2) {
      this.$emit("row-click", {
        originalEvent: event2,
        data: this.rowData,
        index: this.rowIndex
      });
    }, "onRowClick"),
    onRowDblClick: /* @__PURE__ */ __name(function onRowDblClick(event2) {
      this.$emit("row-dblclick", {
        originalEvent: event2,
        data: this.rowData,
        index: this.rowIndex
      });
    }, "onRowDblClick"),
    onRowRightClick: /* @__PURE__ */ __name(function onRowRightClick(event2) {
      this.$emit("row-rightclick", {
        originalEvent: event2,
        data: this.rowData,
        index: this.rowIndex
      });
    }, "onRowRightClick"),
    onRowTouchEnd: /* @__PURE__ */ __name(function onRowTouchEnd(event2) {
      this.$emit("row-touchend", event2);
    }, "onRowTouchEnd"),
    onRowKeyDown: /* @__PURE__ */ __name(function onRowKeyDown(event2) {
      this.$emit("row-keydown", {
        originalEvent: event2,
        data: this.rowData,
        index: this.rowIndex
      });
    }, "onRowKeyDown"),
    onRowMouseDown: /* @__PURE__ */ __name(function onRowMouseDown(event2) {
      this.$emit("row-mousedown", event2);
    }, "onRowMouseDown"),
    onRowDragStart: /* @__PURE__ */ __name(function onRowDragStart(event2) {
      this.$emit("row-dragstart", {
        originalEvent: event2,
        index: this.rowIndex
      });
    }, "onRowDragStart"),
    onRowDragOver: /* @__PURE__ */ __name(function onRowDragOver(event2) {
      this.$emit("row-dragover", {
        originalEvent: event2,
        index: this.rowIndex
      });
    }, "onRowDragOver"),
    onRowDragLeave: /* @__PURE__ */ __name(function onRowDragLeave(event2) {
      this.$emit("row-dragleave", event2);
    }, "onRowDragLeave"),
    onRowDragEnd: /* @__PURE__ */ __name(function onRowDragEnd(event2) {
      this.$emit("row-dragend", event2);
    }, "onRowDragEnd"),
    onRowDrop: /* @__PURE__ */ __name(function onRowDrop(event2) {
      this.$emit("row-drop", event2);
    }, "onRowDrop"),
    onRowToggle: /* @__PURE__ */ __name(function onRowToggle(event2) {
      this.d_rowExpanded = !this.d_rowExpanded;
      this.$emit("row-toggle", _objectSpread$9(_objectSpread$9({}, event2), {}, {
        expanded: this.d_rowExpanded
      }));
    }, "onRowToggle"),
    onRadioChange: /* @__PURE__ */ __name(function onRadioChange(event2) {
      this.$emit("radio-change", event2);
    }, "onRadioChange"),
    onCheckboxChange: /* @__PURE__ */ __name(function onCheckboxChange(event2) {
      this.$emit("checkbox-change", event2);
    }, "onCheckboxChange"),
    onCellEditInit: /* @__PURE__ */ __name(function onCellEditInit(event2) {
      this.$emit("cell-edit-init", event2);
    }, "onCellEditInit"),
    onCellEditComplete: /* @__PURE__ */ __name(function onCellEditComplete(event2) {
      this.$emit("cell-edit-complete", event2);
    }, "onCellEditComplete"),
    onCellEditCancel: /* @__PURE__ */ __name(function onCellEditCancel(event2) {
      this.$emit("cell-edit-cancel", event2);
    }, "onCellEditCancel"),
    onRowEditInit: /* @__PURE__ */ __name(function onRowEditInit2(event2) {
      this.$emit("row-edit-init", event2);
    }, "onRowEditInit"),
    onRowEditSave: /* @__PURE__ */ __name(function onRowEditSave2(event2) {
      this.$emit("row-edit-save", event2);
    }, "onRowEditSave"),
    onRowEditCancel: /* @__PURE__ */ __name(function onRowEditCancel2(event2) {
      this.$emit("row-edit-cancel", event2);
    }, "onRowEditCancel"),
    onEditingMetaChange: /* @__PURE__ */ __name(function onEditingMetaChange(event2) {
      this.$emit("editing-meta-change", event2);
    }, "onEditingMetaChange"),
    getVirtualScrollerProp: /* @__PURE__ */ __name(function getVirtualScrollerProp2(option, options) {
      options = options || this.virtualScrollerContentProps;
      return options ? options[option] : null;
    }, "getVirtualScrollerProp")
  },
  computed: {
    rowIndex: /* @__PURE__ */ __name(function rowIndex() {
      var getItemOptions = this.getVirtualScrollerProp("getItemOptions");
      return getItemOptions ? getItemOptions(this.index).index : this.index;
    }, "rowIndex"),
    rowStyles: /* @__PURE__ */ __name(function rowStyles() {
      var _this$rowStyle;
      return (_this$rowStyle = this.rowStyle) === null || _this$rowStyle === void 0 ? void 0 : _this$rowStyle.call(this, this.rowData);
    }, "rowStyles"),
    rowClasses: /* @__PURE__ */ __name(function rowClasses() {
      var rowStyleClass = [];
      var columnSelectionMode = null;
      if (this.rowClass) {
        var rowClassValue = this.rowClass(this.rowData);
        if (rowClassValue) {
          rowStyleClass.push(rowClassValue);
        }
      }
      if (this.columns) {
        var _iterator = _createForOfIteratorHelper$2(this.columns), _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done; ) {
            var col = _step.value;
            var _selectionMode = this.columnProp(col, "selectionMode");
            if (isNotEmpty(_selectionMode)) {
              columnSelectionMode = _selectionMode;
              break;
            }
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
      }
      return [this.cx("row", {
        rowData: this.rowData,
        index: this.rowIndex,
        columnSelectionMode
      }), rowStyleClass];
    }, "rowClasses"),
    rowTabindex: /* @__PURE__ */ __name(function rowTabindex() {
      if (this.selection === null && (this.selectionMode === "single" || this.selectionMode === "multiple")) {
        return this.rowIndex === 0 ? 0 : -1;
      }
      return -1;
    }, "rowTabindex"),
    isRowEditing: /* @__PURE__ */ __name(function isRowEditing() {
      if (this.rowData && this.editingRows) {
        if (this.dataKey) return this.editingRowKeys ? this.editingRowKeys[resolveFieldData(this.rowData, this.dataKey)] !== void 0 : false;
        else return this.findIndex(this.rowData, this.editingRows) > -1;
      }
      return false;
    }, "isRowEditing"),
    isRowGroupExpanded: /* @__PURE__ */ __name(function isRowGroupExpanded() {
      if (this.expandableRowGroups && this.expandedRowGroups) {
        var groupFieldValue = resolveFieldData(this.rowData, this.groupRowsBy);
        return this.expandedRowGroups.indexOf(groupFieldValue) > -1;
      }
      return false;
    }, "isRowGroupExpanded"),
    isSelected: /* @__PURE__ */ __name(function isSelected() {
      if (this.rowData && this.selection) {
        if (this.dataKey) {
          return this.selectionKeys ? this.selectionKeys[resolveFieldData(this.rowData, this.dataKey)] !== void 0 : false;
        } else {
          if (this.selection instanceof Array) return this.findIndexInSelection(this.rowData) > -1;
          else return this.equals(this.rowData, this.selection);
        }
      }
      return false;
    }, "isSelected"),
    isSelectedWithContextMenu: /* @__PURE__ */ __name(function isSelectedWithContextMenu() {
      if (this.rowData && this.contextMenuSelection) {
        return this.equals(this.rowData, this.contextMenuSelection, this.dataKey);
      }
      return false;
    }, "isSelectedWithContextMenu"),
    shouldRenderRowGroupHeader: /* @__PURE__ */ __name(function shouldRenderRowGroupHeader() {
      var currentRowFieldData = resolveFieldData(this.rowData, this.groupRowsBy);
      var prevRowData = this.value[this.rowIndex - 1];
      if (prevRowData) {
        var previousRowFieldData = resolveFieldData(prevRowData, this.groupRowsBy);
        return currentRowFieldData !== previousRowFieldData;
      } else {
        return true;
      }
    }, "shouldRenderRowGroupHeader"),
    shouldRenderRowGroupFooter: /* @__PURE__ */ __name(function shouldRenderRowGroupFooter() {
      if (this.expandableRowGroups && !this.isRowGroupExpanded) {
        return false;
      } else {
        var currentRowFieldData = resolveFieldData(this.rowData, this.groupRowsBy);
        var nextRowData = this.value[this.rowIndex + 1];
        if (nextRowData) {
          var nextRowFieldData = resolveFieldData(nextRowData, this.groupRowsBy);
          return currentRowFieldData !== nextRowFieldData;
        } else {
          return true;
        }
      }
    }, "shouldRenderRowGroupFooter"),
    columnsLength: /* @__PURE__ */ __name(function columnsLength() {
      var _this2 = this;
      if (this.columns) {
        var hiddenColLength = 0;
        this.columns.forEach(function(column) {
          if (_this2.columnProp(column, "selectionMode") === "single") hiddenColLength--;
          if (_this2.columnProp(column, "hidden")) hiddenColLength++;
        });
        return this.columns.length - hiddenColLength;
      }
      return 0;
    }, "columnsLength")
  },
  components: {
    DTBodyCell: script$9,
    ChevronDownIcon: script$A,
    ChevronRightIcon: script$B
  }
};
function _typeof$8(o) {
  "@babel/helpers - typeof";
  return _typeof$8 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$8(o);
}
__name(_typeof$8, "_typeof$8");
function ownKeys$8(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
__name(ownKeys$8, "ownKeys$8");
function _objectSpread$8(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys$8(Object(t), true).forEach(function(r2) {
      _defineProperty$8(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$8(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
__name(_objectSpread$8, "_objectSpread$8");
function _defineProperty$8(e, r, t) {
  return (r = _toPropertyKey$8(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty$8, "_defineProperty$8");
function _toPropertyKey$8(t) {
  var i = _toPrimitive$8(t, "string");
  return "symbol" == _typeof$8(i) ? i : i + "";
}
__name(_toPropertyKey$8, "_toPropertyKey$8");
function _toPrimitive$8(t, r) {
  if ("object" != _typeof$8(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof$8(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive$8, "_toPrimitive$8");
var _hoisted_1$3 = ["colspan"];
var _hoisted_2$1 = ["tabindex", "aria-selected", "data-p-index", "data-p-selectable-row", "data-p-selected", "data-p-selected-contextmenu"];
var _hoisted_3 = ["id"];
var _hoisted_4 = ["colspan"];
var _hoisted_5 = ["colspan"];
var _hoisted_6 = ["colspan"];
function render$8(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_ChevronDownIcon = resolveComponent("ChevronDownIcon");
  var _component_ChevronRightIcon = resolveComponent("ChevronRightIcon");
  var _component_DTBodyCell = resolveComponent("DTBodyCell");
  return !$props.empty ? (openBlock(), createElementBlock(Fragment, {
    key: 0
  }, [$props.templates["groupheader"] && $props.rowGroupMode === "subheader" && $options.shouldRenderRowGroupHeader ? (openBlock(), createElementBlock("tr", mergeProps({
    key: 0,
    "class": _ctx.cx("rowGroupHeader"),
    style: $props.rowGroupHeaderStyle,
    role: "row"
  }, _ctx.ptm("rowGroupHeader")), [createBaseVNode("td", mergeProps({
    colspan: $options.columnsLength - 1
  }, _objectSpread$8(_objectSpread$8({}, $options.getColumnPT("bodycell")), _ctx.ptm("rowGroupHeaderCell"))), [$props.expandableRowGroups ? (openBlock(), createElementBlock("button", mergeProps({
    key: 0,
    "class": _ctx.cx("rowToggleButton"),
    onClick: _cache[0] || (_cache[0] = function() {
      return $options.onRowGroupToggle && $options.onRowGroupToggle.apply($options, arguments);
    }),
    type: "button"
  }, _ctx.ptm("rowToggleButton")), [$props.templates["rowtoggleicon"] || $props.templates["rowgrouptogglericon"] ? (openBlock(), createBlock(resolveDynamicComponent($props.templates["rowtoggleicon"] || $props.templates["rowgrouptogglericon"]), {
    key: 0,
    expanded: $options.isRowGroupExpanded
  }, null, 8, ["expanded"])) : (openBlock(), createElementBlock(Fragment, {
    key: 1
  }, [$options.isRowGroupExpanded && $props.expandedRowIcon ? (openBlock(), createElementBlock("span", mergeProps({
    key: 0,
    "class": [_ctx.cx("rowToggleIcon"), $props.expandedRowIcon]
  }, _ctx.ptm("rowToggleIcon")), null, 16)) : $options.isRowGroupExpanded && !$props.expandedRowIcon ? (openBlock(), createBlock(_component_ChevronDownIcon, mergeProps({
    key: 1,
    "class": _ctx.cx("rowToggleIcon")
  }, _ctx.ptm("rowToggleIcon")), null, 16, ["class"])) : !$options.isRowGroupExpanded && $props.collapsedRowIcon ? (openBlock(), createElementBlock("span", mergeProps({
    key: 2,
    "class": [_ctx.cx("rowToggleIcon"), $props.collapsedRowIcon]
  }, _ctx.ptm("rowToggleIcon")), null, 16)) : !$options.isRowGroupExpanded && !$props.collapsedRowIcon ? (openBlock(), createBlock(_component_ChevronRightIcon, mergeProps({
    key: 3,
    "class": _ctx.cx("rowToggleIcon")
  }, _ctx.ptm("rowToggleIcon")), null, 16, ["class"])) : createCommentVNode("", true)], 64))], 16)) : createCommentVNode("", true), (openBlock(), createBlock(resolveDynamicComponent($props.templates["groupheader"]), {
    data: $props.rowData,
    index: $options.rowIndex
  }, null, 8, ["data", "index"]))], 16, _hoisted_1$3)], 16)) : createCommentVNode("", true), ($props.expandableRowGroups ? $options.isRowGroupExpanded : true) ? (openBlock(), createElementBlock("tr", mergeProps({
    key: 1,
    "class": $options.rowClasses,
    style: $options.rowStyles,
    tabindex: $options.rowTabindex,
    role: "row",
    "aria-selected": $props.selectionMode ? $options.isSelected : null,
    onClick: _cache[1] || (_cache[1] = function() {
      return $options.onRowClick && $options.onRowClick.apply($options, arguments);
    }),
    onDblclick: _cache[2] || (_cache[2] = function() {
      return $options.onRowDblClick && $options.onRowDblClick.apply($options, arguments);
    }),
    onContextmenu: _cache[3] || (_cache[3] = function() {
      return $options.onRowRightClick && $options.onRowRightClick.apply($options, arguments);
    }),
    onTouchend: _cache[4] || (_cache[4] = function() {
      return $options.onRowTouchEnd && $options.onRowTouchEnd.apply($options, arguments);
    }),
    onKeydown: _cache[5] || (_cache[5] = withModifiers(function() {
      return $options.onRowKeyDown && $options.onRowKeyDown.apply($options, arguments);
    }, ["self"])),
    onMousedown: _cache[6] || (_cache[6] = function() {
      return $options.onRowMouseDown && $options.onRowMouseDown.apply($options, arguments);
    }),
    onDragstart: _cache[7] || (_cache[7] = function() {
      return $options.onRowDragStart && $options.onRowDragStart.apply($options, arguments);
    }),
    onDragover: _cache[8] || (_cache[8] = function() {
      return $options.onRowDragOver && $options.onRowDragOver.apply($options, arguments);
    }),
    onDragleave: _cache[9] || (_cache[9] = function() {
      return $options.onRowDragLeave && $options.onRowDragLeave.apply($options, arguments);
    }),
    onDragend: _cache[10] || (_cache[10] = function() {
      return $options.onRowDragEnd && $options.onRowDragEnd.apply($options, arguments);
    }),
    onDrop: _cache[11] || (_cache[11] = function() {
      return $options.onRowDrop && $options.onRowDrop.apply($options, arguments);
    })
  }, $options.getBodyRowPTOptions("bodyRow"), {
    "data-p-index": $options.rowIndex,
    "data-p-selectable-row": $props.selectionMode ? true : false,
    "data-p-selected": $props.selection && $options.isSelected,
    "data-p-selected-contextmenu": $props.contextMenuSelection && $options.isSelectedWithContextMenu
  }), [(openBlock(true), createElementBlock(Fragment, null, renderList($props.columns, function(col, i) {
    return openBlock(), createElementBlock(Fragment, null, [$options.shouldRenderBodyCell(col) ? (openBlock(), createBlock(_component_DTBodyCell, {
      key: $options.columnProp(col, "columnKey") || $options.columnProp(col, "field") || i,
      rowData: $props.rowData,
      column: col,
      rowIndex: $options.rowIndex,
      index: i,
      selected: $options.isSelected,
      frozenRow: $props.frozenRow,
      rowspan: $props.rowGroupMode === "rowspan" ? $options.calculateRowGroupSize(col) : null,
      editMode: $props.editMode,
      editing: $props.editMode === "row" && $options.isRowEditing,
      editingMeta: $props.editingMeta,
      virtualScrollerContentProps: $props.virtualScrollerContentProps,
      ariaControls: $props.expandedRowId + "_" + $options.rowIndex + "_expansion",
      name: $props.nameAttributeSelector,
      isRowExpanded: $data.d_rowExpanded,
      expandedRowIcon: $props.expandedRowIcon,
      collapsedRowIcon: $props.collapsedRowIcon,
      editButtonProps: $props.editButtonProps,
      onRadioChange: $options.onRadioChange,
      onCheckboxChange: $options.onCheckboxChange,
      onRowToggle: $options.onRowToggle,
      onCellEditInit: $options.onCellEditInit,
      onCellEditComplete: $options.onCellEditComplete,
      onCellEditCancel: $options.onCellEditCancel,
      onRowEditInit: $options.onRowEditInit,
      onRowEditSave: $options.onRowEditSave,
      onRowEditCancel: $options.onRowEditCancel,
      onEditingMetaChange: $options.onEditingMetaChange,
      unstyled: _ctx.unstyled,
      pt: _ctx.pt
    }, null, 8, ["rowData", "column", "rowIndex", "index", "selected", "frozenRow", "rowspan", "editMode", "editing", "editingMeta", "virtualScrollerContentProps", "ariaControls", "name", "isRowExpanded", "expandedRowIcon", "collapsedRowIcon", "editButtonProps", "onRadioChange", "onCheckboxChange", "onRowToggle", "onCellEditInit", "onCellEditComplete", "onCellEditCancel", "onRowEditInit", "onRowEditSave", "onRowEditCancel", "onEditingMetaChange", "unstyled", "pt"])) : createCommentVNode("", true)], 64);
  }), 256))], 16, _hoisted_2$1)) : createCommentVNode("", true), $props.templates["expansion"] && $props.expandedRows && $data.d_rowExpanded ? (openBlock(), createElementBlock("tr", mergeProps({
    key: 2,
    id: $props.expandedRowId + "_" + $options.rowIndex + "_expansion",
    "class": _ctx.cx("rowExpansion"),
    role: "row"
  }, _ctx.ptm("rowExpansion")), [createBaseVNode("td", mergeProps({
    colspan: $options.columnsLength
  }, _objectSpread$8(_objectSpread$8({}, $options.getColumnPT("bodycell")), _ctx.ptm("rowExpansionCell"))), [(openBlock(), createBlock(resolveDynamicComponent($props.templates["expansion"]), {
    data: $props.rowData,
    index: $options.rowIndex
  }, null, 8, ["data", "index"]))], 16, _hoisted_4)], 16, _hoisted_3)) : createCommentVNode("", true), $props.templates["groupfooter"] && $props.rowGroupMode === "subheader" && $options.shouldRenderRowGroupFooter ? (openBlock(), createElementBlock("tr", mergeProps({
    key: 3,
    "class": _ctx.cx("rowGroupFooter"),
    role: "row"
  }, _ctx.ptm("rowGroupFooter")), [createBaseVNode("td", mergeProps({
    colspan: $options.columnsLength - 1
  }, _objectSpread$8(_objectSpread$8({}, $options.getColumnPT("bodycell")), _ctx.ptm("rowGroupFooterCell"))), [(openBlock(), createBlock(resolveDynamicComponent($props.templates["groupfooter"]), {
    data: $props.rowData,
    index: $options.rowIndex
  }, null, 8, ["data", "index"]))], 16, _hoisted_5)], 16)) : createCommentVNode("", true)], 64)) : (openBlock(), createElementBlock("tr", mergeProps({
    key: 1,
    "class": _ctx.cx("emptyMessage"),
    role: "row"
  }, _ctx.ptm("emptyMessage")), [createBaseVNode("td", mergeProps({
    colspan: $options.columnsLength
  }, _objectSpread$8(_objectSpread$8({}, $options.getColumnPT("bodycell")), _ctx.ptm("emptyMessageCell"))), [$props.templates.empty ? (openBlock(), createBlock(resolveDynamicComponent($props.templates.empty), {
    key: 0
  })) : createCommentVNode("", true)], 16, _hoisted_6)], 16));
}
__name(render$8, "render$8");
script$8.render = render$8;
var script$7 = {
  name: "TableBody",
  hostName: "DataTable",
  "extends": script$s,
  emits: ["rowgroup-toggle", "row-click", "row-dblclick", "row-rightclick", "row-touchend", "row-keydown", "row-mousedown", "row-dragstart", "row-dragover", "row-dragleave", "row-dragend", "row-drop", "row-toggle", "radio-change", "checkbox-change", "cell-edit-init", "cell-edit-complete", "cell-edit-cancel", "row-edit-init", "row-edit-save", "row-edit-cancel", "editing-meta-change"],
  props: {
    value: {
      type: Array,
      "default": null
    },
    columns: {
      type: null,
      "default": null
    },
    frozenRow: {
      type: Boolean,
      "default": false
    },
    empty: {
      type: Boolean,
      "default": false
    },
    rowGroupMode: {
      type: String,
      "default": null
    },
    groupRowsBy: {
      type: [Array, String, Function],
      "default": null
    },
    expandableRowGroups: {
      type: Boolean,
      "default": false
    },
    expandedRowGroups: {
      type: Array,
      "default": null
    },
    first: {
      type: Number,
      "default": 0
    },
    dataKey: {
      type: [String, Function],
      "default": null
    },
    expandedRowIcon: {
      type: String,
      "default": null
    },
    collapsedRowIcon: {
      type: String,
      "default": null
    },
    expandedRows: {
      type: [Array, Object],
      "default": null
    },
    selection: {
      type: [Array, Object],
      "default": null
    },
    selectionKeys: {
      type: null,
      "default": null
    },
    selectionMode: {
      type: String,
      "default": null
    },
    contextMenu: {
      type: Boolean,
      "default": false
    },
    contextMenuSelection: {
      type: Object,
      "default": null
    },
    rowClass: {
      type: null,
      "default": null
    },
    rowStyle: {
      type: null,
      "default": null
    },
    editMode: {
      type: String,
      "default": null
    },
    compareSelectionBy: {
      type: String,
      "default": "deepEquals"
    },
    editingRows: {
      type: Array,
      "default": null
    },
    editingRowKeys: {
      type: null,
      "default": null
    },
    editingMeta: {
      type: Object,
      "default": null
    },
    templates: {
      type: null,
      "default": null
    },
    scrollable: {
      type: Boolean,
      "default": false
    },
    editButtonProps: {
      type: Object,
      "default": null
    },
    virtualScrollerContentProps: {
      type: Object,
      "default": null
    },
    isVirtualScrollerDisabled: {
      type: Boolean,
      "default": false
    }
  },
  data: /* @__PURE__ */ __name(function data5() {
    return {
      rowGroupHeaderStyleObject: {}
    };
  }, "data"),
  mounted: /* @__PURE__ */ __name(function mounted4() {
    if (this.frozenRow) {
      this.updateFrozenRowStickyPosition();
    }
    if (this.scrollable && this.rowGroupMode === "subheader") {
      this.updateFrozenRowGroupHeaderStickyPosition();
    }
  }, "mounted"),
  updated: /* @__PURE__ */ __name(function updated2() {
    if (this.frozenRow) {
      this.updateFrozenRowStickyPosition();
    }
    if (this.scrollable && this.rowGroupMode === "subheader") {
      this.updateFrozenRowGroupHeaderStickyPosition();
    }
  }, "updated"),
  methods: {
    getRowKey: /* @__PURE__ */ __name(function getRowKey(rowData, rowIndex2) {
      return this.dataKey ? resolveFieldData(rowData, this.dataKey) : rowIndex2;
    }, "getRowKey"),
    updateFrozenRowStickyPosition: /* @__PURE__ */ __name(function updateFrozenRowStickyPosition() {
      this.$el.style.top = getOuterHeight(this.$el.previousElementSibling) + "px";
    }, "updateFrozenRowStickyPosition"),
    updateFrozenRowGroupHeaderStickyPosition: /* @__PURE__ */ __name(function updateFrozenRowGroupHeaderStickyPosition() {
      var tableHeaderHeight = getOuterHeight(this.$el.previousElementSibling);
      this.rowGroupHeaderStyleObject.top = tableHeaderHeight + "px";
    }, "updateFrozenRowGroupHeaderStickyPosition"),
    getVirtualScrollerProp: /* @__PURE__ */ __name(function getVirtualScrollerProp3(option, options) {
      options = options || this.virtualScrollerContentProps;
      return options ? options[option] : null;
    }, "getVirtualScrollerProp"),
    bodyRef: /* @__PURE__ */ __name(function bodyRef(el) {
      var contentRef = this.getVirtualScrollerProp("contentRef");
      contentRef && contentRef(el);
    }, "bodyRef")
  },
  computed: {
    rowGroupHeaderStyle: /* @__PURE__ */ __name(function rowGroupHeaderStyle() {
      if (this.scrollable) {
        return {
          top: this.rowGroupHeaderStyleObject.top
        };
      }
      return null;
    }, "rowGroupHeaderStyle"),
    bodyContentStyle: /* @__PURE__ */ __name(function bodyContentStyle() {
      return this.getVirtualScrollerProp("contentStyle");
    }, "bodyContentStyle"),
    ptmTBodyOptions: /* @__PURE__ */ __name(function ptmTBodyOptions() {
      var _this$$parentInstance;
      return {
        context: {
          scrollable: (_this$$parentInstance = this.$parentInstance) === null || _this$$parentInstance === void 0 || (_this$$parentInstance = _this$$parentInstance.$parentInstance) === null || _this$$parentInstance === void 0 ? void 0 : _this$$parentInstance.scrollable
        }
      };
    }, "ptmTBodyOptions"),
    expandedRowId: /* @__PURE__ */ __name(function expandedRowId() {
      return UniqueComponentId();
    }, "expandedRowId"),
    nameAttributeSelector: /* @__PURE__ */ __name(function nameAttributeSelector() {
      return UniqueComponentId();
    }, "nameAttributeSelector")
  },
  components: {
    DTBodyRow: script$8
  }
};
function render$7(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_DTBodyRow = resolveComponent("DTBodyRow");
  return openBlock(), createElementBlock("tbody", mergeProps({
    ref: $options.bodyRef,
    "class": _ctx.cx("tbody"),
    role: "rowgroup",
    style: $options.bodyContentStyle
  }, _ctx.ptm("tbody", $options.ptmTBodyOptions)), [!$props.empty ? (openBlock(true), createElementBlock(Fragment, {
    key: 0
  }, renderList($props.value, function(rowData, rowIndex2) {
    return openBlock(), createBlock(_component_DTBodyRow, {
      key: $options.getRowKey(rowData, rowIndex2),
      rowData,
      index: rowIndex2,
      value: $props.value,
      columns: $props.columns,
      frozenRow: $props.frozenRow,
      empty: $props.empty,
      first: $props.first,
      dataKey: $props.dataKey,
      selection: $props.selection,
      selectionKeys: $props.selectionKeys,
      selectionMode: $props.selectionMode,
      contextMenu: $props.contextMenu,
      contextMenuSelection: $props.contextMenuSelection,
      rowGroupMode: $props.rowGroupMode,
      groupRowsBy: $props.groupRowsBy,
      expandableRowGroups: $props.expandableRowGroups,
      rowClass: $props.rowClass,
      rowStyle: $props.rowStyle,
      editMode: $props.editMode,
      compareSelectionBy: $props.compareSelectionBy,
      scrollable: $props.scrollable,
      expandedRowIcon: $props.expandedRowIcon,
      collapsedRowIcon: $props.collapsedRowIcon,
      expandedRows: $props.expandedRows,
      expandedRowGroups: $props.expandedRowGroups,
      editingRows: $props.editingRows,
      editingRowKeys: $props.editingRowKeys,
      templates: $props.templates,
      editButtonProps: $props.editButtonProps,
      virtualScrollerContentProps: $props.virtualScrollerContentProps,
      isVirtualScrollerDisabled: $props.isVirtualScrollerDisabled,
      editingMeta: $props.editingMeta,
      rowGroupHeaderStyle: $options.rowGroupHeaderStyle,
      expandedRowId: $options.expandedRowId,
      nameAttributeSelector: $options.nameAttributeSelector,
      onRowgroupToggle: _cache[0] || (_cache[0] = function($event) {
        return _ctx.$emit("rowgroup-toggle", $event);
      }),
      onRowClick: _cache[1] || (_cache[1] = function($event) {
        return _ctx.$emit("row-click", $event);
      }),
      onRowDblclick: _cache[2] || (_cache[2] = function($event) {
        return _ctx.$emit("row-dblclick", $event);
      }),
      onRowRightclick: _cache[3] || (_cache[3] = function($event) {
        return _ctx.$emit("row-rightclick", $event);
      }),
      onRowTouchend: _cache[4] || (_cache[4] = function($event) {
        return _ctx.$emit("row-touchend", $event);
      }),
      onRowKeydown: _cache[5] || (_cache[5] = function($event) {
        return _ctx.$emit("row-keydown", $event);
      }),
      onRowMousedown: _cache[6] || (_cache[6] = function($event) {
        return _ctx.$emit("row-mousedown", $event);
      }),
      onRowDragstart: _cache[7] || (_cache[7] = function($event) {
        return _ctx.$emit("row-dragstart", $event);
      }),
      onRowDragover: _cache[8] || (_cache[8] = function($event) {
        return _ctx.$emit("row-dragover", $event);
      }),
      onRowDragleave: _cache[9] || (_cache[9] = function($event) {
        return _ctx.$emit("row-dragleave", $event);
      }),
      onRowDragend: _cache[10] || (_cache[10] = function($event) {
        return _ctx.$emit("row-dragend", $event);
      }),
      onRowDrop: _cache[11] || (_cache[11] = function($event) {
        return _ctx.$emit("row-drop", $event);
      }),
      onRowToggle: _cache[12] || (_cache[12] = function($event) {
        return _ctx.$emit("row-toggle", $event);
      }),
      onRadioChange: _cache[13] || (_cache[13] = function($event) {
        return _ctx.$emit("radio-change", $event);
      }),
      onCheckboxChange: _cache[14] || (_cache[14] = function($event) {
        return _ctx.$emit("checkbox-change", $event);
      }),
      onCellEditInit: _cache[15] || (_cache[15] = function($event) {
        return _ctx.$emit("cell-edit-init", $event);
      }),
      onCellEditComplete: _cache[16] || (_cache[16] = function($event) {
        return _ctx.$emit("cell-edit-complete", $event);
      }),
      onCellEditCancel: _cache[17] || (_cache[17] = function($event) {
        return _ctx.$emit("cell-edit-cancel", $event);
      }),
      onRowEditInit: _cache[18] || (_cache[18] = function($event) {
        return _ctx.$emit("row-edit-init", $event);
      }),
      onRowEditSave: _cache[19] || (_cache[19] = function($event) {
        return _ctx.$emit("row-edit-save", $event);
      }),
      onRowEditCancel: _cache[20] || (_cache[20] = function($event) {
        return _ctx.$emit("row-edit-cancel", $event);
      }),
      onEditingMetaChange: _cache[21] || (_cache[21] = function($event) {
        return _ctx.$emit("editing-meta-change", $event);
      }),
      unstyled: _ctx.unstyled,
      pt: _ctx.pt
    }, null, 8, ["rowData", "index", "value", "columns", "frozenRow", "empty", "first", "dataKey", "selection", "selectionKeys", "selectionMode", "contextMenu", "contextMenuSelection", "rowGroupMode", "groupRowsBy", "expandableRowGroups", "rowClass", "rowStyle", "editMode", "compareSelectionBy", "scrollable", "expandedRowIcon", "collapsedRowIcon", "expandedRows", "expandedRowGroups", "editingRows", "editingRowKeys", "templates", "editButtonProps", "virtualScrollerContentProps", "isVirtualScrollerDisabled", "editingMeta", "rowGroupHeaderStyle", "expandedRowId", "nameAttributeSelector", "unstyled", "pt"]);
  }), 128)) : (openBlock(), createBlock(_component_DTBodyRow, {
    key: 1,
    empty: $props.empty,
    columns: $props.columns,
    templates: $props.templates
  }, null, 8, ["empty", "columns", "templates"]))], 16);
}
__name(render$7, "render$7");
script$7.render = render$7;
var script$6 = {
  name: "FooterCell",
  hostName: "DataTable",
  "extends": script$s,
  props: {
    column: {
      type: Object,
      "default": null
    },
    index: {
      type: Number,
      "default": null
    }
  },
  data: /* @__PURE__ */ __name(function data6() {
    return {
      styleObject: {}
    };
  }, "data"),
  mounted: /* @__PURE__ */ __name(function mounted5() {
    if (this.columnProp("frozen")) {
      this.updateStickyPosition();
    }
  }, "mounted"),
  updated: /* @__PURE__ */ __name(function updated3() {
    if (this.columnProp("frozen")) {
      this.updateStickyPosition();
    }
  }, "updated"),
  methods: {
    columnProp: /* @__PURE__ */ __name(function columnProp3(prop) {
      return getVNodeProp(this.column, prop);
    }, "columnProp"),
    getColumnPT: /* @__PURE__ */ __name(function getColumnPT5(key) {
      var _this$$parentInstance, _this$$parentInstance2;
      var columnMetaData = {
        props: this.column.props,
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          index: this.index,
          size: (_this$$parentInstance = this.$parentInstance) === null || _this$$parentInstance === void 0 || (_this$$parentInstance = _this$$parentInstance.$parentInstance) === null || _this$$parentInstance === void 0 ? void 0 : _this$$parentInstance.size,
          showGridlines: ((_this$$parentInstance2 = this.$parentInstance) === null || _this$$parentInstance2 === void 0 || (_this$$parentInstance2 = _this$$parentInstance2.$parentInstance) === null || _this$$parentInstance2 === void 0 ? void 0 : _this$$parentInstance2.showGridlines) || false
        }
      };
      return mergeProps(this.ptm("column.".concat(key), {
        column: columnMetaData
      }), this.ptm("column.".concat(key), columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));
    }, "getColumnPT"),
    getColumnProp: /* @__PURE__ */ __name(function getColumnProp4() {
      return this.column.props && this.column.props.pt ? this.column.props.pt : void 0;
    }, "getColumnProp"),
    updateStickyPosition: /* @__PURE__ */ __name(function updateStickyPosition2() {
      if (this.columnProp("frozen")) {
        var align = this.columnProp("alignFrozen");
        if (align === "right") {
          var right = 0;
          var next2 = getNextElementSibling(this.$el, '[data-p-frozen-column="true"]');
          if (next2) {
            right = getOuterWidth(next2) + parseFloat(next2.style.right || 0);
          }
          this.styleObject.right = right + "px";
        } else {
          var left = 0;
          var prev2 = getPreviousElementSibling(this.$el, '[data-p-frozen-column="true"]');
          if (prev2) {
            left = getOuterWidth(prev2) + parseFloat(prev2.style.left || 0);
          }
          this.styleObject.left = left + "px";
        }
      }
    }, "updateStickyPosition")
  },
  computed: {
    containerClass: /* @__PURE__ */ __name(function containerClass2() {
      return [this.columnProp("footerClass"), this.columnProp("class"), this.cx("footerCell")];
    }, "containerClass"),
    containerStyle: /* @__PURE__ */ __name(function containerStyle2() {
      var bodyStyle = this.columnProp("footerStyle");
      var columnStyle = this.columnProp("style");
      return this.columnProp("frozen") ? [columnStyle, bodyStyle, this.styleObject] : [columnStyle, bodyStyle];
    }, "containerStyle")
  }
};
function _typeof$7(o) {
  "@babel/helpers - typeof";
  return _typeof$7 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$7(o);
}
__name(_typeof$7, "_typeof$7");
function ownKeys$7(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
__name(ownKeys$7, "ownKeys$7");
function _objectSpread$7(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys$7(Object(t), true).forEach(function(r2) {
      _defineProperty$7(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$7(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
__name(_objectSpread$7, "_objectSpread$7");
function _defineProperty$7(e, r, t) {
  return (r = _toPropertyKey$7(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty$7, "_defineProperty$7");
function _toPropertyKey$7(t) {
  var i = _toPrimitive$7(t, "string");
  return "symbol" == _typeof$7(i) ? i : i + "";
}
__name(_toPropertyKey$7, "_toPropertyKey$7");
function _toPrimitive$7(t, r) {
  if ("object" != _typeof$7(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof$7(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive$7, "_toPrimitive$7");
var _hoisted_1$2 = ["colspan", "rowspan", "data-p-frozen-column"];
function render$6(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("td", mergeProps({
    style: $options.containerStyle,
    "class": $options.containerClass,
    role: "cell",
    colspan: $options.columnProp("colspan"),
    rowspan: $options.columnProp("rowspan")
  }, _objectSpread$7(_objectSpread$7({}, $options.getColumnPT("root")), $options.getColumnPT("footerCell")), {
    "data-p-frozen-column": $options.columnProp("frozen")
  }), [$props.column.children && $props.column.children.footer ? (openBlock(), createBlock(resolveDynamicComponent($props.column.children.footer), {
    key: 0,
    column: $props.column
  }, null, 8, ["column"])) : createCommentVNode("", true), $options.columnProp("footer") ? (openBlock(), createElementBlock("span", mergeProps({
    key: 1,
    "class": _ctx.cx("columnFooter")
  }, $options.getColumnPT("columnFooter")), toDisplayString($options.columnProp("footer")), 17)) : createCommentVNode("", true)], 16, _hoisted_1$2);
}
__name(render$6, "render$6");
script$6.render = render$6;
function _createForOfIteratorHelper$1(r, e) {
  var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
  if (!t) {
    if (Array.isArray(r) || (t = _unsupportedIterableToArray$1(r)) || e) {
      t && (r = t);
      var _n = 0, F = /* @__PURE__ */ __name(function F2() {
      }, "F");
      return { s: F, n: /* @__PURE__ */ __name(function n() {
        return _n >= r.length ? { done: true } : { done: false, value: r[_n++] };
      }, "n"), e: /* @__PURE__ */ __name(function e2(r2) {
        throw r2;
      }, "e"), f: F };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var o, a = true, u = false;
  return { s: /* @__PURE__ */ __name(function s() {
    t = t.call(r);
  }, "s"), n: /* @__PURE__ */ __name(function n() {
    var r2 = t.next();
    return a = r2.done, r2;
  }, "n"), e: /* @__PURE__ */ __name(function e2(r2) {
    u = true, o = r2;
  }, "e"), f: /* @__PURE__ */ __name(function f() {
    try {
      a || null == t["return"] || t["return"]();
    } finally {
      if (u) throw o;
    }
  }, "f") };
}
__name(_createForOfIteratorHelper$1, "_createForOfIteratorHelper$1");
function _unsupportedIterableToArray$1(r, a) {
  if (r) {
    if ("string" == typeof r) return _arrayLikeToArray$1(r, a);
    var t = {}.toString.call(r).slice(8, -1);
    return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray$1(r, a) : void 0;
  }
}
__name(_unsupportedIterableToArray$1, "_unsupportedIterableToArray$1");
function _arrayLikeToArray$1(r, a) {
  (null == a || a > r.length) && (a = r.length);
  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
  return n;
}
__name(_arrayLikeToArray$1, "_arrayLikeToArray$1");
var script$5 = {
  name: "TableFooter",
  hostName: "DataTable",
  "extends": script$s,
  props: {
    columnGroup: {
      type: null,
      "default": null
    },
    columns: {
      type: Object,
      "default": null
    }
  },
  provide: /* @__PURE__ */ __name(function provide5() {
    return {
      $rows: this.d_footerRows,
      $columns: this.d_footerColumns
    };
  }, "provide"),
  data: /* @__PURE__ */ __name(function data7() {
    return {
      d_footerRows: new _default({
        type: "Row"
      }),
      d_footerColumns: new _default({
        type: "Column"
      })
    };
  }, "data"),
  beforeUnmount: /* @__PURE__ */ __name(function beforeUnmount2() {
    this.d_footerRows.clear();
    this.d_footerColumns.clear();
  }, "beforeUnmount"),
  methods: {
    columnProp: /* @__PURE__ */ __name(function columnProp4(col, prop) {
      return getVNodeProp(col, prop);
    }, "columnProp"),
    getColumnGroupPT: /* @__PURE__ */ __name(function getColumnGroupPT(key) {
      var columnGroupMetaData = {
        props: this.getColumnGroupProps(),
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          type: "footer",
          scrollable: this.ptmTFootOptions.context.scrollable
        }
      };
      return mergeProps(this.ptm("columnGroup.".concat(key), {
        columnGroup: columnGroupMetaData
      }), this.ptm("columnGroup.".concat(key), columnGroupMetaData), this.ptmo(this.getColumnGroupProps(), key, columnGroupMetaData));
    }, "getColumnGroupPT"),
    getColumnGroupProps: /* @__PURE__ */ __name(function getColumnGroupProps() {
      return this.columnGroup && this.columnGroup.props && this.columnGroup.props.pt ? this.columnGroup.props.pt : void 0;
    }, "getColumnGroupProps"),
    getRowPT: /* @__PURE__ */ __name(function getRowPT(row2, key, index) {
      var rowMetaData = {
        props: row2.props,
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          index
        }
      };
      return mergeProps(this.ptm("row.".concat(key), {
        row: rowMetaData
      }), this.ptm("row.".concat(key), rowMetaData), this.ptmo(this.getRowProp(row2), key, rowMetaData));
    }, "getRowPT"),
    getRowProp: /* @__PURE__ */ __name(function getRowProp(row2) {
      return row2.props && row2.props.pt ? row2.props.pt : void 0;
    }, "getRowProp"),
    getFooterRows: /* @__PURE__ */ __name(function getFooterRows() {
      var _this$d_footerRows;
      return (_this$d_footerRows = this.d_footerRows) === null || _this$d_footerRows === void 0 ? void 0 : _this$d_footerRows.get(this.columnGroup, this.columnGroup.children);
    }, "getFooterRows"),
    getFooterColumns: /* @__PURE__ */ __name(function getFooterColumns(row2) {
      var _this$d_footerColumns;
      return (_this$d_footerColumns = this.d_footerColumns) === null || _this$d_footerColumns === void 0 ? void 0 : _this$d_footerColumns.get(row2, row2.children);
    }, "getFooterColumns")
  },
  computed: {
    hasFooter: /* @__PURE__ */ __name(function hasFooter() {
      var hasFooter2 = false;
      if (this.columnGroup) {
        hasFooter2 = true;
      } else if (this.columns) {
        var _iterator = _createForOfIteratorHelper$1(this.columns), _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done; ) {
            var col = _step.value;
            if (this.columnProp(col, "footer") || col.children && col.children.footer) {
              hasFooter2 = true;
              break;
            }
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
      }
      return hasFooter2;
    }, "hasFooter"),
    ptmTFootOptions: /* @__PURE__ */ __name(function ptmTFootOptions() {
      var _this$$parentInstance;
      return {
        context: {
          scrollable: (_this$$parentInstance = this.$parentInstance) === null || _this$$parentInstance === void 0 || (_this$$parentInstance = _this$$parentInstance.$parentInstance) === null || _this$$parentInstance === void 0 ? void 0 : _this$$parentInstance.scrollable
        }
      };
    }, "ptmTFootOptions")
  },
  components: {
    DTFooterCell: script$6
  }
};
function _typeof$6(o) {
  "@babel/helpers - typeof";
  return _typeof$6 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$6(o);
}
__name(_typeof$6, "_typeof$6");
function ownKeys$6(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
__name(ownKeys$6, "ownKeys$6");
function _objectSpread$6(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys$6(Object(t), true).forEach(function(r2) {
      _defineProperty$6(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$6(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
__name(_objectSpread$6, "_objectSpread$6");
function _defineProperty$6(e, r, t) {
  return (r = _toPropertyKey$6(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty$6, "_defineProperty$6");
function _toPropertyKey$6(t) {
  var i = _toPrimitive$6(t, "string");
  return "symbol" == _typeof$6(i) ? i : i + "";
}
__name(_toPropertyKey$6, "_toPropertyKey$6");
function _toPrimitive$6(t, r) {
  if ("object" != _typeof$6(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof$6(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive$6, "_toPrimitive$6");
function render$5(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_DTFooterCell = resolveComponent("DTFooterCell");
  return $options.hasFooter ? (openBlock(), createElementBlock("tfoot", mergeProps({
    key: 0,
    "class": _ctx.cx("tfoot"),
    style: _ctx.sx("tfoot"),
    role: "rowgroup"
  }, $props.columnGroup ? _objectSpread$6(_objectSpread$6({}, _ctx.ptm("tfoot", $options.ptmTFootOptions)), $options.getColumnGroupPT("root")) : _ctx.ptm("tfoot", $options.ptmTFootOptions), {
    "data-pc-section": "tfoot"
  }), [!$props.columnGroup ? (openBlock(), createElementBlock("tr", mergeProps({
    key: 0,
    role: "row"
  }, _ctx.ptm("footerRow")), [(openBlock(true), createElementBlock(Fragment, null, renderList($props.columns, function(col, i) {
    return openBlock(), createElementBlock(Fragment, {
      key: $options.columnProp(col, "columnKey") || $options.columnProp(col, "field") || i
    }, [!$options.columnProp(col, "hidden") ? (openBlock(), createBlock(_component_DTFooterCell, {
      key: 0,
      column: col,
      pt: _ctx.pt
    }, null, 8, ["column", "pt"])) : createCommentVNode("", true)], 64);
  }), 128))], 16)) : (openBlock(true), createElementBlock(Fragment, {
    key: 1
  }, renderList($options.getFooterRows(), function(row2, i) {
    return openBlock(), createElementBlock("tr", mergeProps({
      key: i,
      role: "row",
      ref_for: true
    }, _objectSpread$6(_objectSpread$6({}, _ctx.ptm("footerRow")), $options.getRowPT(row2, "root", i))), [(openBlock(true), createElementBlock(Fragment, null, renderList($options.getFooterColumns(row2), function(col, j) {
      return openBlock(), createElementBlock(Fragment, {
        key: $options.columnProp(col, "columnKey") || $options.columnProp(col, "field") || j
      }, [!$options.columnProp(col, "hidden") ? (openBlock(), createBlock(_component_DTFooterCell, {
        key: 0,
        column: col,
        index: i,
        pt: _ctx.pt
      }, null, 8, ["column", "index", "pt"])) : createCommentVNode("", true)], 64);
    }), 128))], 16);
  }), 128))], 16)) : createCommentVNode("", true);
}
__name(render$5, "render$5");
script$5.render = render$5;
function _typeof$5(o) {
  "@babel/helpers - typeof";
  return _typeof$5 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$5(o);
}
__name(_typeof$5, "_typeof$5");
function ownKeys$5(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
__name(ownKeys$5, "ownKeys$5");
function _objectSpread$5(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys$5(Object(t), true).forEach(function(r2) {
      _defineProperty$5(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$5(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
__name(_objectSpread$5, "_objectSpread$5");
function _defineProperty$5(e, r, t) {
  return (r = _toPropertyKey$5(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty$5, "_defineProperty$5");
function _toPropertyKey$5(t) {
  var i = _toPrimitive$5(t, "string");
  return "symbol" == _typeof$5(i) ? i : i + "";
}
__name(_toPropertyKey$5, "_toPropertyKey$5");
function _toPrimitive$5(t, r) {
  if ("object" != _typeof$5(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof$5(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive$5, "_toPrimitive$5");
var script$4 = {
  name: "ColumnFilter",
  hostName: "DataTable",
  "extends": script$s,
  emits: ["filter-change", "filter-apply", "operator-change", "matchmode-change", "constraint-add", "constraint-remove", "filter-clear", "apply-click"],
  props: {
    field: {
      type: String,
      "default": null
    },
    type: {
      type: String,
      "default": "text"
    },
    display: {
      type: String,
      "default": null
    },
    showMenu: {
      type: Boolean,
      "default": true
    },
    matchMode: {
      type: String,
      "default": null
    },
    showOperator: {
      type: Boolean,
      "default": true
    },
    showClearButton: {
      type: Boolean,
      "default": true
    },
    showApplyButton: {
      type: Boolean,
      "default": true
    },
    showMatchModes: {
      type: Boolean,
      "default": true
    },
    showAddButton: {
      type: Boolean,
      "default": true
    },
    matchModeOptions: {
      type: Array,
      "default": null
    },
    maxConstraints: {
      type: Number,
      "default": 2
    },
    filterElement: {
      type: Function,
      "default": null
    },
    filterHeaderTemplate: {
      type: Function,
      "default": null
    },
    filterFooterTemplate: {
      type: Function,
      "default": null
    },
    filterClearTemplate: {
      type: Function,
      "default": null
    },
    filterApplyTemplate: {
      type: Function,
      "default": null
    },
    filterIconTemplate: {
      type: Function,
      "default": null
    },
    filterAddIconTemplate: {
      type: Function,
      "default": null
    },
    filterRemoveIconTemplate: {
      type: Function,
      "default": null
    },
    filterClearIconTemplate: {
      type: Function,
      "default": null
    },
    filters: {
      type: Object,
      "default": null
    },
    filtersStore: {
      type: Object,
      "default": null
    },
    filterMenuClass: {
      type: String,
      "default": null
    },
    filterMenuStyle: {
      type: null,
      "default": null
    },
    filterInputProps: {
      type: null,
      "default": null
    },
    filterButtonProps: {
      type: null,
      "default": null
    },
    column: null
  },
  data: /* @__PURE__ */ __name(function data8() {
    return {
      id: this.$attrs.id,
      overlayVisible: false,
      defaultMatchMode: null,
      defaultOperator: null
    };
  }, "data"),
  watch: {
    "$attrs.id": /* @__PURE__ */ __name(function $attrsId(newValue) {
      this.id = newValue || UniqueComponentId();
    }, "$attrsId")
  },
  overlay: null,
  selfClick: false,
  overlayEventListener: null,
  beforeUnmount: /* @__PURE__ */ __name(function beforeUnmount3() {
    if (this.overlayEventListener) {
      OverlayEventBus.off("overlay-click", this.overlayEventListener);
      this.overlayEventListener = null;
    }
    if (this.overlay) {
      ZIndex.clear(this.overlay);
      this.onOverlayHide();
    }
  }, "beforeUnmount"),
  mounted: /* @__PURE__ */ __name(function mounted6() {
    this.id = this.id || UniqueComponentId();
    if (this.filters && this.filters[this.field]) {
      var fieldFilters = this.filters[this.field];
      if (fieldFilters.operator) {
        this.defaultMatchMode = fieldFilters.constraints[0].matchMode;
        this.defaultOperator = fieldFilters.operator;
      } else {
        this.defaultMatchMode = this.filters[this.field].matchMode;
      }
    }
  }, "mounted"),
  methods: {
    getColumnPT: /* @__PURE__ */ __name(function getColumnPT6(key, params) {
      var columnMetaData = _objectSpread$5({
        props: this.column.props,
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        }
      }, params);
      return mergeProps(this.ptm("column.".concat(key), {
        column: columnMetaData
      }), this.ptm("column.".concat(key), columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));
    }, "getColumnPT"),
    getColumnProp: /* @__PURE__ */ __name(function getColumnProp5() {
      return this.column.props && this.column.props.pt ? this.column.props.pt : void 0;
    }, "getColumnProp"),
    ptmFilterConstraintOptions: /* @__PURE__ */ __name(function ptmFilterConstraintOptions(matchMode) {
      return {
        context: {
          highlighted: matchMode && this.isRowMatchModeSelected(matchMode.value)
        }
      };
    }, "ptmFilterConstraintOptions"),
    clearFilter: /* @__PURE__ */ __name(function clearFilter() {
      var _filters = _objectSpread$5({}, this.filters);
      if (_filters[this.field].operator) {
        _filters[this.field].constraints.splice(1);
        _filters[this.field].operator = this.defaultOperator;
        _filters[this.field].constraints[0] = {
          value: null,
          matchMode: this.defaultMatchMode
        };
      } else {
        _filters[this.field].value = null;
        _filters[this.field].matchMode = this.defaultMatchMode;
      }
      this.$emit("filter-clear");
      this.$emit("filter-change", _filters);
      this.$emit("filter-apply");
      this.hide();
    }, "clearFilter"),
    applyFilter: /* @__PURE__ */ __name(function applyFilter() {
      this.$emit("apply-click", {
        field: this.field,
        constraints: this.filters[this.field]
      });
      this.$emit("filter-apply");
      this.hide();
    }, "applyFilter"),
    hasFilter: /* @__PURE__ */ __name(function hasFilter() {
      if (this.filtersStore) {
        var fieldFilter = this.filtersStore[this.field];
        if (fieldFilter) {
          if (fieldFilter.operator) return !this.isFilterBlank(fieldFilter.constraints[0].value);
          else return !this.isFilterBlank(fieldFilter.value);
        }
      }
      return false;
    }, "hasFilter"),
    hasRowFilter: /* @__PURE__ */ __name(function hasRowFilter() {
      return this.filters[this.field] && !this.isFilterBlank(this.filters[this.field].value);
    }, "hasRowFilter"),
    isFilterBlank: /* @__PURE__ */ __name(function isFilterBlank(filter3) {
      if (filter3 !== null && filter3 !== void 0) {
        if (typeof filter3 === "string" && filter3.trim().length == 0 || filter3 instanceof Array && filter3.length == 0) return true;
        else return false;
      }
      return true;
    }, "isFilterBlank"),
    toggleMenu: /* @__PURE__ */ __name(function toggleMenu(event2) {
      this.overlayVisible = !this.overlayVisible;
      event2.preventDefault();
    }, "toggleMenu"),
    onToggleButtonKeyDown: /* @__PURE__ */ __name(function onToggleButtonKeyDown(event2) {
      switch (event2.code) {
        case "Enter":
        case "NumpadEnter":
        case "Space":
          this.toggleMenu(event2);
          break;
        case "Escape":
          this.overlayVisible = false;
          break;
      }
    }, "onToggleButtonKeyDown"),
    onRowMatchModeChange: /* @__PURE__ */ __name(function onRowMatchModeChange(matchMode) {
      var _filters = _objectSpread$5({}, this.filters);
      _filters[this.field].matchMode = matchMode;
      this.$emit("matchmode-change", {
        field: this.field,
        matchMode
      });
      this.$emit("filter-change", _filters);
      this.$emit("filter-apply");
      this.hide();
    }, "onRowMatchModeChange"),
    onRowMatchModeKeyDown: /* @__PURE__ */ __name(function onRowMatchModeKeyDown(event2) {
      var item = event2.target;
      switch (event2.code) {
        case "ArrowDown":
          var nextItem = this.findNextItem(item);
          if (nextItem) {
            item.removeAttribute("tabindex");
            nextItem.tabIndex = "0";
            nextItem.focus();
          }
          event2.preventDefault();
          break;
        case "ArrowUp":
          var prevItem = this.findPrevItem(item);
          if (prevItem) {
            item.removeAttribute("tabindex");
            prevItem.tabIndex = "0";
            prevItem.focus();
          }
          event2.preventDefault();
          break;
      }
    }, "onRowMatchModeKeyDown"),
    isRowMatchModeSelected: /* @__PURE__ */ __name(function isRowMatchModeSelected(matchMode) {
      return this.filters[this.field].matchMode === matchMode;
    }, "isRowMatchModeSelected"),
    onOperatorChange: /* @__PURE__ */ __name(function onOperatorChange(value) {
      var _filters = _objectSpread$5({}, this.filters);
      _filters[this.field].operator = value;
      this.$emit("filter-change", _filters);
      this.$emit("operator-change", {
        field: this.field,
        operator: value
      });
      if (!this.showApplyButton) {
        this.$emit("filter-apply");
      }
    }, "onOperatorChange"),
    onMenuMatchModeChange: /* @__PURE__ */ __name(function onMenuMatchModeChange(value, index) {
      var _filters = _objectSpread$5({}, this.filters);
      _filters[this.field].constraints[index].matchMode = value;
      this.$emit("matchmode-change", {
        field: this.field,
        matchMode: value,
        index
      });
      if (!this.showApplyButton) {
        this.$emit("filter-apply");
      }
    }, "onMenuMatchModeChange"),
    addConstraint: /* @__PURE__ */ __name(function addConstraint() {
      var _filters = _objectSpread$5({}, this.filters);
      var newConstraint = {
        value: null,
        matchMode: this.defaultMatchMode
      };
      _filters[this.field].constraints.push(newConstraint);
      this.$emit("constraint-add", {
        field: this.field,
        constraing: newConstraint
      });
      this.$emit("filter-change", _filters);
      if (!this.showApplyButton) {
        this.$emit("filter-apply");
      }
    }, "addConstraint"),
    removeConstraint: /* @__PURE__ */ __name(function removeConstraint(index) {
      var _filters = _objectSpread$5({}, this.filters);
      var removedConstraint = _filters[this.field].constraints.splice(index, 1);
      this.$emit("constraint-remove", {
        field: this.field,
        constraing: removedConstraint
      });
      this.$emit("filter-change", _filters);
      if (!this.showApplyButton) {
        this.$emit("filter-apply");
      }
    }, "removeConstraint"),
    filterCallback: /* @__PURE__ */ __name(function filterCallback() {
      this.$emit("filter-apply");
    }, "filterCallback"),
    findNextItem: /* @__PURE__ */ __name(function findNextItem(item) {
      var nextItem = item.nextElementSibling;
      if (nextItem) return getAttribute(nextItem, "data-pc-section") === "filterconstraintseparator" ? this.findNextItem(nextItem) : nextItem;
      else return item.parentElement.firstElementChild;
    }, "findNextItem"),
    findPrevItem: /* @__PURE__ */ __name(function findPrevItem(item) {
      var prevItem = item.previousElementSibling;
      if (prevItem) return getAttribute(prevItem, "data-pc-section") === "filterconstraintseparator" ? this.findPrevItem(prevItem) : prevItem;
      else return item.parentElement.lastElementChild;
    }, "findPrevItem"),
    hide: /* @__PURE__ */ __name(function hide() {
      this.overlayVisible = false;
      this.showMenuButton && focus(this.$refs.icon.$el);
    }, "hide"),
    onContentClick: /* @__PURE__ */ __name(function onContentClick(event2) {
      this.selfClick = true;
      OverlayEventBus.emit("overlay-click", {
        originalEvent: event2,
        target: this.overlay
      });
    }, "onContentClick"),
    onContentMouseDown: /* @__PURE__ */ __name(function onContentMouseDown() {
      this.selfClick = true;
    }, "onContentMouseDown"),
    onOverlayEnter: /* @__PURE__ */ __name(function onOverlayEnter(el) {
      var _this = this;
      if (this.filterMenuStyle) {
        addStyle(this.overlay, this.filterMenuStyle);
      }
      ZIndex.set("overlay", el, this.$primevue.config.zIndex.overlay);
      addStyle(el, {
        position: "absolute",
        top: "0",
        left: "0"
      });
      absolutePosition(this.overlay, this.$refs.icon.$el);
      this.bindOutsideClickListener();
      this.bindScrollListener();
      this.bindResizeListener();
      this.overlayEventListener = function(e) {
        if (!_this.isOutsideClicked(e.target)) {
          _this.selfClick = true;
        }
      };
      OverlayEventBus.on("overlay-click", this.overlayEventListener);
    }, "onOverlayEnter"),
    onOverlayAfterEnter: /* @__PURE__ */ __name(function onOverlayAfterEnter() {
      var _this$overlay;
      (_this$overlay = this.overlay) === null || _this$overlay === void 0 || (_this$overlay = _this$overlay.$focustrap) === null || _this$overlay === void 0 || _this$overlay.autoFocus();
    }, "onOverlayAfterEnter"),
    onOverlayLeave: /* @__PURE__ */ __name(function onOverlayLeave() {
      this.onOverlayHide();
    }, "onOverlayLeave"),
    onOverlayAfterLeave: /* @__PURE__ */ __name(function onOverlayAfterLeave(el) {
      ZIndex.clear(el);
    }, "onOverlayAfterLeave"),
    onOverlayHide: /* @__PURE__ */ __name(function onOverlayHide() {
      this.unbindOutsideClickListener();
      this.unbindResizeListener();
      this.unbindScrollListener();
      this.overlay = null;
      OverlayEventBus.off("overlay-click", this.overlayEventListener);
      this.overlayEventListener = null;
    }, "onOverlayHide"),
    overlayRef: /* @__PURE__ */ __name(function overlayRef(el) {
      this.overlay = el;
    }, "overlayRef"),
    isOutsideClicked: /* @__PURE__ */ __name(function isOutsideClicked(target) {
      return !this.isTargetClicked(target) && this.overlay && !(this.overlay.isSameNode(target) || this.overlay.contains(target));
    }, "isOutsideClicked"),
    isTargetClicked: /* @__PURE__ */ __name(function isTargetClicked(target) {
      return this.$refs.icon && (this.$refs.icon.$el.isSameNode(target) || this.$refs.icon.$el.contains(target));
    }, "isTargetClicked"),
    bindOutsideClickListener: /* @__PURE__ */ __name(function bindOutsideClickListener() {
      var _this2 = this;
      if (!this.outsideClickListener) {
        this.outsideClickListener = function(event2) {
          if (_this2.overlayVisible && !_this2.selfClick && _this2.isOutsideClicked(event2.target)) {
            _this2.overlayVisible = false;
          }
          _this2.selfClick = false;
        };
        document.addEventListener("click", this.outsideClickListener);
      }
    }, "bindOutsideClickListener"),
    unbindOutsideClickListener: /* @__PURE__ */ __name(function unbindOutsideClickListener() {
      if (this.outsideClickListener) {
        document.removeEventListener("click", this.outsideClickListener);
        this.outsideClickListener = null;
        this.selfClick = false;
      }
    }, "unbindOutsideClickListener"),
    bindScrollListener: /* @__PURE__ */ __name(function bindScrollListener() {
      var _this3 = this;
      if (!this.scrollHandler) {
        this.scrollHandler = new ConnectedOverlayScrollHandler(this.$refs.icon.$el, function() {
          if (_this3.overlayVisible) {
            _this3.hide();
          }
        });
      }
      this.scrollHandler.bindScrollListener();
    }, "bindScrollListener"),
    unbindScrollListener: /* @__PURE__ */ __name(function unbindScrollListener() {
      if (this.scrollHandler) {
        this.scrollHandler.unbindScrollListener();
      }
    }, "unbindScrollListener"),
    bindResizeListener: /* @__PURE__ */ __name(function bindResizeListener() {
      var _this4 = this;
      if (!this.resizeListener) {
        this.resizeListener = function() {
          if (_this4.overlayVisible && !isTouchDevice()) {
            _this4.hide();
          }
        };
        window.addEventListener("resize", this.resizeListener);
      }
    }, "bindResizeListener"),
    unbindResizeListener: /* @__PURE__ */ __name(function unbindResizeListener() {
      if (this.resizeListener) {
        window.removeEventListener("resize", this.resizeListener);
        this.resizeListener = null;
      }
    }, "unbindResizeListener")
  },
  computed: {
    showMenuButton: /* @__PURE__ */ __name(function showMenuButton() {
      return this.showMenu && (this.display === "row" ? this.type !== "boolean" : true);
    }, "showMenuButton"),
    overlayId: /* @__PURE__ */ __name(function overlayId() {
      return this.id + "_overlay";
    }, "overlayId"),
    matchModes: /* @__PURE__ */ __name(function matchModes() {
      var _this5 = this;
      return this.matchModeOptions || this.$primevue.config.filterMatchModeOptions[this.type].map(function(key) {
        return {
          label: _this5.$primevue.config.locale[key],
          value: key
        };
      });
    }, "matchModes"),
    isShowMatchModes: /* @__PURE__ */ __name(function isShowMatchModes() {
      return this.type !== "boolean" && this.showMatchModes && this.matchModes;
    }, "isShowMatchModes"),
    operatorOptions: /* @__PURE__ */ __name(function operatorOptions() {
      return [{
        label: this.$primevue.config.locale.matchAll,
        value: FilterOperator.AND
      }, {
        label: this.$primevue.config.locale.matchAny,
        value: FilterOperator.OR
      }];
    }, "operatorOptions"),
    noFilterLabel: /* @__PURE__ */ __name(function noFilterLabel() {
      return this.$primevue.config.locale ? this.$primevue.config.locale.noFilter : void 0;
    }, "noFilterLabel"),
    isShowOperator: /* @__PURE__ */ __name(function isShowOperator() {
      return this.showOperator && this.filters[this.field].operator;
    }, "isShowOperator"),
    operator: /* @__PURE__ */ __name(function operator() {
      return this.filters[this.field].operator;
    }, "operator"),
    fieldConstraints: /* @__PURE__ */ __name(function fieldConstraints() {
      return this.filters[this.field].constraints || [this.filters[this.field]];
    }, "fieldConstraints"),
    showRemoveIcon: /* @__PURE__ */ __name(function showRemoveIcon() {
      return this.fieldConstraints.length > 1;
    }, "showRemoveIcon"),
    removeRuleButtonLabel: /* @__PURE__ */ __name(function removeRuleButtonLabel() {
      return this.$primevue.config.locale ? this.$primevue.config.locale.removeRule : void 0;
    }, "removeRuleButtonLabel"),
    addRuleButtonLabel: /* @__PURE__ */ __name(function addRuleButtonLabel() {
      return this.$primevue.config.locale ? this.$primevue.config.locale.addRule : void 0;
    }, "addRuleButtonLabel"),
    isShowAddConstraint: /* @__PURE__ */ __name(function isShowAddConstraint() {
      return this.showAddButton && this.filters[this.field].operator && this.fieldConstraints && this.fieldConstraints.length < this.maxConstraints;
    }, "isShowAddConstraint"),
    clearButtonLabel: /* @__PURE__ */ __name(function clearButtonLabel() {
      return this.$primevue.config.locale ? this.$primevue.config.locale.clear : void 0;
    }, "clearButtonLabel"),
    applyButtonLabel: /* @__PURE__ */ __name(function applyButtonLabel() {
      return this.$primevue.config.locale ? this.$primevue.config.locale.apply : void 0;
    }, "applyButtonLabel"),
    columnFilterButtonAriaLabel: /* @__PURE__ */ __name(function columnFilterButtonAriaLabel() {
      return this.$primevue.config.locale ? this.overlayVisible ? this.$primevue.config.locale.showFilterMenu : this.$primevue.config.locale.hideFilterMenu : void 0;
    }, "columnFilterButtonAriaLabel"),
    filterOperatorAriaLabel: /* @__PURE__ */ __name(function filterOperatorAriaLabel() {
      return this.$primevue.config.locale ? this.$primevue.config.locale.filterOperator : void 0;
    }, "filterOperatorAriaLabel"),
    filterRuleAriaLabel: /* @__PURE__ */ __name(function filterRuleAriaLabel() {
      return this.$primevue.config.locale ? this.$primevue.config.locale.filterConstraint : void 0;
    }, "filterRuleAriaLabel"),
    ptmHeaderFilterClearParams: /* @__PURE__ */ __name(function ptmHeaderFilterClearParams() {
      return {
        context: {
          hidden: this.hasRowFilter()
        }
      };
    }, "ptmHeaderFilterClearParams"),
    ptmFilterMenuParams: /* @__PURE__ */ __name(function ptmFilterMenuParams() {
      return {
        context: {
          overlayVisible: this.overlayVisible,
          active: this.hasFilter()
        }
      };
    }, "ptmFilterMenuParams")
  },
  components: {
    Select: script$u,
    Button: script$z,
    Portal: script$E,
    FilterSlashIcon: script$h,
    FilterIcon: script$i,
    TrashIcon: script$g,
    PlusIcon: script$F
  },
  directives: {
    focustrap: FocusTrap
  }
};
function _typeof$4(o) {
  "@babel/helpers - typeof";
  return _typeof$4 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$4(o);
}
__name(_typeof$4, "_typeof$4");
function ownKeys$4(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
__name(ownKeys$4, "ownKeys$4");
function _objectSpread$4(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys$4(Object(t), true).forEach(function(r2) {
      _defineProperty$4(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$4(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
__name(_objectSpread$4, "_objectSpread$4");
function _defineProperty$4(e, r, t) {
  return (r = _toPropertyKey$4(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty$4, "_defineProperty$4");
function _toPropertyKey$4(t) {
  var i = _toPrimitive$4(t, "string");
  return "symbol" == _typeof$4(i) ? i : i + "";
}
__name(_toPropertyKey$4, "_toPropertyKey$4");
function _toPrimitive$4(t, r) {
  if ("object" != _typeof$4(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof$4(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive$4, "_toPrimitive$4");
var _hoisted_1$1 = ["id", "aria-modal"];
var _hoisted_2 = ["onClick", "onKeydown", "tabindex"];
function render$4(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_Button = resolveComponent("Button");
  var _component_Select = resolveComponent("Select");
  var _component_Portal = resolveComponent("Portal");
  var _directive_focustrap = resolveDirective("focustrap");
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx("filter")
  }, $options.getColumnPT("filter")), [$props.display === "row" ? (openBlock(), createElementBlock("div", mergeProps({
    key: 0,
    "class": _ctx.cx("filterElementContainer")
  }, _objectSpread$4(_objectSpread$4({}, $props.filterInputProps), $options.getColumnPT("filterElementContainer"))), [(openBlock(), createBlock(resolveDynamicComponent($props.filterElement), {
    field: $props.field,
    filterModel: $props.filters[$props.field],
    filterCallback: $options.filterCallback
  }, null, 8, ["field", "filterModel", "filterCallback"]))], 16)) : createCommentVNode("", true), $options.showMenuButton ? (openBlock(), createBlock(_component_Button, mergeProps({
    key: 1,
    ref: "icon",
    "aria-label": $options.columnFilterButtonAriaLabel,
    "aria-haspopup": "true",
    "aria-expanded": $data.overlayVisible,
    "aria-controls": $options.overlayId,
    "class": _ctx.cx("pcColumnFilterButton"),
    unstyled: _ctx.unstyled,
    onClick: _cache[0] || (_cache[0] = function($event) {
      return $options.toggleMenu($event);
    }),
    onKeydown: _cache[1] || (_cache[1] = function($event) {
      return $options.onToggleButtonKeyDown($event);
    })
  }, _objectSpread$4(_objectSpread$4({}, $options.getColumnPT("pcColumnFilterButton", $options.ptmFilterMenuParams)), $props.filterButtonProps.filter)), {
    icon: withCtx(function(slotProps) {
      return [(openBlock(), createBlock(resolveDynamicComponent($props.filterIconTemplate || "FilterIcon"), mergeProps({
        "class": slotProps["class"]
      }, $options.getColumnPT("filterMenuIcon")), null, 16, ["class"]))];
    }),
    _: 1
  }, 16, ["aria-label", "aria-expanded", "aria-controls", "class", "unstyled"])) : createCommentVNode("", true), $props.showClearButton && $props.display === "row" && $options.hasRowFilter() ? (openBlock(), createBlock(_component_Button, mergeProps({
    key: 2,
    "class": _ctx.cx("pcColumnFilterClearButton"),
    unstyled: _ctx.unstyled,
    onClick: _cache[2] || (_cache[2] = function($event) {
      return $options.clearFilter();
    })
  }, _objectSpread$4(_objectSpread$4({}, $options.getColumnPT("pcColumnFilterClearButton", $options.ptmHeaderFilterClearParams)), $props.filterButtonProps.inline.clear)), {
    icon: withCtx(function(slotProps) {
      return [(openBlock(), createBlock(resolveDynamicComponent($props.filterClearIconTemplate || "FilterSlashIcon"), mergeProps({
        "class": slotProps["class"]
      }, $options.getColumnPT("filterClearIcon")), null, 16, ["class"]))];
    }),
    _: 1
  }, 16, ["class", "unstyled"])) : createCommentVNode("", true), createVNode(_component_Portal, null, {
    "default": withCtx(function() {
      return [createVNode(Transition, mergeProps({
        name: "p-connected-overlay",
        onEnter: $options.onOverlayEnter,
        onAfterEnter: $options.onOverlayAfterEnter,
        onLeave: $options.onOverlayLeave,
        onAfterLeave: $options.onOverlayAfterLeave
      }, $options.getColumnPT("transition")), {
        "default": withCtx(function() {
          return [$data.overlayVisible ? withDirectives((openBlock(), createElementBlock("div", mergeProps({
            key: 0,
            ref: $options.overlayRef,
            id: $options.overlayId,
            "aria-modal": $data.overlayVisible,
            role: "dialog",
            "class": [_ctx.cx("filterOverlay"), $props.filterMenuClass],
            onKeydown: _cache[10] || (_cache[10] = withKeys(function() {
              return $options.hide && $options.hide.apply($options, arguments);
            }, ["escape"])),
            onClick: _cache[11] || (_cache[11] = function() {
              return $options.onContentClick && $options.onContentClick.apply($options, arguments);
            }),
            onMousedown: _cache[12] || (_cache[12] = function() {
              return $options.onContentMouseDown && $options.onContentMouseDown.apply($options, arguments);
            })
          }, $options.getColumnPT("filterOverlay")), [(openBlock(), createBlock(resolveDynamicComponent($props.filterHeaderTemplate), {
            field: $props.field,
            filterModel: $props.filters[$props.field],
            filterCallback: $options.filterCallback
          }, null, 8, ["field", "filterModel", "filterCallback"])), $props.display === "row" ? (openBlock(), createElementBlock("ul", mergeProps({
            key: 0,
            "class": _ctx.cx("filterConstraintList")
          }, $options.getColumnPT("filterConstraintList")), [(openBlock(true), createElementBlock(Fragment, null, renderList($options.matchModes, function(matchMode, i) {
            return openBlock(), createElementBlock("li", mergeProps({
              key: matchMode.label,
              "class": _ctx.cx("filterConstraint", {
                matchMode
              }),
              onClick: /* @__PURE__ */ __name(function onClick3($event) {
                return $options.onRowMatchModeChange(matchMode.value);
              }, "onClick"),
              onKeydown: [_cache[3] || (_cache[3] = function($event) {
                return $options.onRowMatchModeKeyDown($event);
              }), withKeys(withModifiers(function($event) {
                return $options.onRowMatchModeChange(matchMode.value);
              }, ["prevent"]), ["enter"])],
              tabindex: i === 0 ? "0" : null,
              ref_for: true
            }, $options.getColumnPT("filterConstraint", $options.ptmFilterConstraintOptions(matchMode))), toDisplayString(matchMode.label), 17, _hoisted_2);
          }), 128)), createBaseVNode("li", mergeProps({
            "class": _ctx.cx("filterConstraintSeparator")
          }, $options.getColumnPT("filterConstraintSeparator")), null, 16), createBaseVNode("li", mergeProps({
            "class": _ctx.cx("filterConstraint"),
            onClick: _cache[4] || (_cache[4] = function($event) {
              return $options.clearFilter();
            }),
            onKeydown: [_cache[5] || (_cache[5] = function($event) {
              return $options.onRowMatchModeKeyDown($event);
            }), _cache[6] || (_cache[6] = withKeys(function($event) {
              return _ctx.onRowClearItemClick();
            }, ["enter"]))]
          }, $options.getColumnPT("filterConstraint")), toDisplayString($options.noFilterLabel), 17)], 16)) : (openBlock(), createElementBlock(Fragment, {
            key: 1
          }, [$options.isShowOperator ? (openBlock(), createElementBlock("div", mergeProps({
            key: 0,
            "class": _ctx.cx("filterOperator")
          }, $options.getColumnPT("filterOperator")), [createVNode(_component_Select, {
            options: $options.operatorOptions,
            modelValue: $options.operator,
            "aria-label": $options.filterOperatorAriaLabel,
            "class": normalizeClass(_ctx.cx("pcFilterOperatorDropdown")),
            optionLabel: "label",
            optionValue: "value",
            "onUpdate:modelValue": _cache[7] || (_cache[7] = function($event) {
              return $options.onOperatorChange($event);
            }),
            unstyled: _ctx.unstyled,
            pt: $options.getColumnPT("pcFilterOperatorDropdown")
          }, null, 8, ["options", "modelValue", "aria-label", "class", "unstyled", "pt"])], 16)) : createCommentVNode("", true), createBaseVNode("div", mergeProps({
            "class": _ctx.cx("filterRuleList")
          }, $options.getColumnPT("filterRuleList")), [(openBlock(true), createElementBlock(Fragment, null, renderList($options.fieldConstraints, function(fieldConstraint, i) {
            return openBlock(), createElementBlock("div", mergeProps({
              key: i,
              "class": _ctx.cx("filterRule"),
              ref_for: true
            }, $options.getColumnPT("filterRule")), [$options.isShowMatchModes ? (openBlock(), createBlock(_component_Select, {
              key: 0,
              options: $options.matchModes,
              modelValue: fieldConstraint.matchMode,
              "class": normalizeClass(_ctx.cx("pcFilterConstraintDropdown")),
              optionLabel: "label",
              optionValue: "value",
              "aria-label": $options.filterRuleAriaLabel,
              "onUpdate:modelValue": /* @__PURE__ */ __name(function onUpdateModelValue($event) {
                return $options.onMenuMatchModeChange($event, i);
              }, "onUpdateModelValue"),
              unstyled: _ctx.unstyled,
              pt: $options.getColumnPT("pcFilterConstraintDropdown")
            }, null, 8, ["options", "modelValue", "class", "aria-label", "onUpdate:modelValue", "unstyled", "pt"])) : createCommentVNode("", true), $props.display === "menu" ? (openBlock(), createBlock(resolveDynamicComponent($props.filterElement), {
              key: 1,
              field: $props.field,
              filterModel: fieldConstraint,
              filterCallback: $options.filterCallback,
              applyFilter: $options.applyFilter
            }, null, 8, ["field", "filterModel", "filterCallback", "applyFilter"])) : createCommentVNode("", true), $options.showRemoveIcon ? (openBlock(), createElementBlock("div", mergeProps({
              key: 2,
              ref_for: true
            }, $options.getColumnPT("filterRemove")), [createVNode(_component_Button, mergeProps({
              type: "button",
              "class": _ctx.cx("pcFilterRemoveRuleButton"),
              onClick: /* @__PURE__ */ __name(function onClick3($event) {
                return $options.removeConstraint(i);
              }, "onClick"),
              label: $options.removeRuleButtonLabel,
              unstyled: _ctx.unstyled,
              ref_for: true
            }, $props.filterButtonProps.popover.removeRule, {
              pt: $options.getColumnPT("pcFilterRemoveRuleButton")
            }), {
              icon: withCtx(function(iconProps) {
                return [(openBlock(), createBlock(resolveDynamicComponent($props.filterRemoveIconTemplate || "TrashIcon"), mergeProps({
                  "class": iconProps["class"],
                  ref_for: true
                }, $options.getColumnPT("pcFilterRemoveRuleButton")["icon"]), null, 16, ["class"]))];
              }),
              _: 2
            }, 1040, ["class", "onClick", "label", "unstyled", "pt"])], 16)) : createCommentVNode("", true)], 16);
          }), 128))], 16), $options.isShowAddConstraint ? (openBlock(), createElementBlock("div", normalizeProps(mergeProps({
            key: 1
          }, $options.getColumnPT("filterAddButtonContainer"))), [createVNode(_component_Button, mergeProps({
            type: "button",
            label: $options.addRuleButtonLabel,
            iconPos: "left",
            "class": _ctx.cx("pcFilterAddRuleButton"),
            onClick: _cache[8] || (_cache[8] = function($event) {
              return $options.addConstraint();
            }),
            unstyled: _ctx.unstyled
          }, $props.filterButtonProps.popover.addRule, {
            pt: $options.getColumnPT("pcFilterAddRuleButton")
          }), {
            icon: withCtx(function(iconProps) {
              return [(openBlock(), createBlock(resolveDynamicComponent($props.filterAddIconTemplate || "PlusIcon"), mergeProps({
                "class": iconProps["class"]
              }, $options.getColumnPT("pcFilterAddRuleButton")["icon"]), null, 16, ["class"]))];
            }),
            _: 1
          }, 16, ["label", "class", "unstyled", "pt"])], 16)) : createCommentVNode("", true), createBaseVNode("div", mergeProps({
            "class": _ctx.cx("filterButtonbar")
          }, $options.getColumnPT("filterButtonbar")), [!$props.filterClearTemplate && $props.showClearButton ? (openBlock(), createBlock(_component_Button, mergeProps({
            key: 0,
            type: "button",
            "class": _ctx.cx("pcFilterClearButton"),
            label: $options.clearButtonLabel,
            onClick: $options.clearFilter,
            unstyled: _ctx.unstyled
          }, $props.filterButtonProps.popover.clear, {
            pt: $options.getColumnPT("pcFilterClearButton")
          }), null, 16, ["class", "label", "onClick", "unstyled", "pt"])) : (openBlock(), createBlock(resolveDynamicComponent($props.filterClearTemplate), {
            key: 1,
            field: $props.field,
            filterModel: $props.filters[$props.field],
            filterCallback: $options.clearFilter
          }, null, 8, ["field", "filterModel", "filterCallback"])), $props.showApplyButton ? (openBlock(), createElementBlock(Fragment, {
            key: 2
          }, [!$props.filterApplyTemplate ? (openBlock(), createBlock(_component_Button, mergeProps({
            key: 0,
            type: "button",
            "class": _ctx.cx("pcFilterApplyButton"),
            label: $options.applyButtonLabel,
            onClick: _cache[9] || (_cache[9] = function($event) {
              return $options.applyFilter();
            }),
            unstyled: _ctx.unstyled
          }, $props.filterButtonProps.popover.apply, {
            pt: $options.getColumnPT("pcFilterApplyButton")
          }), null, 16, ["class", "label", "unstyled", "pt"])) : (openBlock(), createBlock(resolveDynamicComponent($props.filterApplyTemplate), {
            key: 1,
            field: $props.field,
            filterModel: $props.filters[$props.field],
            filterCallback: $options.applyFilter
          }, null, 8, ["field", "filterModel", "filterCallback"]))], 64)) : createCommentVNode("", true)], 16)], 64)), (openBlock(), createBlock(resolveDynamicComponent($props.filterFooterTemplate), {
            field: $props.field,
            filterModel: $props.filters[$props.field],
            filterCallback: $options.filterCallback
          }, null, 8, ["field", "filterModel", "filterCallback"]))], 16, _hoisted_1$1)), [[_directive_focustrap]]) : createCommentVNode("", true)];
        }),
        _: 1
      }, 16, ["onEnter", "onAfterEnter", "onLeave", "onAfterLeave"])];
    }),
    _: 1
  })], 16);
}
__name(render$4, "render$4");
script$4.render = render$4;
var script$3 = {
  name: "HeaderCheckbox",
  hostName: "DataTable",
  "extends": script$s,
  emits: ["change"],
  props: {
    checked: null,
    disabled: null,
    column: null,
    headerCheckboxIconTemplate: {
      type: Function,
      "default": null
    }
  },
  methods: {
    getColumnPT: /* @__PURE__ */ __name(function getColumnPT7(key) {
      var columnMetaData = {
        props: this.column.props,
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          checked: this.checked,
          disabled: this.disabled
        }
      };
      return mergeProps(this.ptm("column.".concat(key), {
        column: columnMetaData
      }), this.ptm("column.".concat(key), columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));
    }, "getColumnPT"),
    getColumnProp: /* @__PURE__ */ __name(function getColumnProp6() {
      return this.column.props && this.column.props.pt ? this.column.props.pt : void 0;
    }, "getColumnProp"),
    onChange: /* @__PURE__ */ __name(function onChange7(event2) {
      this.$emit("change", {
        originalEvent: event2,
        checked: !this.checked
      });
    }, "onChange")
  },
  computed: {
    headerCheckboxAriaLabel: /* @__PURE__ */ __name(function headerCheckboxAriaLabel() {
      return this.$primevue.config.locale.aria ? this.checked ? this.$primevue.config.locale.aria.selectAll : this.$primevue.config.locale.aria.unselectAll : void 0;
    }, "headerCheckboxAriaLabel")
  },
  components: {
    CheckIcon: script$x,
    Checkbox: script$y
  }
};
function render$3(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_CheckIcon = resolveComponent("CheckIcon");
  var _component_Checkbox = resolveComponent("Checkbox");
  return openBlock(), createBlock(_component_Checkbox, {
    modelValue: $props.checked,
    binary: true,
    disabled: $props.disabled,
    "aria-label": $options.headerCheckboxAriaLabel,
    onChange: $options.onChange,
    pt: $options.getColumnPT("pcHeaderCheckbox")
  }, {
    icon: withCtx(function(slotProps) {
      return [$props.headerCheckboxIconTemplate ? (openBlock(), createBlock(resolveDynamicComponent($props.headerCheckboxIconTemplate), {
        key: 0,
        checked: slotProps.checked,
        "class": normalizeClass(slotProps["class"])
      }, null, 8, ["checked", "class"])) : !$props.headerCheckboxIconTemplate && slotProps.checked ? (openBlock(), createBlock(_component_CheckIcon, mergeProps({
        key: 1,
        "class": slotProps["class"]
      }, $options.getColumnPT("pcHeaderCheckbox")["icon"]), null, 16, ["class"])) : createCommentVNode("", true)];
    }),
    _: 1
  }, 8, ["modelValue", "disabled", "aria-label", "onChange", "pt"]);
}
__name(render$3, "render$3");
script$3.render = render$3;
var script$2 = {
  name: "HeaderCell",
  hostName: "DataTable",
  "extends": script$s,
  emits: ["column-click", "column-mousedown", "column-dragstart", "column-dragover", "column-dragleave", "column-drop", "column-resizestart", "checkbox-change", "filter-change", "filter-apply", "operator-change", "matchmode-change", "constraint-add", "constraint-remove", "filter-clear", "apply-click"],
  props: {
    column: {
      type: Object,
      "default": null
    },
    index: {
      type: Number,
      "default": null
    },
    resizableColumns: {
      type: Boolean,
      "default": false
    },
    groupRowsBy: {
      type: [Array, String, Function],
      "default": null
    },
    sortMode: {
      type: String,
      "default": "single"
    },
    groupRowSortField: {
      type: [String, Function],
      "default": null
    },
    sortField: {
      type: [String, Function],
      "default": null
    },
    sortOrder: {
      type: Number,
      "default": null
    },
    multiSortMeta: {
      type: Array,
      "default": null
    },
    allRowsSelected: {
      type: Boolean,
      "default": false
    },
    empty: {
      type: Boolean,
      "default": false
    },
    filterDisplay: {
      type: String,
      "default": null
    },
    filters: {
      type: Object,
      "default": null
    },
    filtersStore: {
      type: Object,
      "default": null
    },
    filterColumn: {
      type: Boolean,
      "default": false
    },
    reorderableColumns: {
      type: Boolean,
      "default": false
    },
    filterInputProps: {
      type: null,
      "default": null
    },
    filterButtonProps: {
      type: null,
      "default": null
    }
  },
  data: /* @__PURE__ */ __name(function data9() {
    return {
      styleObject: {}
    };
  }, "data"),
  mounted: /* @__PURE__ */ __name(function mounted7() {
    if (this.columnProp("frozen")) {
      this.updateStickyPosition();
    }
  }, "mounted"),
  updated: /* @__PURE__ */ __name(function updated4() {
    if (this.columnProp("frozen")) {
      this.updateStickyPosition();
    }
  }, "updated"),
  methods: {
    columnProp: /* @__PURE__ */ __name(function columnProp5(prop) {
      return getVNodeProp(this.column, prop);
    }, "columnProp"),
    getColumnPT: /* @__PURE__ */ __name(function getColumnPT8(key) {
      var _this$$parentInstance, _this$$parentInstance2;
      var columnMetaData = {
        props: this.column.props,
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          index: this.index,
          sortable: this.columnProp("sortable") === "" || this.columnProp("sortable"),
          sorted: this.isColumnSorted(),
          resizable: this.resizableColumns,
          size: (_this$$parentInstance = this.$parentInstance) === null || _this$$parentInstance === void 0 || (_this$$parentInstance = _this$$parentInstance.$parentInstance) === null || _this$$parentInstance === void 0 ? void 0 : _this$$parentInstance.size,
          showGridlines: ((_this$$parentInstance2 = this.$parentInstance) === null || _this$$parentInstance2 === void 0 || (_this$$parentInstance2 = _this$$parentInstance2.$parentInstance) === null || _this$$parentInstance2 === void 0 ? void 0 : _this$$parentInstance2.showGridlines) || false
        }
      };
      return mergeProps(this.ptm("column.".concat(key), {
        column: columnMetaData
      }), this.ptm("column.".concat(key), columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));
    }, "getColumnPT"),
    getColumnProp: /* @__PURE__ */ __name(function getColumnProp7() {
      return this.column.props && this.column.props.pt ? this.column.props.pt : void 0;
    }, "getColumnProp"),
    onClick: /* @__PURE__ */ __name(function onClick2(event2) {
      this.$emit("column-click", {
        originalEvent: event2,
        column: this.column
      });
    }, "onClick"),
    onKeyDown: /* @__PURE__ */ __name(function onKeyDown2(event2) {
      if ((event2.code === "Enter" || event2.code === "NumpadEnter" || event2.code === "Space") && event2.currentTarget.nodeName === "TH" && getAttribute(event2.currentTarget, "data-p-sortable-column")) {
        this.$emit("column-click", {
          originalEvent: event2,
          column: this.column
        });
        event2.preventDefault();
      }
    }, "onKeyDown"),
    onMouseDown: /* @__PURE__ */ __name(function onMouseDown(event2) {
      this.$emit("column-mousedown", {
        originalEvent: event2,
        column: this.column
      });
    }, "onMouseDown"),
    onDragStart: /* @__PURE__ */ __name(function onDragStart(event2) {
      this.$emit("column-dragstart", {
        originalEvent: event2,
        column: this.column
      });
    }, "onDragStart"),
    onDragOver: /* @__PURE__ */ __name(function onDragOver(event2) {
      this.$emit("column-dragover", {
        originalEvent: event2,
        column: this.column
      });
    }, "onDragOver"),
    onDragLeave: /* @__PURE__ */ __name(function onDragLeave(event2) {
      this.$emit("column-dragleave", {
        originalEvent: event2,
        column: this.column
      });
    }, "onDragLeave"),
    onDrop: /* @__PURE__ */ __name(function onDrop(event2) {
      this.$emit("column-drop", {
        originalEvent: event2,
        column: this.column
      });
    }, "onDrop"),
    onResizeStart: /* @__PURE__ */ __name(function onResizeStart(event2) {
      this.$emit("column-resizestart", event2);
    }, "onResizeStart"),
    getMultiSortMetaIndex: /* @__PURE__ */ __name(function getMultiSortMetaIndex() {
      var _this = this;
      return this.multiSortMeta.findIndex(function(meta) {
        return meta.field === _this.columnProp("field") || meta.field === _this.columnProp("sortField");
      });
    }, "getMultiSortMetaIndex"),
    getBadgeValue: /* @__PURE__ */ __name(function getBadgeValue() {
      var index = this.getMultiSortMetaIndex();
      return this.groupRowsBy && this.groupRowsBy === this.groupRowSortField && index > -1 ? index : index + 1;
    }, "getBadgeValue"),
    isMultiSorted: /* @__PURE__ */ __name(function isMultiSorted() {
      return this.sortMode === "multiple" && this.columnProp("sortable") && this.getMultiSortMetaIndex() > -1;
    }, "isMultiSorted"),
    isColumnSorted: /* @__PURE__ */ __name(function isColumnSorted() {
      return this.sortMode === "single" ? this.sortField && (this.sortField === this.columnProp("field") || this.sortField === this.columnProp("sortField")) : this.isMultiSorted();
    }, "isColumnSorted"),
    updateStickyPosition: /* @__PURE__ */ __name(function updateStickyPosition3() {
      if (this.columnProp("frozen")) {
        var align = this.columnProp("alignFrozen");
        if (align === "right") {
          var right = 0;
          var next2 = getNextElementSibling(this.$el, '[data-p-frozen-column="true"]');
          if (next2) {
            right = getOuterWidth(next2) + parseFloat(next2.style.right || 0);
          }
          this.styleObject.right = right + "px";
        } else {
          var left = 0;
          var prev2 = getPreviousElementSibling(this.$el, '[data-p-frozen-column="true"]');
          if (prev2) {
            left = getOuterWidth(prev2) + parseFloat(prev2.style.left || 0);
          }
          this.styleObject.left = left + "px";
        }
        var filterRow = this.$el.parentElement.nextElementSibling;
        if (filterRow) {
          var index = getIndex(this.$el);
          if (filterRow.children[index]) {
            filterRow.children[index].style.left = this.styleObject.left;
            filterRow.children[index].style.right = this.styleObject.right;
          }
        }
      }
    }, "updateStickyPosition"),
    onHeaderCheckboxChange: /* @__PURE__ */ __name(function onHeaderCheckboxChange(event2) {
      this.$emit("checkbox-change", event2);
    }, "onHeaderCheckboxChange")
  },
  computed: {
    containerClass: /* @__PURE__ */ __name(function containerClass3() {
      return [this.cx("headerCell"), this.filterColumn ? this.columnProp("filterHeaderClass") : this.columnProp("headerClass"), this.columnProp("class")];
    }, "containerClass"),
    containerStyle: /* @__PURE__ */ __name(function containerStyle3() {
      var headerStyle = this.filterColumn ? this.columnProp("filterHeaderStyle") : this.columnProp("headerStyle");
      var columnStyle = this.columnProp("style");
      return this.columnProp("frozen") ? [columnStyle, headerStyle, this.styleObject] : [columnStyle, headerStyle];
    }, "containerStyle"),
    sortState: /* @__PURE__ */ __name(function sortState() {
      var sorted2 = false;
      var sortOrder2 = null;
      if (this.sortMode === "single") {
        sorted2 = this.sortField && (this.sortField === this.columnProp("field") || this.sortField === this.columnProp("sortField"));
        sortOrder2 = sorted2 ? this.sortOrder : 0;
      } else if (this.sortMode === "multiple") {
        var metaIndex = this.getMultiSortMetaIndex();
        if (metaIndex > -1) {
          sorted2 = true;
          sortOrder2 = this.multiSortMeta[metaIndex].order;
        }
      }
      return {
        sorted: sorted2,
        sortOrder: sortOrder2
      };
    }, "sortState"),
    sortableColumnIcon: /* @__PURE__ */ __name(function sortableColumnIcon() {
      var _this$sortState = this.sortState, sorted2 = _this$sortState.sorted, sortOrder2 = _this$sortState.sortOrder;
      if (!sorted2) return script$f;
      else if (sorted2 && sortOrder2 > 0) return script$d;
      else if (sorted2 && sortOrder2 < 0) return script$e;
      return null;
    }, "sortableColumnIcon"),
    ariaSort: /* @__PURE__ */ __name(function ariaSort() {
      if (this.columnProp("sortable")) {
        var _this$sortState2 = this.sortState, sorted2 = _this$sortState2.sorted, sortOrder2 = _this$sortState2.sortOrder;
        if (sorted2 && sortOrder2 < 0) return "descending";
        else if (sorted2 && sortOrder2 > 0) return "ascending";
        else return "none";
      } else {
        return null;
      }
    }, "ariaSort")
  },
  components: {
    Badge: script$G,
    DTHeaderCheckbox: script$3,
    DTColumnFilter: script$4,
    SortAltIcon: script$f,
    SortAmountUpAltIcon: script$d,
    SortAmountDownIcon: script$e
  }
};
function _typeof$3(o) {
  "@babel/helpers - typeof";
  return _typeof$3 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$3(o);
}
__name(_typeof$3, "_typeof$3");
function ownKeys$3(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
__name(ownKeys$3, "ownKeys$3");
function _objectSpread$3(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys$3(Object(t), true).forEach(function(r2) {
      _defineProperty$3(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$3(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
__name(_objectSpread$3, "_objectSpread$3");
function _defineProperty$3(e, r, t) {
  return (r = _toPropertyKey$3(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty$3, "_defineProperty$3");
function _toPropertyKey$3(t) {
  var i = _toPrimitive$3(t, "string");
  return "symbol" == _typeof$3(i) ? i : i + "";
}
__name(_toPropertyKey$3, "_toPropertyKey$3");
function _toPrimitive$3(t, r) {
  if ("object" != _typeof$3(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof$3(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive$3, "_toPrimitive$3");
var _hoisted_1 = ["tabindex", "colspan", "rowspan", "aria-sort", "data-p-sortable-column", "data-p-resizable-column", "data-p-sorted", "data-p-filter-column", "data-p-frozen-column", "data-p-reorderable-column"];
function render$2(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_Badge = resolveComponent("Badge");
  var _component_DTHeaderCheckbox = resolveComponent("DTHeaderCheckbox");
  var _component_DTColumnFilter = resolveComponent("DTColumnFilter");
  return openBlock(), createElementBlock("th", mergeProps({
    style: $options.containerStyle,
    "class": $options.containerClass,
    tabindex: $options.columnProp("sortable") ? "0" : null,
    role: "columnheader",
    colspan: $options.columnProp("colspan"),
    rowspan: $options.columnProp("rowspan"),
    "aria-sort": $options.ariaSort,
    onClick: _cache[8] || (_cache[8] = function() {
      return $options.onClick && $options.onClick.apply($options, arguments);
    }),
    onKeydown: _cache[9] || (_cache[9] = function() {
      return $options.onKeyDown && $options.onKeyDown.apply($options, arguments);
    }),
    onMousedown: _cache[10] || (_cache[10] = function() {
      return $options.onMouseDown && $options.onMouseDown.apply($options, arguments);
    }),
    onDragstart: _cache[11] || (_cache[11] = function() {
      return $options.onDragStart && $options.onDragStart.apply($options, arguments);
    }),
    onDragover: _cache[12] || (_cache[12] = function() {
      return $options.onDragOver && $options.onDragOver.apply($options, arguments);
    }),
    onDragleave: _cache[13] || (_cache[13] = function() {
      return $options.onDragLeave && $options.onDragLeave.apply($options, arguments);
    }),
    onDrop: _cache[14] || (_cache[14] = function() {
      return $options.onDrop && $options.onDrop.apply($options, arguments);
    })
  }, _objectSpread$3(_objectSpread$3({}, $options.getColumnPT("root")), $options.getColumnPT("headerCell")), {
    "data-p-sortable-column": $options.columnProp("sortable"),
    "data-p-resizable-column": $props.resizableColumns,
    "data-p-sorted": $options.isColumnSorted(),
    "data-p-filter-column": $props.filterColumn,
    "data-p-frozen-column": $options.columnProp("frozen"),
    "data-p-reorderable-column": $props.reorderableColumns
  }), [$props.resizableColumns && !$options.columnProp("frozen") ? (openBlock(), createElementBlock("span", mergeProps({
    key: 0,
    "class": _ctx.cx("columnResizer"),
    onMousedown: _cache[0] || (_cache[0] = function() {
      return $options.onResizeStart && $options.onResizeStart.apply($options, arguments);
    })
  }, $options.getColumnPT("columnResizer")), null, 16)) : createCommentVNode("", true), createBaseVNode("div", mergeProps({
    "class": _ctx.cx("columnHeaderContent")
  }, $options.getColumnPT("columnHeaderContent")), [$props.column.children && $props.column.children.header ? (openBlock(), createBlock(resolveDynamicComponent($props.column.children.header), {
    key: 0,
    column: $props.column
  }, null, 8, ["column"])) : createCommentVNode("", true), $options.columnProp("header") ? (openBlock(), createElementBlock("span", mergeProps({
    key: 1,
    "class": _ctx.cx("columnTitle")
  }, $options.getColumnPT("columnTitle")), toDisplayString($options.columnProp("header")), 17)) : createCommentVNode("", true), $options.columnProp("sortable") ? (openBlock(), createElementBlock("span", normalizeProps(mergeProps({
    key: 2
  }, $options.getColumnPT("sort"))), [(openBlock(), createBlock(resolveDynamicComponent($props.column.children && $props.column.children.sorticon || $options.sortableColumnIcon), mergeProps({
    sorted: $options.sortState.sorted,
    sortOrder: $options.sortState.sortOrder,
    "class": _ctx.cx("sortIcon")
  }, $options.getColumnPT("sorticon")), null, 16, ["sorted", "sortOrder", "class"]))], 16)) : createCommentVNode("", true), $options.isMultiSorted() ? (openBlock(), createBlock(_component_Badge, {
    key: 3,
    "class": normalizeClass(_ctx.cx("pcSortBadge")),
    pt: $options.getColumnPT("pcSortBadge"),
    value: $options.getBadgeValue(),
    size: "small"
  }, null, 8, ["class", "pt", "value"])) : createCommentVNode("", true), $options.columnProp("selectionMode") === "multiple" && $props.filterDisplay !== "row" ? (openBlock(), createBlock(_component_DTHeaderCheckbox, {
    key: 4,
    checked: $props.allRowsSelected,
    onChange: $options.onHeaderCheckboxChange,
    disabled: $props.empty,
    headerCheckboxIconTemplate: $props.column.children && $props.column.children.headercheckboxicon,
    column: $props.column,
    unstyled: _ctx.unstyled,
    pt: _ctx.pt
  }, null, 8, ["checked", "onChange", "disabled", "headerCheckboxIconTemplate", "column", "unstyled", "pt"])) : createCommentVNode("", true), $props.filterDisplay === "menu" && $props.column.children && $props.column.children.filter ? (openBlock(), createBlock(_component_DTColumnFilter, {
    key: 5,
    field: $options.columnProp("filterField") || $options.columnProp("field"),
    type: $options.columnProp("dataType"),
    display: "menu",
    showMenu: $options.columnProp("showFilterMenu"),
    filterElement: $props.column.children && $props.column.children.filter,
    filterHeaderTemplate: $props.column.children && $props.column.children.filterheader,
    filterFooterTemplate: $props.column.children && $props.column.children.filterfooter,
    filterClearTemplate: $props.column.children && $props.column.children.filterclear,
    filterApplyTemplate: $props.column.children && $props.column.children.filterapply,
    filterIconTemplate: $props.column.children && $props.column.children.filtericon,
    filterAddIconTemplate: $props.column.children && $props.column.children.filteraddicon,
    filterRemoveIconTemplate: $props.column.children && $props.column.children.filterremoveicon,
    filterClearIconTemplate: $props.column.children && $props.column.children.filterclearicon,
    filters: $props.filters,
    filtersStore: $props.filtersStore,
    filterInputProps: $props.filterInputProps,
    filterButtonProps: $props.filterButtonProps,
    onFilterChange: _cache[1] || (_cache[1] = function($event) {
      return _ctx.$emit("filter-change", $event);
    }),
    onFilterApply: _cache[2] || (_cache[2] = function($event) {
      return _ctx.$emit("filter-apply");
    }),
    filterMenuStyle: $options.columnProp("filterMenuStyle"),
    filterMenuClass: $options.columnProp("filterMenuClass"),
    showOperator: $options.columnProp("showFilterOperator"),
    showClearButton: $options.columnProp("showClearButton"),
    showApplyButton: $options.columnProp("showApplyButton"),
    showMatchModes: $options.columnProp("showFilterMatchModes"),
    showAddButton: $options.columnProp("showAddButton"),
    matchModeOptions: $options.columnProp("filterMatchModeOptions"),
    maxConstraints: $options.columnProp("maxConstraints"),
    onOperatorChange: _cache[3] || (_cache[3] = function($event) {
      return _ctx.$emit("operator-change", $event);
    }),
    onMatchmodeChange: _cache[4] || (_cache[4] = function($event) {
      return _ctx.$emit("matchmode-change", $event);
    }),
    onConstraintAdd: _cache[5] || (_cache[5] = function($event) {
      return _ctx.$emit("constraint-add", $event);
    }),
    onConstraintRemove: _cache[6] || (_cache[6] = function($event) {
      return _ctx.$emit("constraint-remove", $event);
    }),
    onApplyClick: _cache[7] || (_cache[7] = function($event) {
      return _ctx.$emit("apply-click", $event);
    }),
    column: $props.column,
    unstyled: _ctx.unstyled,
    pt: _ctx.pt
  }, null, 8, ["field", "type", "showMenu", "filterElement", "filterHeaderTemplate", "filterFooterTemplate", "filterClearTemplate", "filterApplyTemplate", "filterIconTemplate", "filterAddIconTemplate", "filterRemoveIconTemplate", "filterClearIconTemplate", "filters", "filtersStore", "filterInputProps", "filterButtonProps", "filterMenuStyle", "filterMenuClass", "showOperator", "showClearButton", "showApplyButton", "showMatchModes", "showAddButton", "matchModeOptions", "maxConstraints", "column", "unstyled", "pt"])) : createCommentVNode("", true)], 16)], 16, _hoisted_1);
}
__name(render$2, "render$2");
script$2.render = render$2;
var script$1 = {
  name: "TableHeader",
  hostName: "DataTable",
  "extends": script$s,
  emits: ["column-click", "column-mousedown", "column-dragstart", "column-dragover", "column-dragleave", "column-drop", "column-resizestart", "checkbox-change", "filter-change", "filter-apply", "operator-change", "matchmode-change", "constraint-add", "constraint-remove", "filter-clear", "apply-click"],
  props: {
    columnGroup: {
      type: null,
      "default": null
    },
    columns: {
      type: null,
      "default": null
    },
    rowGroupMode: {
      type: String,
      "default": null
    },
    groupRowsBy: {
      type: [Array, String, Function],
      "default": null
    },
    resizableColumns: {
      type: Boolean,
      "default": false
    },
    allRowsSelected: {
      type: Boolean,
      "default": false
    },
    empty: {
      type: Boolean,
      "default": false
    },
    sortMode: {
      type: String,
      "default": "single"
    },
    groupRowSortField: {
      type: [String, Function],
      "default": null
    },
    sortField: {
      type: [String, Function],
      "default": null
    },
    sortOrder: {
      type: Number,
      "default": null
    },
    multiSortMeta: {
      type: Array,
      "default": null
    },
    filterDisplay: {
      type: String,
      "default": null
    },
    filters: {
      type: Object,
      "default": null
    },
    filtersStore: {
      type: Object,
      "default": null
    },
    reorderableColumns: {
      type: Boolean,
      "default": false
    },
    first: {
      type: Number,
      "default": 0
    },
    filterInputProps: {
      type: null,
      "default": null
    },
    filterButtonProps: {
      type: null,
      "default": null
    }
  },
  provide: /* @__PURE__ */ __name(function provide6() {
    return {
      $rows: this.d_headerRows,
      $columns: this.d_headerColumns
    };
  }, "provide"),
  data: /* @__PURE__ */ __name(function data10() {
    return {
      d_headerRows: new _default({
        type: "Row"
      }),
      d_headerColumns: new _default({
        type: "Column"
      })
    };
  }, "data"),
  beforeUnmount: /* @__PURE__ */ __name(function beforeUnmount4() {
    this.d_headerRows.clear();
    this.d_headerColumns.clear();
  }, "beforeUnmount"),
  methods: {
    columnProp: /* @__PURE__ */ __name(function columnProp6(col, prop) {
      return getVNodeProp(col, prop);
    }, "columnProp"),
    getColumnGroupPT: /* @__PURE__ */ __name(function getColumnGroupPT2(key) {
      var _this$$parentInstance;
      var columnGroupMetaData = {
        props: this.getColumnGroupProps(),
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          type: "header",
          scrollable: (_this$$parentInstance = this.$parentInstance) === null || _this$$parentInstance === void 0 || (_this$$parentInstance = _this$$parentInstance.$parentInstance) === null || _this$$parentInstance === void 0 ? void 0 : _this$$parentInstance.scrollable
        }
      };
      return mergeProps(this.ptm("columnGroup.".concat(key), {
        columnGroup: columnGroupMetaData
      }), this.ptm("columnGroup.".concat(key), columnGroupMetaData), this.ptmo(this.getColumnGroupProps(), key, columnGroupMetaData));
    }, "getColumnGroupPT"),
    getColumnGroupProps: /* @__PURE__ */ __name(function getColumnGroupProps2() {
      return this.columnGroup && this.columnGroup.props && this.columnGroup.props.pt ? this.columnGroup.props.pt : void 0;
    }, "getColumnGroupProps"),
    getRowPT: /* @__PURE__ */ __name(function getRowPT2(row2, key, index) {
      var rowMetaData = {
        props: row2.props,
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          index
        }
      };
      return mergeProps(this.ptm("row.".concat(key), {
        row: rowMetaData
      }), this.ptm("row.".concat(key), rowMetaData), this.ptmo(this.getRowProp(row2), key, rowMetaData));
    }, "getRowPT"),
    getRowProp: /* @__PURE__ */ __name(function getRowProp2(row2) {
      return row2.props && row2.props.pt ? row2.props.pt : void 0;
    }, "getRowProp"),
    getColumnPT: /* @__PURE__ */ __name(function getColumnPT9(column, key, index) {
      var columnMetaData = {
        props: column.props,
        parent: {
          instance: this,
          props: this.$props,
          state: this.$data
        },
        context: {
          index
        }
      };
      return mergeProps(this.ptm("column.".concat(key), {
        column: columnMetaData
      }), this.ptm("column.".concat(key), columnMetaData), this.ptmo(this.getColumnProp(column), key, columnMetaData));
    }, "getColumnPT"),
    getColumnProp: /* @__PURE__ */ __name(function getColumnProp8(column) {
      return column.props && column.props.pt ? column.props.pt : void 0;
    }, "getColumnProp"),
    getFilterColumnHeaderClass: /* @__PURE__ */ __name(function getFilterColumnHeaderClass(column) {
      return [this.cx("headerCell", {
        column
      }), this.columnProp(column, "filterHeaderClass"), this.columnProp(column, "class")];
    }, "getFilterColumnHeaderClass"),
    getFilterColumnHeaderStyle: /* @__PURE__ */ __name(function getFilterColumnHeaderStyle(column) {
      return [this.columnProp(column, "filterHeaderStyle"), this.columnProp(column, "style")];
    }, "getFilterColumnHeaderStyle"),
    getHeaderRows: /* @__PURE__ */ __name(function getHeaderRows() {
      var _this$d_headerRows;
      return (_this$d_headerRows = this.d_headerRows) === null || _this$d_headerRows === void 0 ? void 0 : _this$d_headerRows.get(this.columnGroup, this.columnGroup.children);
    }, "getHeaderRows"),
    getHeaderColumns: /* @__PURE__ */ __name(function getHeaderColumns(row2) {
      var _this$d_headerColumns;
      return (_this$d_headerColumns = this.d_headerColumns) === null || _this$d_headerColumns === void 0 ? void 0 : _this$d_headerColumns.get(row2, row2.children);
    }, "getHeaderColumns")
  },
  computed: {
    ptmTHeadOptions: /* @__PURE__ */ __name(function ptmTHeadOptions() {
      var _this$$parentInstance2;
      return {
        context: {
          scrollable: (_this$$parentInstance2 = this.$parentInstance) === null || _this$$parentInstance2 === void 0 || (_this$$parentInstance2 = _this$$parentInstance2.$parentInstance) === null || _this$$parentInstance2 === void 0 ? void 0 : _this$$parentInstance2.scrollable
        }
      };
    }, "ptmTHeadOptions")
  },
  components: {
    DTHeaderCell: script$2,
    DTHeaderCheckbox: script$3,
    DTColumnFilter: script$4
  }
};
function _typeof$2(o) {
  "@babel/helpers - typeof";
  return _typeof$2 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$2(o);
}
__name(_typeof$2, "_typeof$2");
function ownKeys$2(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
__name(ownKeys$2, "ownKeys$2");
function _objectSpread$2(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys$2(Object(t), true).forEach(function(r2) {
      _defineProperty$2(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$2(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
__name(_objectSpread$2, "_objectSpread$2");
function _defineProperty$2(e, r, t) {
  return (r = _toPropertyKey$2(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty$2, "_defineProperty$2");
function _toPropertyKey$2(t) {
  var i = _toPrimitive$2(t, "string");
  return "symbol" == _typeof$2(i) ? i : i + "";
}
__name(_toPropertyKey$2, "_toPropertyKey$2");
function _toPrimitive$2(t, r) {
  if ("object" != _typeof$2(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof$2(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive$2, "_toPrimitive$2");
function render$1(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_DTHeaderCell = resolveComponent("DTHeaderCell");
  var _component_DTHeaderCheckbox = resolveComponent("DTHeaderCheckbox");
  var _component_DTColumnFilter = resolveComponent("DTColumnFilter");
  return openBlock(), createElementBlock("thead", mergeProps({
    "class": _ctx.cx("thead"),
    style: _ctx.sx("thead"),
    role: "rowgroup"
  }, $props.columnGroup ? _objectSpread$2(_objectSpread$2({}, _ctx.ptm("thead", $options.ptmTHeadOptions)), $options.getColumnGroupPT("root")) : _ctx.ptm("thead", $options.ptmTHeadOptions), {
    "data-pc-section": "thead"
  }), [!$props.columnGroup ? (openBlock(), createElementBlock(Fragment, {
    key: 0
  }, [createBaseVNode("tr", mergeProps({
    role: "row"
  }, _ctx.ptm("headerRow")), [(openBlock(true), createElementBlock(Fragment, null, renderList($props.columns, function(col, i) {
    return openBlock(), createElementBlock(Fragment, {
      key: $options.columnProp(col, "columnKey") || $options.columnProp(col, "field") || i
    }, [!$options.columnProp(col, "hidden") && ($props.rowGroupMode !== "subheader" || $props.groupRowsBy !== $options.columnProp(col, "field")) ? (openBlock(), createBlock(_component_DTHeaderCell, {
      key: 0,
      column: col,
      index: i,
      onColumnClick: _cache[0] || (_cache[0] = function($event) {
        return _ctx.$emit("column-click", $event);
      }),
      onColumnMousedown: _cache[1] || (_cache[1] = function($event) {
        return _ctx.$emit("column-mousedown", $event);
      }),
      onColumnDragstart: _cache[2] || (_cache[2] = function($event) {
        return _ctx.$emit("column-dragstart", $event);
      }),
      onColumnDragover: _cache[3] || (_cache[3] = function($event) {
        return _ctx.$emit("column-dragover", $event);
      }),
      onColumnDragleave: _cache[4] || (_cache[4] = function($event) {
        return _ctx.$emit("column-dragleave", $event);
      }),
      onColumnDrop: _cache[5] || (_cache[5] = function($event) {
        return _ctx.$emit("column-drop", $event);
      }),
      groupRowsBy: $props.groupRowsBy,
      groupRowSortField: $props.groupRowSortField,
      reorderableColumns: $props.reorderableColumns,
      resizableColumns: $props.resizableColumns,
      onColumnResizestart: _cache[6] || (_cache[6] = function($event) {
        return _ctx.$emit("column-resizestart", $event);
      }),
      sortMode: $props.sortMode,
      sortField: $props.sortField,
      sortOrder: $props.sortOrder,
      multiSortMeta: $props.multiSortMeta,
      allRowsSelected: $props.allRowsSelected,
      empty: $props.empty,
      onCheckboxChange: _cache[7] || (_cache[7] = function($event) {
        return _ctx.$emit("checkbox-change", $event);
      }),
      filters: $props.filters,
      filterDisplay: $props.filterDisplay,
      filtersStore: $props.filtersStore,
      filterInputProps: $props.filterInputProps,
      filterButtonProps: $props.filterButtonProps,
      first: $props.first,
      onFilterChange: _cache[8] || (_cache[8] = function($event) {
        return _ctx.$emit("filter-change", $event);
      }),
      onFilterApply: _cache[9] || (_cache[9] = function($event) {
        return _ctx.$emit("filter-apply");
      }),
      onOperatorChange: _cache[10] || (_cache[10] = function($event) {
        return _ctx.$emit("operator-change", $event);
      }),
      onMatchmodeChange: _cache[11] || (_cache[11] = function($event) {
        return _ctx.$emit("matchmode-change", $event);
      }),
      onConstraintAdd: _cache[12] || (_cache[12] = function($event) {
        return _ctx.$emit("constraint-add", $event);
      }),
      onConstraintRemove: _cache[13] || (_cache[13] = function($event) {
        return _ctx.$emit("constraint-remove", $event);
      }),
      onApplyClick: _cache[14] || (_cache[14] = function($event) {
        return _ctx.$emit("apply-click", $event);
      }),
      unstyled: _ctx.unstyled,
      pt: _ctx.pt
    }, null, 8, ["column", "index", "groupRowsBy", "groupRowSortField", "reorderableColumns", "resizableColumns", "sortMode", "sortField", "sortOrder", "multiSortMeta", "allRowsSelected", "empty", "filters", "filterDisplay", "filtersStore", "filterInputProps", "filterButtonProps", "first", "unstyled", "pt"])) : createCommentVNode("", true)], 64);
  }), 128))], 16), $props.filterDisplay === "row" ? (openBlock(), createElementBlock("tr", mergeProps({
    key: 0,
    role: "row"
  }, _ctx.ptm("headerRow")), [(openBlock(true), createElementBlock(Fragment, null, renderList($props.columns, function(col, i) {
    return openBlock(), createElementBlock(Fragment, {
      key: $options.columnProp(col, "columnKey") || $options.columnProp(col, "field") || i
    }, [!$options.columnProp(col, "hidden") && ($props.rowGroupMode !== "subheader" || $props.groupRowsBy !== $options.columnProp(col, "field")) ? (openBlock(), createElementBlock("th", mergeProps({
      key: 0,
      style: $options.getFilterColumnHeaderStyle(col),
      "class": $options.getFilterColumnHeaderClass(col),
      ref_for: true
    }, _objectSpread$2(_objectSpread$2({}, $options.getColumnPT(col, "root", i)), $options.getColumnPT(col, "headerCell", i))), [$options.columnProp(col, "selectionMode") === "multiple" ? (openBlock(), createBlock(_component_DTHeaderCheckbox, {
      key: 0,
      checked: $props.allRowsSelected,
      disabled: $props.empty,
      onChange: _cache[15] || (_cache[15] = function($event) {
        return _ctx.$emit("checkbox-change", $event);
      }),
      column: col,
      unstyled: _ctx.unstyled,
      pt: _ctx.pt
    }, null, 8, ["checked", "disabled", "column", "unstyled", "pt"])) : createCommentVNode("", true), col.children && col.children.filter ? (openBlock(), createBlock(_component_DTColumnFilter, {
      key: 1,
      field: $options.columnProp(col, "filterField") || $options.columnProp(col, "field"),
      type: $options.columnProp(col, "dataType"),
      display: "row",
      showMenu: $options.columnProp(col, "showFilterMenu"),
      filterElement: col.children && col.children.filter,
      filterHeaderTemplate: col.children && col.children.filterheader,
      filterFooterTemplate: col.children && col.children.filterfooter,
      filterClearTemplate: col.children && col.children.filterclear,
      filterApplyTemplate: col.children && col.children.filterapply,
      filterIconTemplate: col.children && col.children.filtericon,
      filterAddIconTemplate: col.children && col.children.filteraddicon,
      filterRemoveIconTemplate: col.children && col.children.filterremoveicon,
      filterClearIconTemplate: col.children && col.children.filterclearicon,
      filters: $props.filters,
      filtersStore: $props.filtersStore,
      filterInputProps: $props.filterInputProps,
      filterButtonProps: $props.filterButtonProps,
      onFilterChange: _cache[16] || (_cache[16] = function($event) {
        return _ctx.$emit("filter-change", $event);
      }),
      onFilterApply: _cache[17] || (_cache[17] = function($event) {
        return _ctx.$emit("filter-apply");
      }),
      filterMenuStyle: $options.columnProp(col, "filterMenuStyle"),
      filterMenuClass: $options.columnProp(col, "filterMenuClass"),
      showOperator: $options.columnProp(col, "showFilterOperator"),
      showClearButton: $options.columnProp(col, "showClearButton"),
      showApplyButton: $options.columnProp(col, "showApplyButton"),
      showMatchModes: $options.columnProp(col, "showFilterMatchModes"),
      showAddButton: $options.columnProp(col, "showAddButton"),
      matchModeOptions: $options.columnProp(col, "filterMatchModeOptions"),
      maxConstraints: $options.columnProp(col, "maxConstraints"),
      onOperatorChange: _cache[18] || (_cache[18] = function($event) {
        return _ctx.$emit("operator-change", $event);
      }),
      onMatchmodeChange: _cache[19] || (_cache[19] = function($event) {
        return _ctx.$emit("matchmode-change", $event);
      }),
      onConstraintAdd: _cache[20] || (_cache[20] = function($event) {
        return _ctx.$emit("constraint-add", $event);
      }),
      onConstraintRemove: _cache[21] || (_cache[21] = function($event) {
        return _ctx.$emit("constraint-remove", $event);
      }),
      onApplyClick: _cache[22] || (_cache[22] = function($event) {
        return _ctx.$emit("apply-click", $event);
      }),
      column: col,
      unstyled: _ctx.unstyled,
      pt: _ctx.pt
    }, null, 8, ["field", "type", "showMenu", "filterElement", "filterHeaderTemplate", "filterFooterTemplate", "filterClearTemplate", "filterApplyTemplate", "filterIconTemplate", "filterAddIconTemplate", "filterRemoveIconTemplate", "filterClearIconTemplate", "filters", "filtersStore", "filterInputProps", "filterButtonProps", "filterMenuStyle", "filterMenuClass", "showOperator", "showClearButton", "showApplyButton", "showMatchModes", "showAddButton", "matchModeOptions", "maxConstraints", "column", "unstyled", "pt"])) : createCommentVNode("", true)], 16)) : createCommentVNode("", true)], 64);
  }), 128))], 16)) : createCommentVNode("", true)], 64)) : (openBlock(true), createElementBlock(Fragment, {
    key: 1
  }, renderList($options.getHeaderRows(), function(row2, i) {
    return openBlock(), createElementBlock("tr", mergeProps({
      key: i,
      role: "row",
      ref_for: true
    }, _objectSpread$2(_objectSpread$2({}, _ctx.ptm("headerRow")), $options.getRowPT(row2, "root", i))), [(openBlock(true), createElementBlock(Fragment, null, renderList($options.getHeaderColumns(row2), function(col, j) {
      return openBlock(), createElementBlock(Fragment, {
        key: $options.columnProp(col, "columnKey") || $options.columnProp(col, "field") || j
      }, [!$options.columnProp(col, "hidden") && ($props.rowGroupMode !== "subheader" || $props.groupRowsBy !== $options.columnProp(col, "field")) && typeof col.children !== "string" ? (openBlock(), createBlock(_component_DTHeaderCell, {
        key: 0,
        column: col,
        onColumnClick: _cache[23] || (_cache[23] = function($event) {
          return _ctx.$emit("column-click", $event);
        }),
        onColumnMousedown: _cache[24] || (_cache[24] = function($event) {
          return _ctx.$emit("column-mousedown", $event);
        }),
        groupRowsBy: $props.groupRowsBy,
        groupRowSortField: $props.groupRowSortField,
        sortMode: $props.sortMode,
        sortField: $props.sortField,
        sortOrder: $props.sortOrder,
        multiSortMeta: $props.multiSortMeta,
        allRowsSelected: $props.allRowsSelected,
        empty: $props.empty,
        onCheckboxChange: _cache[25] || (_cache[25] = function($event) {
          return _ctx.$emit("checkbox-change", $event);
        }),
        filters: $props.filters,
        filterDisplay: $props.filterDisplay,
        filtersStore: $props.filtersStore,
        onFilterChange: _cache[26] || (_cache[26] = function($event) {
          return _ctx.$emit("filter-change", $event);
        }),
        onFilterApply: _cache[27] || (_cache[27] = function($event) {
          return _ctx.$emit("filter-apply");
        }),
        onOperatorChange: _cache[28] || (_cache[28] = function($event) {
          return _ctx.$emit("operator-change", $event);
        }),
        onMatchmodeChange: _cache[29] || (_cache[29] = function($event) {
          return _ctx.$emit("matchmode-change", $event);
        }),
        onConstraintAdd: _cache[30] || (_cache[30] = function($event) {
          return _ctx.$emit("constraint-add", $event);
        }),
        onConstraintRemove: _cache[31] || (_cache[31] = function($event) {
          return _ctx.$emit("constraint-remove", $event);
        }),
        onApplyClick: _cache[32] || (_cache[32] = function($event) {
          return _ctx.$emit("apply-click", $event);
        }),
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["column", "groupRowsBy", "groupRowSortField", "sortMode", "sortField", "sortOrder", "multiSortMeta", "allRowsSelected", "empty", "filters", "filterDisplay", "filtersStore", "unstyled", "pt"])) : createCommentVNode("", true)], 64);
    }), 128))], 16);
  }), 128))], 16);
}
__name(render$1, "render$1");
script$1.render = render$1;
function _typeof$1(o) {
  "@babel/helpers - typeof";
  return _typeof$1 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof$1(o);
}
__name(_typeof$1, "_typeof$1");
var _excluded = ["expanded"];
function _objectWithoutProperties(e, t) {
  if (null == e) return {};
  var o, r, i = _objectWithoutPropertiesLoose(e, t);
  if (Object.getOwnPropertySymbols) {
    var s = Object.getOwnPropertySymbols(e);
    for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);
  }
  return i;
}
__name(_objectWithoutProperties, "_objectWithoutProperties");
function _objectWithoutPropertiesLoose(r, e) {
  if (null == r) return {};
  var t = {};
  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {
    if (e.includes(n)) continue;
    t[n] = r[n];
  }
  return t;
}
__name(_objectWithoutPropertiesLoose, "_objectWithoutPropertiesLoose");
function ownKeys$1(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
__name(ownKeys$1, "ownKeys$1");
function _objectSpread$1(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys$1(Object(t), true).forEach(function(r2) {
      _defineProperty$1(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
__name(_objectSpread$1, "_objectSpread$1");
function _defineProperty$1(e, r, t) {
  return (r = _toPropertyKey$1(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty$1, "_defineProperty$1");
function _toPropertyKey$1(t) {
  var i = _toPrimitive$1(t, "string");
  return "symbol" == _typeof$1(i) ? i : i + "";
}
__name(_toPropertyKey$1, "_toPropertyKey$1");
function _toPrimitive$1(t, r) {
  if ("object" != _typeof$1(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof$1(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive$1, "_toPrimitive$1");
function _slicedToArray(r, e) {
  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();
}
__name(_slicedToArray, "_slicedToArray");
function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
__name(_nonIterableRest, "_nonIterableRest");
function _iterableToArrayLimit(r, l) {
  var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
  if (null != t) {
    var e, n, i, u, a = [], f = true, o = false;
    try {
      if (i = (t = t.call(r)).next, 0 === l) ;
      else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = true) ;
    } catch (r2) {
      o = true, n = r2;
    } finally {
      try {
        if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return;
      } finally {
        if (o) throw n;
      }
    }
    return a;
  }
}
__name(_iterableToArrayLimit, "_iterableToArrayLimit");
function _arrayWithHoles(r) {
  if (Array.isArray(r)) return r;
}
__name(_arrayWithHoles, "_arrayWithHoles");
function _createForOfIteratorHelper(r, e) {
  var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
  if (!t) {
    if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e) {
      t && (r = t);
      var _n = 0, F = /* @__PURE__ */ __name(function F2() {
      }, "F");
      return { s: F, n: /* @__PURE__ */ __name(function n() {
        return _n >= r.length ? { done: true } : { done: false, value: r[_n++] };
      }, "n"), e: /* @__PURE__ */ __name(function e2(r2) {
        throw r2;
      }, "e"), f: F };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var o, a = true, u = false;
  return { s: /* @__PURE__ */ __name(function s() {
    t = t.call(r);
  }, "s"), n: /* @__PURE__ */ __name(function n() {
    var r2 = t.next();
    return a = r2.done, r2;
  }, "n"), e: /* @__PURE__ */ __name(function e2(r2) {
    u = true, o = r2;
  }, "e"), f: /* @__PURE__ */ __name(function f() {
    try {
      a || null == t["return"] || t["return"]();
    } finally {
      if (u) throw o;
    }
  }, "f") };
}
__name(_createForOfIteratorHelper, "_createForOfIteratorHelper");
function _toConsumableArray(r) {
  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();
}
__name(_toConsumableArray, "_toConsumableArray");
function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
__name(_nonIterableSpread, "_nonIterableSpread");
function _unsupportedIterableToArray(r, a) {
  if (r) {
    if ("string" == typeof r) return _arrayLikeToArray(r, a);
    var t = {}.toString.call(r).slice(8, -1);
    return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;
  }
}
__name(_unsupportedIterableToArray, "_unsupportedIterableToArray");
function _iterableToArray(r) {
  if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r);
}
__name(_iterableToArray, "_iterableToArray");
function _arrayWithoutHoles(r) {
  if (Array.isArray(r)) return _arrayLikeToArray(r);
}
__name(_arrayWithoutHoles, "_arrayWithoutHoles");
function _arrayLikeToArray(r, a) {
  (null == a || a > r.length) && (a = r.length);
  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
  return n;
}
__name(_arrayLikeToArray, "_arrayLikeToArray");
var script = {
  name: "DataTable",
  "extends": script$c,
  inheritAttrs: false,
  emits: ["value-change", "update:first", "update:rows", "page", "update:sortField", "update:sortOrder", "update:multiSortMeta", "sort", "filter", "row-click", "row-dblclick", "update:selection", "row-select", "row-unselect", "update:contextMenuSelection", "row-contextmenu", "row-unselect-all", "row-select-all", "select-all-change", "column-resize-end", "column-reorder", "row-reorder", "update:expandedRows", "row-collapse", "row-expand", "update:expandedRowGroups", "rowgroup-collapse", "rowgroup-expand", "update:filters", "state-restore", "state-save", "cell-edit-init", "cell-edit-complete", "cell-edit-cancel", "update:editingRows", "row-edit-init", "row-edit-save", "row-edit-cancel"],
  provide: /* @__PURE__ */ __name(function provide7() {
    return {
      $columns: this.d_columns,
      $columnGroups: this.d_columnGroups
    };
  }, "provide"),
  data: /* @__PURE__ */ __name(function data11() {
    return {
      d_first: this.first,
      d_rows: this.rows,
      d_sortField: this.sortField,
      d_sortOrder: this.sortOrder,
      d_nullSortOrder: this.nullSortOrder,
      d_multiSortMeta: this.multiSortMeta ? _toConsumableArray(this.multiSortMeta) : [],
      d_groupRowsSortMeta: null,
      d_selectionKeys: null,
      d_columnOrder: null,
      d_editingRowKeys: null,
      d_editingMeta: {},
      d_filters: this.cloneFilters(this.filters),
      d_columns: new _default({
        type: "Column"
      }),
      d_columnGroups: new _default({
        type: "ColumnGroup"
      })
    };
  }, "data"),
  rowTouched: false,
  anchorRowIndex: null,
  rangeRowIndex: null,
  documentColumnResizeListener: null,
  documentColumnResizeEndListener: null,
  lastResizeHelperX: null,
  resizeColumnElement: null,
  columnResizing: false,
  colReorderIconWidth: null,
  colReorderIconHeight: null,
  draggedColumn: null,
  draggedColumnElement: null,
  draggedRowIndex: null,
  droppedRowIndex: null,
  rowDragging: null,
  columnWidthsState: null,
  tableWidthState: null,
  columnWidthsRestored: false,
  watch: {
    first: /* @__PURE__ */ __name(function first3(newValue) {
      this.d_first = newValue;
    }, "first"),
    rows: /* @__PURE__ */ __name(function rows2(newValue) {
      this.d_rows = newValue;
    }, "rows"),
    sortField: /* @__PURE__ */ __name(function sortField(newValue) {
      this.d_sortField = newValue;
    }, "sortField"),
    sortOrder: /* @__PURE__ */ __name(function sortOrder(newValue) {
      this.d_sortOrder = newValue;
    }, "sortOrder"),
    nullSortOrder: /* @__PURE__ */ __name(function nullSortOrder(newValue) {
      this.d_nullSortOrder = newValue;
    }, "nullSortOrder"),
    multiSortMeta: /* @__PURE__ */ __name(function multiSortMeta(newValue) {
      this.d_multiSortMeta = newValue;
    }, "multiSortMeta"),
    selection: {
      immediate: true,
      handler: /* @__PURE__ */ __name(function handler2(newValue) {
        if (this.dataKey) {
          this.updateSelectionKeys(newValue);
        }
      }, "handler")
    },
    editingRows: {
      immediate: true,
      handler: /* @__PURE__ */ __name(function handler3(newValue) {
        if (this.dataKey) {
          this.updateEditingRowKeys(newValue);
        }
      }, "handler")
    },
    filters: {
      deep: true,
      handler: /* @__PURE__ */ __name(function handler4(newValue) {
        this.d_filters = this.cloneFilters(newValue);
      }, "handler")
    }
  },
  mounted: /* @__PURE__ */ __name(function mounted8() {
    this.$el.setAttribute(this.attributeSelector, "");
    if (this.isStateful()) {
      this.restoreState();
      this.resizableColumns && this.restoreColumnWidths();
    }
    if (this.editMode === "row" && this.dataKey && !this.d_editingRowKeys) {
      this.updateEditingRowKeys(this.editingRows);
    }
  }, "mounted"),
  beforeUnmount: /* @__PURE__ */ __name(function beforeUnmount5() {
    this.unbindColumnResizeEvents();
    this.destroyStyleElement();
    this.d_columns.clear();
    this.d_columnGroups.clear();
  }, "beforeUnmount"),
  updated: /* @__PURE__ */ __name(function updated5() {
    if (this.isStateful()) {
      this.saveState();
    }
    if (this.editMode === "row" && this.dataKey && !this.d_editingRowKeys) {
      this.updateEditingRowKeys(this.editingRows);
    }
  }, "updated"),
  methods: {
    columnProp: /* @__PURE__ */ __name(function columnProp7(col, prop) {
      return getVNodeProp(col, prop);
    }, "columnProp"),
    onPage: /* @__PURE__ */ __name(function onPage(event2) {
      var _this = this;
      this.clearEditingMetaData();
      this.d_first = event2.first;
      this.d_rows = event2.rows;
      var pageEvent = this.createLazyLoadEvent(event2);
      pageEvent.pageCount = event2.pageCount;
      pageEvent.page = event2.page;
      this.$emit("update:first", this.d_first);
      this.$emit("update:rows", this.d_rows);
      this.$emit("page", pageEvent);
      this.$nextTick(function() {
        _this.$emit("value-change", _this.processedData);
      });
    }, "onPage"),
    onColumnHeaderClick: /* @__PURE__ */ __name(function onColumnHeaderClick(e) {
      var _this2 = this;
      var event2 = e.originalEvent;
      var column = e.column;
      if (this.columnProp(column, "sortable")) {
        var targetNode = event2.target;
        var columnField = this.columnProp(column, "sortField") || this.columnProp(column, "field");
        if (getAttribute(targetNode, "data-p-sortable-column") === true || getAttribute(targetNode, "data-pc-section") === "columntitle" || getAttribute(targetNode, "data-pc-section") === "columnheadercontent" || getAttribute(targetNode, "data-pc-section") === "sorticon" || getAttribute(targetNode.parentElement, "data-pc-section") === "sorticon" || getAttribute(targetNode.parentElement.parentElement, "data-pc-section") === "sorticon" || targetNode.closest('[data-p-sortable-column="true"]') && !targetNode.closest('[data-pc-section="columnfilterbutton"]') && !isClickable(event2.target)) {
          clearSelection();
          if (this.sortMode === "single") {
            if (this.d_sortField === columnField) {
              if (this.removableSort && this.d_sortOrder * -1 === this.defaultSortOrder) {
                this.d_sortOrder = null;
                this.d_sortField = null;
              } else {
                this.d_sortOrder = this.d_sortOrder * -1;
              }
            } else {
              this.d_sortOrder = this.defaultSortOrder;
              this.d_sortField = columnField;
            }
            this.$emit("update:sortField", this.d_sortField);
            this.$emit("update:sortOrder", this.d_sortOrder);
            this.resetPage();
          } else if (this.sortMode === "multiple") {
            var metaKey = event2.metaKey || event2.ctrlKey;
            if (!metaKey) {
              this.d_multiSortMeta = this.d_multiSortMeta.filter(function(meta) {
                return meta.field === columnField;
              });
            }
            this.addMultiSortField(columnField);
            this.$emit("update:multiSortMeta", this.d_multiSortMeta);
          }
          this.$emit("sort", this.createLazyLoadEvent(event2));
          this.$nextTick(function() {
            _this2.$emit("value-change", _this2.processedData);
          });
        }
      }
    }, "onColumnHeaderClick"),
    sortSingle: /* @__PURE__ */ __name(function sortSingle(value) {
      var _this3 = this;
      this.clearEditingMetaData();
      if (this.groupRowsBy && this.groupRowsBy === this.sortField) {
        this.d_multiSortMeta = [{
          field: this.sortField,
          order: this.sortOrder || this.defaultSortOrder
        }, {
          field: this.d_sortField,
          order: this.d_sortOrder
        }];
        return this.sortMultiple(value);
      }
      var data12 = _toConsumableArray(value);
      var resolvedFieldData = /* @__PURE__ */ new Map();
      var _iterator = _createForOfIteratorHelper(data12), _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done; ) {
          var item = _step.value;
          resolvedFieldData.set(item, resolveFieldData(item, this.d_sortField));
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      var comparer = localeComparator();
      data12.sort(function(data1, data22) {
        var value1 = resolvedFieldData.get(data1);
        var value2 = resolvedFieldData.get(data22);
        return sort(value1, value2, _this3.d_sortOrder, comparer, _this3.d_nullSortOrder);
      });
      return data12;
    }, "sortSingle"),
    sortMultiple: /* @__PURE__ */ __name(function sortMultiple(value) {
      var _this4 = this;
      this.clearEditingMetaData();
      if (this.groupRowsBy && (this.d_groupRowsSortMeta || this.d_multiSortMeta.length && this.groupRowsBy === this.d_multiSortMeta[0].field)) {
        var firstSortMeta = this.d_multiSortMeta[0];
        !this.d_groupRowsSortMeta && (this.d_groupRowsSortMeta = firstSortMeta);
        if (firstSortMeta.field !== this.d_groupRowsSortMeta.field) {
          this.d_multiSortMeta = [this.d_groupRowsSortMeta].concat(_toConsumableArray(this.d_multiSortMeta));
        }
      }
      var data12 = _toConsumableArray(value);
      data12.sort(function(data1, data22) {
        return _this4.multisortField(data1, data22, 0);
      });
      return data12;
    }, "sortMultiple"),
    multisortField: /* @__PURE__ */ __name(function multisortField(data1, data22, index) {
      var value1 = resolveFieldData(data1, this.d_multiSortMeta[index].field);
      var value2 = resolveFieldData(data22, this.d_multiSortMeta[index].field);
      var comparer = localeComparator();
      if (value1 === value2) {
        return this.d_multiSortMeta.length - 1 > index ? this.multisortField(data1, data22, index + 1) : 0;
      }
      return sort(value1, value2, this.d_multiSortMeta[index].order, comparer, this.d_nullSortOrder);
    }, "multisortField"),
    addMultiSortField: /* @__PURE__ */ __name(function addMultiSortField(field2) {
      var index = this.d_multiSortMeta.findIndex(function(meta) {
        return meta.field === field2;
      });
      if (index >= 0) {
        if (this.removableSort && this.d_multiSortMeta[index].order * -1 === this.defaultSortOrder) this.d_multiSortMeta.splice(index, 1);
        else this.d_multiSortMeta[index] = {
          field: field2,
          order: this.d_multiSortMeta[index].order * -1
        };
      } else {
        this.d_multiSortMeta.push({
          field: field2,
          order: this.defaultSortOrder
        });
      }
      this.d_multiSortMeta = _toConsumableArray(this.d_multiSortMeta);
    }, "addMultiSortField"),
    getActiveFilters: /* @__PURE__ */ __name(function getActiveFilters(filters) {
      var removeEmptyFilters = /* @__PURE__ */ __name(function removeEmptyFilters2(_ref) {
        var _ref2 = _slicedToArray(_ref, 2), key = _ref2[0], value = _ref2[1];
        if (value.constraints) {
          var filteredConstraints = value.constraints.filter(function(constraint) {
            return constraint.value !== null;
          });
          if (filteredConstraints.length > 0) {
            return [key, _objectSpread$1(_objectSpread$1({}, value), {}, {
              constraints: filteredConstraints
            })];
          }
        } else if (value.value !== null) {
          return [key, value];
        }
        return void 0;
      }, "removeEmptyFilters");
      var filterValidEntries = /* @__PURE__ */ __name(function filterValidEntries2(entry) {
        return entry !== void 0;
      }, "filterValidEntries");
      var entries = Object.entries(filters).map(removeEmptyFilters).filter(filterValidEntries);
      return Object.fromEntries(entries);
    }, "getActiveFilters"),
    filter: /* @__PURE__ */ __name(function filter2(data12) {
      var _this5 = this;
      if (!data12) {
        return;
      }
      this.clearEditingMetaData();
      var activeFilters = this.getActiveFilters(this.filters);
      var globalFilterFieldsArray;
      if (activeFilters["global"]) {
        globalFilterFieldsArray = this.globalFilterFields || this.columns.map(function(col) {
          return _this5.columnProp(col, "filterField") || _this5.columnProp(col, "field");
        });
      }
      var filteredValue = [];
      for (var i = 0; i < data12.length; i++) {
        var localMatch = true;
        var globalMatch = false;
        var localFiltered = false;
        for (var prop in activeFilters) {
          if (Object.prototype.hasOwnProperty.call(activeFilters, prop) && prop !== "global") {
            localFiltered = true;
            var filterField = prop;
            var filterMeta = activeFilters[filterField];
            if (filterMeta.operator) {
              var _iterator2 = _createForOfIteratorHelper(filterMeta.constraints), _step2;
              try {
                for (_iterator2.s(); !(_step2 = _iterator2.n()).done; ) {
                  var filterConstraint2 = _step2.value;
                  localMatch = this.executeLocalFilter(filterField, data12[i], filterConstraint2);
                  if (filterMeta.operator === FilterOperator.OR && localMatch || filterMeta.operator === FilterOperator.AND && !localMatch) {
                    break;
                  }
                }
              } catch (err) {
                _iterator2.e(err);
              } finally {
                _iterator2.f();
              }
            } else {
              localMatch = this.executeLocalFilter(filterField, data12[i], filterMeta);
            }
            if (!localMatch) {
              break;
            }
          }
        }
        if (localMatch && activeFilters["global"] && !globalMatch && globalFilterFieldsArray) {
          for (var j = 0; j < globalFilterFieldsArray.length; j++) {
            var globalFilterField = globalFilterFieldsArray[j];
            globalMatch = FilterService.filters[activeFilters["global"].matchMode || FilterMatchMode.CONTAINS](resolveFieldData(data12[i], globalFilterField), activeFilters["global"].value, this.filterLocale);
            if (globalMatch) {
              break;
            }
          }
        }
        var matches = void 0;
        if (activeFilters["global"]) {
          matches = localFiltered ? localFiltered && localMatch && globalMatch : globalMatch;
        } else {
          matches = localFiltered && localMatch;
        }
        if (matches) {
          filteredValue.push(data12[i]);
        }
      }
      if (filteredValue.length === this.value.length || Object.keys(activeFilters).length == 0) {
        filteredValue = data12;
      }
      var filterEvent = this.createLazyLoadEvent();
      filterEvent.filteredValue = filteredValue;
      this.$emit("filter", filterEvent);
      this.$nextTick(function() {
        _this5.$emit("value-change", _this5.processedData);
      });
      return filteredValue;
    }, "filter"),
    executeLocalFilter: /* @__PURE__ */ __name(function executeLocalFilter(field2, rowData, filterMeta) {
      var filterValue = filterMeta.value;
      var filterMatchMode = filterMeta.matchMode || FilterMatchMode.STARTS_WITH;
      var dataFieldValue = resolveFieldData(rowData, field2);
      var filterConstraint2 = FilterService.filters[filterMatchMode];
      return filterConstraint2(dataFieldValue, filterValue, this.filterLocale);
    }, "executeLocalFilter"),
    onRowClick: /* @__PURE__ */ __name(function onRowClick2(e) {
      var event2 = e.originalEvent;
      var body = this.$refs.bodyRef && this.$refs.bodyRef.$el;
      var focusedItem = findSingle(body, 'tr[data-p-selectable-row="true"][tabindex="0"]');
      if (isClickable(event2.target)) {
        return;
      }
      this.$emit("row-click", e);
      if (this.selectionMode) {
        var rowData = e.data;
        var rowIndex2 = this.d_first + e.index;
        if (this.isMultipleSelectionMode() && event2.shiftKey && this.anchorRowIndex != null) {
          clearSelection();
          this.rangeRowIndex = rowIndex2;
          this.selectRange(event2);
        } else {
          var selected = this.isSelected(rowData);
          var metaSelection = this.rowTouched ? false : this.metaKeySelection;
          this.anchorRowIndex = rowIndex2;
          this.rangeRowIndex = rowIndex2;
          if (metaSelection) {
            var metaKey = event2.metaKey || event2.ctrlKey;
            if (selected && metaKey) {
              if (this.isSingleSelectionMode()) {
                this.$emit("update:selection", null);
              } else {
                var selectionIndex = this.findIndexInSelection(rowData);
                var _selection = this.selection.filter(function(val, i) {
                  return i != selectionIndex;
                });
                this.$emit("update:selection", _selection);
              }
              this.$emit("row-unselect", {
                originalEvent: event2,
                data: rowData,
                index: rowIndex2,
                type: "row"
              });
            } else {
              if (this.isSingleSelectionMode()) {
                this.$emit("update:selection", rowData);
              } else if (this.isMultipleSelectionMode()) {
                var _selection2 = metaKey ? this.selection || [] : [];
                _selection2 = [].concat(_toConsumableArray(_selection2), [rowData]);
                this.$emit("update:selection", _selection2);
              }
              this.$emit("row-select", {
                originalEvent: event2,
                data: rowData,
                index: rowIndex2,
                type: "row"
              });
            }
          } else {
            if (this.selectionMode === "single") {
              if (selected) {
                this.$emit("update:selection", null);
                this.$emit("row-unselect", {
                  originalEvent: event2,
                  data: rowData,
                  index: rowIndex2,
                  type: "row"
                });
              } else {
                this.$emit("update:selection", rowData);
                this.$emit("row-select", {
                  originalEvent: event2,
                  data: rowData,
                  index: rowIndex2,
                  type: "row"
                });
              }
            } else if (this.selectionMode === "multiple") {
              if (selected) {
                var _selectionIndex = this.findIndexInSelection(rowData);
                var _selection3 = this.selection.filter(function(val, i) {
                  return i != _selectionIndex;
                });
                this.$emit("update:selection", _selection3);
                this.$emit("row-unselect", {
                  originalEvent: event2,
                  data: rowData,
                  index: rowIndex2,
                  type: "row"
                });
              } else {
                var _selection4 = this.selection ? [].concat(_toConsumableArray(this.selection), [rowData]) : [rowData];
                this.$emit("update:selection", _selection4);
                this.$emit("row-select", {
                  originalEvent: event2,
                  data: rowData,
                  index: rowIndex2,
                  type: "row"
                });
              }
            }
          }
        }
      }
      this.rowTouched = false;
      if (focusedItem) {
        var _event$target, _event$target2, _event$target3;
        if (((_event$target = event2.target) === null || _event$target === void 0 ? void 0 : _event$target.getAttribute("data-pc-section")) === "rowtoggleicon" || ((_event$target2 = event2.target) === null || _event$target2 === void 0 || (_event$target2 = _event$target2.parentElement) === null || _event$target2 === void 0 ? void 0 : _event$target2.getAttribute("data-pc-section")) === "rowtoggleicon") return;
        var targetRow = (_event$target3 = event2.target) === null || _event$target3 === void 0 ? void 0 : _event$target3.closest('tr[data-p-selectable-row="true"]');
        focusedItem.tabIndex = "-1";
        targetRow.tabIndex = "0";
      }
    }, "onRowClick"),
    onRowDblClick: /* @__PURE__ */ __name(function onRowDblClick2(e) {
      var event2 = e.originalEvent;
      if (isClickable(event2.target)) {
        return;
      }
      this.$emit("row-dblclick", e);
    }, "onRowDblClick"),
    onRowRightClick: /* @__PURE__ */ __name(function onRowRightClick2(event2) {
      if (this.contextMenu) {
        clearSelection();
        event2.originalEvent.target.focus();
      }
      this.$emit("update:contextMenuSelection", event2.data);
      this.$emit("row-contextmenu", event2);
    }, "onRowRightClick"),
    onRowTouchEnd: /* @__PURE__ */ __name(function onRowTouchEnd2() {
      this.rowTouched = true;
    }, "onRowTouchEnd"),
    onRowKeyDown: /* @__PURE__ */ __name(function onRowKeyDown2(e, slotProps) {
      var event2 = e.originalEvent;
      var rowData = e.data;
      var rowIndex2 = e.index;
      var metaKey = event2.metaKey || event2.ctrlKey;
      if (this.selectionMode) {
        var row2 = event2.target;
        switch (event2.code) {
          case "ArrowDown":
            this.onArrowDownKey(event2, row2, rowIndex2, slotProps);
            break;
          case "ArrowUp":
            this.onArrowUpKey(event2, row2, rowIndex2, slotProps);
            break;
          case "Home":
            this.onHomeKey(event2, row2, rowIndex2, slotProps);
            break;
          case "End":
            this.onEndKey(event2, row2, rowIndex2, slotProps);
            break;
          case "Enter":
          case "NumpadEnter":
            this.onEnterKey(event2, rowData, rowIndex2);
            break;
          case "Space":
            this.onSpaceKey(event2, rowData, rowIndex2, slotProps);
            break;
          case "Tab":
            this.onTabKey(event2, rowIndex2);
            break;
          default:
            if (event2.code === "KeyA" && metaKey && this.isMultipleSelectionMode()) {
              var data12 = this.dataToRender(slotProps.rows);
              this.$emit("update:selection", data12);
            }
            event2.preventDefault();
            break;
        }
      }
    }, "onRowKeyDown"),
    onArrowDownKey: /* @__PURE__ */ __name(function onArrowDownKey(event2, row2, rowIndex2, slotProps) {
      var nextRow = this.findNextSelectableRow(row2);
      nextRow && this.focusRowChange(row2, nextRow);
      if (event2.shiftKey) {
        var data12 = this.dataToRender(slotProps.rows);
        var nextRowIndex = rowIndex2 + 1 >= data12.length ? data12.length - 1 : rowIndex2 + 1;
        this.onRowClick({
          originalEvent: event2,
          data: data12[nextRowIndex],
          index: nextRowIndex
        });
      }
      event2.preventDefault();
    }, "onArrowDownKey"),
    onArrowUpKey: /* @__PURE__ */ __name(function onArrowUpKey(event2, row2, rowIndex2, slotProps) {
      var prevRow = this.findPrevSelectableRow(row2);
      prevRow && this.focusRowChange(row2, prevRow);
      if (event2.shiftKey) {
        var data12 = this.dataToRender(slotProps.rows);
        var prevRowIndex = rowIndex2 - 1 <= 0 ? 0 : rowIndex2 - 1;
        this.onRowClick({
          originalEvent: event2,
          data: data12[prevRowIndex],
          index: prevRowIndex
        });
      }
      event2.preventDefault();
    }, "onArrowUpKey"),
    onHomeKey: /* @__PURE__ */ __name(function onHomeKey(event2, row2, rowIndex2, slotProps) {
      var firstRow = this.findFirstSelectableRow();
      firstRow && this.focusRowChange(row2, firstRow);
      if (event2.ctrlKey && event2.shiftKey) {
        var data12 = this.dataToRender(slotProps.rows);
        this.$emit("update:selection", data12.slice(0, rowIndex2 + 1));
      }
      event2.preventDefault();
    }, "onHomeKey"),
    onEndKey: /* @__PURE__ */ __name(function onEndKey(event2, row2, rowIndex2, slotProps) {
      var lastRow = this.findLastSelectableRow();
      lastRow && this.focusRowChange(row2, lastRow);
      if (event2.ctrlKey && event2.shiftKey) {
        var data12 = this.dataToRender(slotProps.rows);
        this.$emit("update:selection", data12.slice(rowIndex2, data12.length));
      }
      event2.preventDefault();
    }, "onEndKey"),
    onEnterKey: /* @__PURE__ */ __name(function onEnterKey(event2, rowData, rowIndex2) {
      this.onRowClick({
        originalEvent: event2,
        data: rowData,
        index: rowIndex2
      });
      event2.preventDefault();
    }, "onEnterKey"),
    onSpaceKey: /* @__PURE__ */ __name(function onSpaceKey(event2, rowData, rowIndex2, slotProps) {
      this.onEnterKey(event2, rowData, rowIndex2);
      if (event2.shiftKey && this.selection !== null) {
        var data12 = this.dataToRender(slotProps.rows);
        var index;
        if (this.selection.length > 0) {
          var firstSelectedRowIndex, lastSelectedRowIndex;
          firstSelectedRowIndex = findIndexInList(this.selection[0], data12);
          lastSelectedRowIndex = findIndexInList(this.selection[this.selection.length - 1], data12);
          index = rowIndex2 <= firstSelectedRowIndex ? lastSelectedRowIndex : firstSelectedRowIndex;
        } else {
          index = findIndexInList(this.selection, data12);
        }
        var _selection = index !== rowIndex2 ? data12.slice(Math.min(index, rowIndex2), Math.max(index, rowIndex2) + 1) : rowData;
        this.$emit("update:selection", _selection);
      }
    }, "onSpaceKey"),
    onTabKey: /* @__PURE__ */ __name(function onTabKey(event2, rowIndex2) {
      var body = this.$refs.bodyRef && this.$refs.bodyRef.$el;
      var rows3 = find(body, 'tr[data-p-selectable-row="true"]');
      if (event2.code === "Tab" && rows3 && rows3.length > 0) {
        var firstSelectedRow = findSingle(body, 'tr[data-p-selected="true"]');
        var focusedItem = findSingle(body, 'tr[data-p-selectable-row="true"][tabindex="0"]');
        if (firstSelectedRow) {
          firstSelectedRow.tabIndex = "0";
          focusedItem && focusedItem !== firstSelectedRow && (focusedItem.tabIndex = "-1");
        } else {
          rows3[0].tabIndex = "0";
          focusedItem !== rows3[0] && (rows3[rowIndex2].tabIndex = "-1");
        }
      }
    }, "onTabKey"),
    findNextSelectableRow: /* @__PURE__ */ __name(function findNextSelectableRow(row2) {
      var nextRow = row2.nextElementSibling;
      if (nextRow) {
        if (getAttribute(nextRow, "data-p-selectable-row") === true) return nextRow;
        else return this.findNextSelectableRow(nextRow);
      } else {
        return null;
      }
    }, "findNextSelectableRow"),
    findPrevSelectableRow: /* @__PURE__ */ __name(function findPrevSelectableRow(row2) {
      var prevRow = row2.previousElementSibling;
      if (prevRow) {
        if (getAttribute(prevRow, "data-p-selectable-row") === true) return prevRow;
        else return this.findPrevSelectableRow(prevRow);
      } else {
        return null;
      }
    }, "findPrevSelectableRow"),
    findFirstSelectableRow: /* @__PURE__ */ __name(function findFirstSelectableRow() {
      var firstRow = findSingle(this.$refs.table, 'tr[data-p-selectable-row="true"]');
      return firstRow;
    }, "findFirstSelectableRow"),
    findLastSelectableRow: /* @__PURE__ */ __name(function findLastSelectableRow() {
      var rows3 = find(this.$refs.table, 'tr[data-p-selectable-row="true"]');
      return rows3 ? rows3[rows3.length - 1] : null;
    }, "findLastSelectableRow"),
    focusRowChange: /* @__PURE__ */ __name(function focusRowChange(firstFocusableRow, currentFocusedRow) {
      firstFocusableRow.tabIndex = "-1";
      currentFocusedRow.tabIndex = "0";
      focus(currentFocusedRow);
    }, "focusRowChange"),
    toggleRowWithRadio: /* @__PURE__ */ __name(function toggleRowWithRadio2(event2) {
      var rowData = event2.data;
      if (this.isSelected(rowData)) {
        this.$emit("update:selection", null);
        this.$emit("row-unselect", {
          originalEvent: event2.originalEvent,
          data: rowData,
          index: event2.index,
          type: "radiobutton"
        });
      } else {
        this.$emit("update:selection", rowData);
        this.$emit("row-select", {
          originalEvent: event2.originalEvent,
          data: rowData,
          index: event2.index,
          type: "radiobutton"
        });
      }
    }, "toggleRowWithRadio"),
    toggleRowWithCheckbox: /* @__PURE__ */ __name(function toggleRowWithCheckbox2(event2) {
      var rowData = event2.data;
      if (this.isSelected(rowData)) {
        var selectionIndex = this.findIndexInSelection(rowData);
        var _selection = this.selection.filter(function(val, i) {
          return i != selectionIndex;
        });
        this.$emit("update:selection", _selection);
        this.$emit("row-unselect", {
          originalEvent: event2.originalEvent,
          data: rowData,
          index: event2.index,
          type: "checkbox"
        });
      } else {
        var _selection5 = this.selection ? _toConsumableArray(this.selection) : [];
        _selection5 = [].concat(_toConsumableArray(_selection5), [rowData]);
        this.$emit("update:selection", _selection5);
        this.$emit("row-select", {
          originalEvent: event2.originalEvent,
          data: rowData,
          index: event2.index,
          type: "checkbox"
        });
      }
    }, "toggleRowWithCheckbox"),
    toggleRowsWithCheckbox: /* @__PURE__ */ __name(function toggleRowsWithCheckbox(event2) {
      if (this.selectAll !== null) {
        this.$emit("select-all-change", event2);
      } else {
        var originalEvent = event2.originalEvent, checked2 = event2.checked;
        var _selection = [];
        if (checked2) {
          _selection = this.frozenValue ? [].concat(_toConsumableArray(this.frozenValue), _toConsumableArray(this.processedData)) : this.processedData;
          this.$emit("row-select-all", {
            originalEvent,
            data: _selection
          });
        } else {
          this.$emit("row-unselect-all", {
            originalEvent
          });
        }
        this.$emit("update:selection", _selection);
      }
    }, "toggleRowsWithCheckbox"),
    isSingleSelectionMode: /* @__PURE__ */ __name(function isSingleSelectionMode() {
      return this.selectionMode === "single";
    }, "isSingleSelectionMode"),
    isMultipleSelectionMode: /* @__PURE__ */ __name(function isMultipleSelectionMode() {
      return this.selectionMode === "multiple";
    }, "isMultipleSelectionMode"),
    isSelected: /* @__PURE__ */ __name(function isSelected2(rowData) {
      if (rowData && this.selection) {
        if (this.dataKey) {
          return this.d_selectionKeys ? this.d_selectionKeys[resolveFieldData(rowData, this.dataKey)] !== void 0 : false;
        } else {
          if (this.selection instanceof Array) return this.findIndexInSelection(rowData) > -1;
          else return this.equals(rowData, this.selection);
        }
      }
      return false;
    }, "isSelected"),
    findIndexInSelection: /* @__PURE__ */ __name(function findIndexInSelection2(rowData) {
      return this.findIndex(rowData, this.selection);
    }, "findIndexInSelection"),
    findIndex: /* @__PURE__ */ __name(function findIndex2(rowData, collection) {
      var index = -1;
      if (collection && collection.length) {
        for (var i = 0; i < collection.length; i++) {
          if (this.equals(rowData, collection[i])) {
            index = i;
            break;
          }
        }
      }
      return index;
    }, "findIndex"),
    updateSelectionKeys: /* @__PURE__ */ __name(function updateSelectionKeys(selection) {
      this.d_selectionKeys = {};
      if (Array.isArray(selection)) {
        var _iterator3 = _createForOfIteratorHelper(selection), _step3;
        try {
          for (_iterator3.s(); !(_step3 = _iterator3.n()).done; ) {
            var data12 = _step3.value;
            this.d_selectionKeys[String(resolveFieldData(data12, this.dataKey))] = 1;
          }
        } catch (err) {
          _iterator3.e(err);
        } finally {
          _iterator3.f();
        }
      } else {
        this.d_selectionKeys[String(resolveFieldData(selection, this.dataKey))] = 1;
      }
    }, "updateSelectionKeys"),
    updateEditingRowKeys: /* @__PURE__ */ __name(function updateEditingRowKeys(editingRows) {
      if (editingRows && editingRows.length) {
        this.d_editingRowKeys = {};
        var _iterator4 = _createForOfIteratorHelper(editingRows), _step4;
        try {
          for (_iterator4.s(); !(_step4 = _iterator4.n()).done; ) {
            var data12 = _step4.value;
            this.d_editingRowKeys[String(resolveFieldData(data12, this.dataKey))] = 1;
          }
        } catch (err) {
          _iterator4.e(err);
        } finally {
          _iterator4.f();
        }
      } else {
        this.d_editingRowKeys = null;
      }
    }, "updateEditingRowKeys"),
    equals: /* @__PURE__ */ __name(function equals$12(data1, data22) {
      return this.compareSelectionBy === "equals" ? data1 === data22 : equals(data1, data22, this.dataKey);
    }, "equals$1"),
    selectRange: /* @__PURE__ */ __name(function selectRange(event2) {
      var rangeStart, rangeEnd;
      if (this.rangeRowIndex > this.anchorRowIndex) {
        rangeStart = this.anchorRowIndex;
        rangeEnd = this.rangeRowIndex;
      } else if (this.rangeRowIndex < this.anchorRowIndex) {
        rangeStart = this.rangeRowIndex;
        rangeEnd = this.anchorRowIndex;
      } else {
        rangeStart = this.rangeRowIndex;
        rangeEnd = this.rangeRowIndex;
      }
      if (this.lazy && this.paginator) {
        rangeStart -= this.first;
        rangeEnd -= this.first;
      }
      var value = this.processedData;
      var _selection = [];
      for (var i = rangeStart; i <= rangeEnd; i++) {
        var rangeRowData = value[i];
        _selection.push(rangeRowData);
        this.$emit("row-select", {
          originalEvent: event2,
          data: rangeRowData,
          type: "row"
        });
      }
      this.$emit("update:selection", _selection);
    }, "selectRange"),
    exportCSV: /* @__PURE__ */ __name(function exportCSV$1(options, data12) {
      var _this6 = this;
      var csv = "\uFEFF";
      if (!data12) {
        data12 = this.processedData;
        if (options && options.selectionOnly) data12 = this.selection || [];
        else if (this.frozenValue) data12 = data12 ? [].concat(_toConsumableArray(this.frozenValue), _toConsumableArray(data12)) : this.frozenValue;
      }
      var headerInitiated = false;
      for (var i = 0; i < this.columns.length; i++) {
        var column = this.columns[i];
        if (this.columnProp(column, "exportable") !== false && this.columnProp(column, "field")) {
          if (headerInitiated) csv += this.csvSeparator;
          else headerInitiated = true;
          csv += '"' + (this.columnProp(column, "exportHeader") || this.columnProp(column, "header") || this.columnProp(column, "field")) + '"';
        }
      }
      if (data12) {
        data12.forEach(function(record) {
          csv += "\n";
          var rowInitiated = false;
          for (var _i = 0; _i < _this6.columns.length; _i++) {
            var _column = _this6.columns[_i];
            if (_this6.columnProp(_column, "exportable") !== false && _this6.columnProp(_column, "field")) {
              if (rowInitiated) csv += _this6.csvSeparator;
              else rowInitiated = true;
              var cellData = resolveFieldData(record, _this6.columnProp(_column, "field"));
              if (cellData != null) {
                if (_this6.exportFunction) {
                  cellData = _this6.exportFunction({
                    data: cellData,
                    field: _this6.columnProp(_column, "field")
                  });
                } else cellData = String(cellData).replace(/"/g, '""');
              } else cellData = "";
              csv += '"' + cellData + '"';
            }
          }
        });
      }
      var footerInitiated = false;
      for (var _i2 = 0; _i2 < this.columns.length; _i2++) {
        var _column2 = this.columns[_i2];
        if (_i2 === 0) csv += "\n";
        if (this.columnProp(_column2, "exportable") !== false && this.columnProp(_column2, "exportFooter")) {
          if (footerInitiated) csv += this.csvSeparator;
          else footerInitiated = true;
          csv += '"' + (this.columnProp(_column2, "exportFooter") || this.columnProp(_column2, "footer") || this.columnProp(_column2, "field")) + '"';
        }
      }
      exportCSV(csv, this.exportFilename);
    }, "exportCSV$1"),
    resetPage: /* @__PURE__ */ __name(function resetPage() {
      this.d_first = 0;
      this.$emit("update:first", this.d_first);
    }, "resetPage"),
    onColumnResizeStart: /* @__PURE__ */ __name(function onColumnResizeStart(event2) {
      var containerLeft = getOffset(this.$el).left;
      this.resizeColumnElement = event2.target.parentElement;
      this.columnResizing = true;
      this.lastResizeHelperX = event2.pageX - containerLeft + this.$el.scrollLeft;
      this.bindColumnResizeEvents();
    }, "onColumnResizeStart"),
    onColumnResize: /* @__PURE__ */ __name(function onColumnResize(event2) {
      var containerLeft = getOffset(this.$el).left;
      this.$el.setAttribute("data-p-unselectable-text", "true");
      !this.isUnstyled && addStyle(this.$el, {
        "user-select": "none"
      });
      this.$refs.resizeHelper.style.height = this.$el.offsetHeight + "px";
      this.$refs.resizeHelper.style.top = "0px";
      this.$refs.resizeHelper.style.left = event2.pageX - containerLeft + this.$el.scrollLeft + "px";
      this.$refs.resizeHelper.style.display = "block";
    }, "onColumnResize"),
    onColumnResizeEnd: /* @__PURE__ */ __name(function onColumnResizeEnd() {
      var delta = this.$refs.resizeHelper.offsetLeft - this.lastResizeHelperX;
      var columnWidth = this.resizeColumnElement.offsetWidth;
      var newColumnWidth = columnWidth + delta;
      var minWidth = this.resizeColumnElement.style.minWidth || 15;
      if (columnWidth + delta > parseInt(minWidth, 10)) {
        if (this.columnResizeMode === "fit") {
          var nextColumn = this.resizeColumnElement.nextElementSibling;
          var nextColumnWidth = nextColumn.offsetWidth - delta;
          if (newColumnWidth > 15 && nextColumnWidth > 15) {
            this.resizeTableCells(newColumnWidth, nextColumnWidth);
          }
        } else if (this.columnResizeMode === "expand") {
          var tableWidth = this.$refs.table.offsetWidth + delta + "px";
          var updateTableWidth = /* @__PURE__ */ __name(function updateTableWidth2(el) {
            !!el && (el.style.width = el.style.minWidth = tableWidth);
          }, "updateTableWidth");
          this.resizeTableCells(newColumnWidth);
          updateTableWidth(this.$refs.table);
          if (!this.virtualScrollerDisabled) {
            var body = this.$refs.bodyRef && this.$refs.bodyRef.$el;
            var frozenBody = this.$refs.frozenBodyRef && this.$refs.frozenBodyRef.$el;
            updateTableWidth(body);
            updateTableWidth(frozenBody);
          }
        }
        this.$emit("column-resize-end", {
          element: this.resizeColumnElement,
          delta
        });
      }
      this.$refs.resizeHelper.style.display = "none";
      this.resizeColumn = null;
      this.$el.removeAttribute("data-p-unselectable-text");
      !this.isUnstyled && (this.$el.style["user-select"] = "");
      this.unbindColumnResizeEvents();
      if (this.isStateful()) {
        this.saveState();
      }
    }, "onColumnResizeEnd"),
    resizeTableCells: /* @__PURE__ */ __name(function resizeTableCells(newColumnWidth, nextColumnWidth) {
      var colIndex = getIndex(this.resizeColumnElement);
      var widths = [];
      var headers = find(this.$refs.table, 'thead[data-pc-section="thead"] > tr > th');
      headers.forEach(function(header) {
        return widths.push(getOuterWidth(header));
      });
      this.destroyStyleElement();
      this.createStyleElement();
      var innerHTML = "";
      var selector = '[data-pc-name="datatable"]['.concat(this.attributeSelector, '] > [data-pc-section="tablecontainer"] ').concat(this.virtualScrollerDisabled ? "" : '> [data-pc-name="virtualscroller"]', ' > table[data-pc-section="table"]');
      widths.forEach(function(width, index) {
        var colWidth = index === colIndex ? newColumnWidth : nextColumnWidth && index === colIndex + 1 ? nextColumnWidth : width;
        var style = "width: ".concat(colWidth, "px !important; max-width: ").concat(colWidth, "px !important");
        innerHTML += "\n                    ".concat(selector, ' > thead[data-pc-section="thead"] > tr > th:nth-child(').concat(index + 1, "),\n                    ").concat(selector, ' > tbody[data-pc-section="tbody"] > tr > td:nth-child(').concat(index + 1, "),\n                    ").concat(selector, ' > tfoot[data-pc-section="tfoot"] > tr > td:nth-child(').concat(index + 1, ") {\n                        ").concat(style, "\n                    }\n                ");
      });
      this.styleElement.innerHTML = innerHTML;
    }, "resizeTableCells"),
    bindColumnResizeEvents: /* @__PURE__ */ __name(function bindColumnResizeEvents() {
      var _this7 = this;
      if (!this.documentColumnResizeListener) {
        this.documentColumnResizeListener = document.addEventListener("mousemove", function() {
          if (_this7.columnResizing) {
            _this7.onColumnResize(event);
          }
        });
      }
      if (!this.documentColumnResizeEndListener) {
        this.documentColumnResizeEndListener = document.addEventListener("mouseup", function() {
          if (_this7.columnResizing) {
            _this7.columnResizing = false;
            _this7.onColumnResizeEnd();
          }
        });
      }
    }, "bindColumnResizeEvents"),
    unbindColumnResizeEvents: /* @__PURE__ */ __name(function unbindColumnResizeEvents() {
      if (this.documentColumnResizeListener) {
        document.removeEventListener("document", this.documentColumnResizeListener);
        this.documentColumnResizeListener = null;
      }
      if (this.documentColumnResizeEndListener) {
        document.removeEventListener("document", this.documentColumnResizeEndListener);
        this.documentColumnResizeEndListener = null;
      }
    }, "unbindColumnResizeEvents"),
    onColumnHeaderMouseDown: /* @__PURE__ */ __name(function onColumnHeaderMouseDown(e) {
      var event2 = e.originalEvent;
      var column = e.column;
      if (this.reorderableColumns && this.columnProp(column, "reorderableColumn") !== false) {
        if (event2.target.nodeName === "INPUT" || event2.target.nodeName === "TEXTAREA" || getAttribute(event2.target, '[data-pc-section="columnresizer"]')) event2.currentTarget.draggable = false;
        else event2.currentTarget.draggable = true;
      }
    }, "onColumnHeaderMouseDown"),
    onColumnHeaderDragStart: /* @__PURE__ */ __name(function onColumnHeaderDragStart(e) {
      var event2 = e.originalEvent, column = e.column;
      if (this.columnResizing) {
        event2.preventDefault();
        return;
      }
      this.colReorderIconWidth = getHiddenElementOuterWidth(this.$refs.reorderIndicatorUp);
      this.colReorderIconHeight = getHiddenElementOuterHeight(this.$refs.reorderIndicatorUp);
      this.draggedColumn = column;
      this.draggedColumnElement = this.findParentHeader(event2.target);
      event2.dataTransfer.setData("text", "b");
    }, "onColumnHeaderDragStart"),
    onColumnHeaderDragOver: /* @__PURE__ */ __name(function onColumnHeaderDragOver(e) {
      var event2 = e.originalEvent, column = e.column;
      var dropHeader = this.findParentHeader(event2.target);
      if (this.reorderableColumns && this.draggedColumnElement && dropHeader && !this.columnProp(column, "frozen")) {
        event2.preventDefault();
        var containerOffset = getOffset(this.$el);
        var dropHeaderOffset = getOffset(dropHeader);
        if (this.draggedColumnElement !== dropHeader) {
          var targetLeft = dropHeaderOffset.left - containerOffset.left;
          var columnCenter = dropHeaderOffset.left + dropHeader.offsetWidth / 2;
          this.$refs.reorderIndicatorUp.style.top = dropHeaderOffset.top - containerOffset.top - (this.colReorderIconHeight - 1) + "px";
          this.$refs.reorderIndicatorDown.style.top = dropHeaderOffset.top - containerOffset.top + dropHeader.offsetHeight + "px";
          if (event2.pageX > columnCenter) {
            this.$refs.reorderIndicatorUp.style.left = targetLeft + dropHeader.offsetWidth - Math.ceil(this.colReorderIconWidth / 2) + "px";
            this.$refs.reorderIndicatorDown.style.left = targetLeft + dropHeader.offsetWidth - Math.ceil(this.colReorderIconWidth / 2) + "px";
            this.dropPosition = 1;
          } else {
            this.$refs.reorderIndicatorUp.style.left = targetLeft - Math.ceil(this.colReorderIconWidth / 2) + "px";
            this.$refs.reorderIndicatorDown.style.left = targetLeft - Math.ceil(this.colReorderIconWidth / 2) + "px";
            this.dropPosition = -1;
          }
          this.$refs.reorderIndicatorUp.style.display = "block";
          this.$refs.reorderIndicatorDown.style.display = "block";
        }
      }
    }, "onColumnHeaderDragOver"),
    onColumnHeaderDragLeave: /* @__PURE__ */ __name(function onColumnHeaderDragLeave(e) {
      var event2 = e.originalEvent;
      if (this.reorderableColumns && this.draggedColumnElement) {
        event2.preventDefault();
        this.$refs.reorderIndicatorUp.style.display = "none";
        this.$refs.reorderIndicatorDown.style.display = "none";
      }
    }, "onColumnHeaderDragLeave"),
    onColumnHeaderDrop: /* @__PURE__ */ __name(function onColumnHeaderDrop(e) {
      var _this8 = this;
      var event2 = e.originalEvent, column = e.column;
      event2.preventDefault();
      if (this.draggedColumnElement) {
        var dragIndex = getIndex(this.draggedColumnElement);
        var dropIndex = getIndex(this.findParentHeader(event2.target));
        var allowDrop = dragIndex !== dropIndex;
        if (allowDrop && (dropIndex - dragIndex === 1 && this.dropPosition === -1 || dropIndex - dragIndex === -1 && this.dropPosition === 1)) {
          allowDrop = false;
        }
        if (allowDrop) {
          var isSameColumn = /* @__PURE__ */ __name(function isSameColumn2(col1, col2) {
            return _this8.columnProp(col1, "columnKey") || _this8.columnProp(col2, "columnKey") ? _this8.columnProp(col1, "columnKey") === _this8.columnProp(col2, "columnKey") : _this8.columnProp(col1, "field") === _this8.columnProp(col2, "field");
          }, "isSameColumn");
          var dragColIndex = this.columns.findIndex(function(child) {
            return isSameColumn(child, _this8.draggedColumn);
          });
          var dropColIndex = this.columns.findIndex(function(child) {
            return isSameColumn(child, column);
          });
          var widths = [];
          var headers = find(this.$el, 'thead[data-pc-section="thead"] > tr > th');
          headers.forEach(function(header) {
            return widths.push(getOuterWidth(header));
          });
          var movedItem = widths.find(function(_, index) {
            return index === dragColIndex;
          });
          var remainingItems = widths.filter(function(_, index) {
            return index !== dragColIndex;
          });
          var reorderedWidths = [].concat(_toConsumableArray(remainingItems.slice(0, dropColIndex)), [movedItem], _toConsumableArray(remainingItems.slice(dropColIndex)));
          this.addColumnWidthStyles(reorderedWidths);
          if (dropColIndex < dragColIndex && this.dropPosition === 1) {
            dropColIndex++;
          }
          if (dropColIndex > dragColIndex && this.dropPosition === -1) {
            dropColIndex--;
          }
          reorderArray(this.columns, dragColIndex, dropColIndex);
          this.updateReorderableColumns();
          this.$emit("column-reorder", {
            originalEvent: event2,
            dragIndex: dragColIndex,
            dropIndex: dropColIndex
          });
        }
        this.$refs.reorderIndicatorUp.style.display = "none";
        this.$refs.reorderIndicatorDown.style.display = "none";
        this.draggedColumnElement.draggable = false;
        this.draggedColumnElement = null;
        this.draggedColumn = null;
        this.dropPosition = null;
      }
    }, "onColumnHeaderDrop"),
    findParentHeader: /* @__PURE__ */ __name(function findParentHeader(element) {
      if (element.nodeName === "TH") {
        return element;
      } else {
        var parent = element.parentElement;
        while (parent.nodeName !== "TH") {
          parent = parent.parentElement;
          if (!parent) break;
        }
        return parent;
      }
    }, "findParentHeader"),
    findColumnByKey: /* @__PURE__ */ __name(function findColumnByKey(columns2, key) {
      if (columns2 && columns2.length) {
        for (var i = 0; i < columns2.length; i++) {
          var column = columns2[i];
          if (this.columnProp(column, "columnKey") === key || this.columnProp(column, "field") === key) {
            return column;
          }
        }
      }
      return null;
    }, "findColumnByKey"),
    onRowMouseDown: /* @__PURE__ */ __name(function onRowMouseDown2(event2) {
      if (getAttribute(event2.target, "data-pc-section") === "reorderablerowhandle" || getAttribute(event2.target.parentElement, "data-pc-section") === "reorderablerowhandle") event2.currentTarget.draggable = true;
      else event2.currentTarget.draggable = false;
    }, "onRowMouseDown"),
    onRowDragStart: /* @__PURE__ */ __name(function onRowDragStart2(e) {
      var event2 = e.originalEvent;
      var index = e.index;
      this.rowDragging = true;
      this.draggedRowIndex = index;
      event2.dataTransfer.setData("text", "b");
    }, "onRowDragStart"),
    onRowDragOver: /* @__PURE__ */ __name(function onRowDragOver2(e) {
      var event2 = e.originalEvent;
      var index = e.index;
      if (this.rowDragging && this.draggedRowIndex !== index) {
        var rowElement = event2.currentTarget;
        var rowY = getOffset(rowElement).top + getWindowScrollTop();
        var pageY = event2.pageY;
        var rowMidY = rowY + getOuterHeight(rowElement) / 2;
        var prevRowElement = rowElement.previousElementSibling;
        if (pageY < rowMidY) {
          rowElement.setAttribute("data-p-datatable-dragpoint-bottom", "false");
          !this.isUnstyled && removeClass(rowElement, "p-datatable-dragpoint-bottom");
          this.droppedRowIndex = index;
          if (prevRowElement) {
            prevRowElement.setAttribute("data-p-datatable-dragpoint-bottom", "true");
            !this.isUnstyled && addClass(prevRowElement, "p-datatable-dragpoint-bottom");
          } else {
            rowElement.setAttribute("data-p-datatable-dragpoint-top", "true");
            !this.isUnstyled && addClass(rowElement, "p-datatable-dragpoint-top");
          }
        } else {
          if (prevRowElement) {
            prevRowElement.setAttribute("data-p-datatable-dragpoint-bottom", "false");
            !this.isUnstyled && removeClass(prevRowElement, "p-datatable-dragpoint-bottom");
          } else {
            rowElement.setAttribute("data-p-datatable-dragpoint-top", "true");
            !this.isUnstyled && addClass(rowElement, "p-datatable-dragpoint-top");
          }
          this.droppedRowIndex = index + 1;
          rowElement.setAttribute("data-p-datatable-dragpoint-bottom", "true");
          !this.isUnstyled && addClass(rowElement, "p-datatable-dragpoint-bottom");
        }
        event2.preventDefault();
      }
    }, "onRowDragOver"),
    onRowDragLeave: /* @__PURE__ */ __name(function onRowDragLeave2(event2) {
      var rowElement = event2.currentTarget;
      var prevRowElement = rowElement.previousElementSibling;
      if (prevRowElement) {
        prevRowElement.setAttribute("data-p-datatable-dragpoint-bottom", "false");
        !this.isUnstyled && removeClass(prevRowElement, "p-datatable-dragpoint-bottom");
      }
      rowElement.setAttribute("data-p-datatable-dragpoint-bottom", "false");
      !this.isUnstyled && removeClass(rowElement, "p-datatable-dragpoint-bottom");
      rowElement.setAttribute("data-p-datatable-dragpoint-top", "false");
      !this.isUnstyled && removeClass(rowElement, "p-datatable-dragpoint-top");
    }, "onRowDragLeave"),
    onRowDragEnd: /* @__PURE__ */ __name(function onRowDragEnd2(event2) {
      this.rowDragging = false;
      this.draggedRowIndex = null;
      this.droppedRowIndex = null;
      event2.currentTarget.draggable = false;
    }, "onRowDragEnd"),
    onRowDrop: /* @__PURE__ */ __name(function onRowDrop2(event2) {
      if (this.droppedRowIndex != null) {
        var dropIndex = this.draggedRowIndex > this.droppedRowIndex ? this.droppedRowIndex : this.droppedRowIndex === 0 ? 0 : this.droppedRowIndex - 1;
        var processedData2 = _toConsumableArray(this.processedData);
        reorderArray(processedData2, this.draggedRowIndex + this.d_first, dropIndex + this.d_first);
        this.$emit("row-reorder", {
          originalEvent: event2,
          dragIndex: this.draggedRowIndex,
          dropIndex,
          value: processedData2
        });
      }
      this.onRowDragLeave(event2);
      this.onRowDragEnd(event2);
      event2.preventDefault();
    }, "onRowDrop"),
    toggleRow: /* @__PURE__ */ __name(function toggleRow2(event2) {
      var _this9 = this;
      var expanded = event2.expanded, rest = _objectWithoutProperties(event2, _excluded);
      var rowData = event2.data;
      var expandedRows;
      if (this.dataKey) {
        var value = resolveFieldData(rowData, this.dataKey);
        expandedRows = this.expandedRows ? _objectSpread$1({}, this.expandedRows) : {};
        expanded ? expandedRows[value] = true : delete expandedRows[value];
      } else {
        expandedRows = this.expandedRows ? _toConsumableArray(this.expandedRows) : [];
        expanded ? expandedRows.push(rowData) : expandedRows = expandedRows.filter(function(d) {
          return !_this9.equals(rowData, d);
        });
      }
      this.$emit("update:expandedRows", expandedRows);
      expanded ? this.$emit("row-expand", rest) : this.$emit("row-collapse", rest);
    }, "toggleRow"),
    toggleRowGroup: /* @__PURE__ */ __name(function toggleRowGroup(e) {
      var event2 = e.originalEvent;
      var data12 = e.data;
      var groupFieldValue = resolveFieldData(data12, this.groupRowsBy);
      var _expandedRowGroups = this.expandedRowGroups ? _toConsumableArray(this.expandedRowGroups) : [];
      if (this.isRowGroupExpanded(data12)) {
        _expandedRowGroups = _expandedRowGroups.filter(function(group) {
          return group !== groupFieldValue;
        });
        this.$emit("update:expandedRowGroups", _expandedRowGroups);
        this.$emit("rowgroup-collapse", {
          originalEvent: event2,
          data: groupFieldValue
        });
      } else {
        _expandedRowGroups.push(groupFieldValue);
        this.$emit("update:expandedRowGroups", _expandedRowGroups);
        this.$emit("rowgroup-expand", {
          originalEvent: event2,
          data: groupFieldValue
        });
      }
    }, "toggleRowGroup"),
    isRowGroupExpanded: /* @__PURE__ */ __name(function isRowGroupExpanded2(rowData) {
      if (this.expandableRowGroups && this.expandedRowGroups) {
        var groupFieldValue = resolveFieldData(rowData, this.groupRowsBy);
        return this.expandedRowGroups.indexOf(groupFieldValue) > -1;
      }
      return false;
    }, "isRowGroupExpanded"),
    isStateful: /* @__PURE__ */ __name(function isStateful() {
      return this.stateKey != null;
    }, "isStateful"),
    getStorage: /* @__PURE__ */ __name(function getStorage() {
      switch (this.stateStorage) {
        case "local":
          return window.localStorage;
        case "session":
          return window.sessionStorage;
        default:
          throw new Error(this.stateStorage + ' is not a valid value for the state storage, supported values are "local" and "session".');
      }
    }, "getStorage"),
    saveState: /* @__PURE__ */ __name(function saveState() {
      var storage = this.getStorage();
      var state = {};
      if (this.paginator) {
        state.first = this.d_first;
        state.rows = this.d_rows;
      }
      if (this.d_sortField) {
        state.sortField = this.d_sortField;
        state.sortOrder = this.d_sortOrder;
      }
      if (this.d_multiSortMeta) {
        state.multiSortMeta = this.d_multiSortMeta;
      }
      if (this.hasFilters) {
        state.filters = this.filters;
      }
      if (this.resizableColumns) {
        this.saveColumnWidths(state);
      }
      if (this.reorderableColumns) {
        state.columnOrder = this.d_columnOrder;
      }
      if (this.expandedRows) {
        state.expandedRows = this.expandedRows;
      }
      if (this.expandedRowGroups) {
        state.expandedRowGroups = this.expandedRowGroups;
      }
      if (this.selection) {
        state.selection = this.selection;
        state.selectionKeys = this.d_selectionKeys;
      }
      if (Object.keys(state).length) {
        storage.setItem(this.stateKey, JSON.stringify(state));
      }
      this.$emit("state-save", state);
    }, "saveState"),
    restoreState: /* @__PURE__ */ __name(function restoreState() {
      var storage = this.getStorage();
      var stateString = storage.getItem(this.stateKey);
      var dateFormat = /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z/;
      var reviver = /* @__PURE__ */ __name(function reviver2(key, value) {
        if (typeof value === "string" && dateFormat.test(value)) {
          return new Date(value);
        }
        return value;
      }, "reviver");
      if (stateString) {
        var restoredState = JSON.parse(stateString, reviver);
        if (this.paginator) {
          this.d_first = restoredState.first;
          this.d_rows = restoredState.rows;
        }
        if (restoredState.sortField) {
          this.d_sortField = restoredState.sortField;
          this.d_sortOrder = restoredState.sortOrder;
        }
        if (restoredState.multiSortMeta) {
          this.d_multiSortMeta = restoredState.multiSortMeta;
        }
        if (restoredState.filters) {
          this.$emit("update:filters", restoredState.filters);
        }
        if (this.resizableColumns) {
          this.columnWidthsState = restoredState.columnWidths;
          this.tableWidthState = restoredState.tableWidth;
        }
        if (this.reorderableColumns) {
          this.d_columnOrder = restoredState.columnOrder;
        }
        if (restoredState.expandedRows) {
          this.$emit("update:expandedRows", restoredState.expandedRows);
        }
        if (restoredState.expandedRowGroups) {
          this.$emit("update:expandedRowGroups", restoredState.expandedRowGroups);
        }
        if (restoredState.selection) {
          this.d_selectionKeys = restoredState.d_selectionKeys;
          this.$emit("update:selection", restoredState.selection);
        }
        this.$emit("state-restore", restoredState);
      }
    }, "restoreState"),
    saveColumnWidths: /* @__PURE__ */ __name(function saveColumnWidths(state) {
      var widths = [];
      var headers = find(this.$el, 'thead[data-pc-section="thead"] > tr > th');
      headers.forEach(function(header) {
        return widths.push(getOuterWidth(header));
      });
      state.columnWidths = widths.join(",");
      if (this.columnResizeMode === "expand") {
        state.tableWidth = getOuterWidth(this.$refs.table) + "px";
      }
    }, "saveColumnWidths"),
    addColumnWidthStyles: /* @__PURE__ */ __name(function addColumnWidthStyles(widths) {
      this.createStyleElement();
      var innerHTML = "";
      var selector = '[data-pc-name="datatable"]['.concat(this.attributeSelector, '] > [data-pc-section="tablecontainer"] ').concat(this.virtualScrollerDisabled ? "" : '> [data-pc-name="virtualscroller"]', ' > table[data-pc-section="table"]');
      widths.forEach(function(width, index) {
        var style = "width: ".concat(width, "px !important; max-width: ").concat(width, "px !important");
        innerHTML += "\n        ".concat(selector, ' > thead[data-pc-section="thead"] > tr > th:nth-child(').concat(index + 1, "),\n        ").concat(selector, ' > tbody[data-pc-section="tbody"] > tr > td:nth-child(').concat(index + 1, "),\n        ").concat(selector, ' > tfoot[data-pc-section="tfoot"] > tr > td:nth-child(').concat(index + 1, ") {\n            ").concat(style, "\n        }\n    ");
      });
      this.styleElement.innerHTML = innerHTML;
    }, "addColumnWidthStyles"),
    restoreColumnWidths: /* @__PURE__ */ __name(function restoreColumnWidths() {
      if (this.columnWidthsState) {
        var widths = this.columnWidthsState.split(",");
        if (this.columnResizeMode === "expand" && this.tableWidthState) {
          this.$refs.table.style.width = this.tableWidthState;
          this.$refs.table.style.minWidth = this.tableWidthState;
        }
        if (isNotEmpty(widths)) {
          this.addColumnWidthStyles(widths);
        }
      }
    }, "restoreColumnWidths"),
    onCellEditInit: /* @__PURE__ */ __name(function onCellEditInit2(event2) {
      this.$emit("cell-edit-init", event2);
    }, "onCellEditInit"),
    onCellEditComplete: /* @__PURE__ */ __name(function onCellEditComplete2(event2) {
      this.$emit("cell-edit-complete", event2);
    }, "onCellEditComplete"),
    onCellEditCancel: /* @__PURE__ */ __name(function onCellEditCancel2(event2) {
      this.$emit("cell-edit-cancel", event2);
    }, "onCellEditCancel"),
    onRowEditInit: /* @__PURE__ */ __name(function onRowEditInit3(event2) {
      var _editingRows = this.editingRows ? _toConsumableArray(this.editingRows) : [];
      _editingRows.push(event2.data);
      this.$emit("update:editingRows", _editingRows);
      this.$emit("row-edit-init", event2);
    }, "onRowEditInit"),
    onRowEditSave: /* @__PURE__ */ __name(function onRowEditSave3(event2) {
      var _editingRows = _toConsumableArray(this.editingRows);
      _editingRows.splice(this.findIndex(event2.data, _editingRows), 1);
      this.$emit("update:editingRows", _editingRows);
      this.$emit("row-edit-save", event2);
    }, "onRowEditSave"),
    onRowEditCancel: /* @__PURE__ */ __name(function onRowEditCancel3(event2) {
      var _editingRows = _toConsumableArray(this.editingRows);
      _editingRows.splice(this.findIndex(event2.data, _editingRows), 1);
      this.$emit("update:editingRows", _editingRows);
      this.$emit("row-edit-cancel", event2);
    }, "onRowEditCancel"),
    onEditingMetaChange: /* @__PURE__ */ __name(function onEditingMetaChange2(event2) {
      var data12 = event2.data, field2 = event2.field, index = event2.index, editing2 = event2.editing;
      var editingMeta = _objectSpread$1({}, this.d_editingMeta);
      var meta = editingMeta[index];
      if (editing2) {
        !meta && (meta = editingMeta[index] = {
          data: _objectSpread$1({}, data12),
          fields: []
        });
        meta["fields"].push(field2);
      } else if (meta) {
        var fields = meta["fields"].filter(function(f) {
          return f !== field2;
        });
        !fields.length ? delete editingMeta[index] : meta["fields"] = fields;
      }
      this.d_editingMeta = editingMeta;
    }, "onEditingMetaChange"),
    clearEditingMetaData: /* @__PURE__ */ __name(function clearEditingMetaData() {
      if (this.editMode) {
        this.d_editingMeta = {};
      }
    }, "clearEditingMetaData"),
    createLazyLoadEvent: /* @__PURE__ */ __name(function createLazyLoadEvent(event2) {
      return {
        originalEvent: event2,
        first: this.d_first,
        rows: this.d_rows,
        sortField: this.d_sortField,
        sortOrder: this.d_sortOrder,
        multiSortMeta: this.d_multiSortMeta,
        filters: this.d_filters
      };
    }, "createLazyLoadEvent"),
    hasGlobalFilter: /* @__PURE__ */ __name(function hasGlobalFilter() {
      return this.filters && Object.prototype.hasOwnProperty.call(this.filters, "global");
    }, "hasGlobalFilter"),
    onFilterChange: /* @__PURE__ */ __name(function onFilterChange(filters) {
      this.d_filters = filters;
    }, "onFilterChange"),
    onFilterApply: /* @__PURE__ */ __name(function onFilterApply() {
      this.d_first = 0;
      this.$emit("update:first", this.d_first);
      this.$emit("update:filters", this.d_filters);
      if (this.lazy) {
        this.$emit("filter", this.createLazyLoadEvent());
      }
    }, "onFilterApply"),
    cloneFilters: /* @__PURE__ */ __name(function cloneFilters() {
      var cloned = {};
      if (this.filters) {
        Object.entries(this.filters).forEach(function(_ref3) {
          var _ref4 = _slicedToArray(_ref3, 2), prop = _ref4[0], value = _ref4[1];
          cloned[prop] = value.operator ? {
            operator: value.operator,
            constraints: value.constraints.map(function(constraint) {
              return _objectSpread$1({}, constraint);
            })
          } : _objectSpread$1({}, value);
        });
      }
      return cloned;
    }, "cloneFilters"),
    updateReorderableColumns: /* @__PURE__ */ __name(function updateReorderableColumns() {
      var _this10 = this;
      var columnOrder = [];
      this.columns.forEach(function(col) {
        return columnOrder.push(_this10.columnProp(col, "columnKey") || _this10.columnProp(col, "field"));
      });
      this.d_columnOrder = columnOrder;
    }, "updateReorderableColumns"),
    createStyleElement: /* @__PURE__ */ __name(function createStyleElement() {
      var _this$$primevue;
      this.styleElement = document.createElement("style");
      this.styleElement.type = "text/css";
      setAttribute(this.styleElement, "nonce", (_this$$primevue = this.$primevue) === null || _this$$primevue === void 0 || (_this$$primevue = _this$$primevue.config) === null || _this$$primevue === void 0 || (_this$$primevue = _this$$primevue.csp) === null || _this$$primevue === void 0 ? void 0 : _this$$primevue.nonce);
      document.head.appendChild(this.styleElement);
    }, "createStyleElement"),
    destroyStyleElement: /* @__PURE__ */ __name(function destroyStyleElement() {
      if (this.styleElement) {
        document.head.removeChild(this.styleElement);
        this.styleElement = null;
      }
    }, "destroyStyleElement"),
    dataToRender: /* @__PURE__ */ __name(function dataToRender(data12) {
      var _data = data12 || this.processedData;
      if (_data && this.paginator) {
        var first4 = this.lazy ? 0 : this.d_first;
        return _data.slice(first4, first4 + this.d_rows);
      }
      return _data;
    }, "dataToRender"),
    getVirtualScrollerRef: /* @__PURE__ */ __name(function getVirtualScrollerRef() {
      return this.$refs.virtualScroller;
    }, "getVirtualScrollerRef"),
    hasSpacerStyle: /* @__PURE__ */ __name(function hasSpacerStyle(style) {
      return isNotEmpty(style);
    }, "hasSpacerStyle")
  },
  computed: {
    columns: /* @__PURE__ */ __name(function columns() {
      var cols = this.d_columns.get(this);
      if (this.reorderableColumns && this.d_columnOrder) {
        var orderedColumns = [];
        var _iterator5 = _createForOfIteratorHelper(this.d_columnOrder), _step5;
        try {
          for (_iterator5.s(); !(_step5 = _iterator5.n()).done; ) {
            var columnKey = _step5.value;
            var column = this.findColumnByKey(cols, columnKey);
            if (column && !this.columnProp(column, "hidden")) {
              orderedColumns.push(column);
            }
          }
        } catch (err) {
          _iterator5.e(err);
        } finally {
          _iterator5.f();
        }
        return [].concat(orderedColumns, _toConsumableArray(cols.filter(function(item) {
          return orderedColumns.indexOf(item) < 0;
        })));
      }
      return cols;
    }, "columns"),
    columnGroups: /* @__PURE__ */ __name(function columnGroups() {
      return this.d_columnGroups.get(this);
    }, "columnGroups"),
    headerColumnGroup: /* @__PURE__ */ __name(function headerColumnGroup() {
      var _this$columnGroups, _this11 = this;
      return (_this$columnGroups = this.columnGroups) === null || _this$columnGroups === void 0 ? void 0 : _this$columnGroups.find(function(group) {
        return _this11.columnProp(group, "type") === "header";
      });
    }, "headerColumnGroup"),
    footerColumnGroup: /* @__PURE__ */ __name(function footerColumnGroup() {
      var _this$columnGroups2, _this12 = this;
      return (_this$columnGroups2 = this.columnGroups) === null || _this$columnGroups2 === void 0 ? void 0 : _this$columnGroups2.find(function(group) {
        return _this12.columnProp(group, "type") === "footer";
      });
    }, "footerColumnGroup"),
    hasFilters: /* @__PURE__ */ __name(function hasFilters() {
      return this.filters && Object.keys(this.filters).length > 0 && this.filters.constructor === Object;
    }, "hasFilters"),
    processedData: /* @__PURE__ */ __name(function processedData() {
      var _this$virtualScroller;
      var data12 = this.value || [];
      if (!this.lazy && !((_this$virtualScroller = this.virtualScrollerOptions) !== null && _this$virtualScroller !== void 0 && _this$virtualScroller.lazy)) {
        if (data12 && data12.length) {
          if (this.hasFilters) {
            data12 = this.filter(data12);
          }
          if (this.sorted) {
            if (this.sortMode === "single") data12 = this.sortSingle(data12);
            else if (this.sortMode === "multiple") data12 = this.sortMultiple(data12);
          }
        }
      }
      return data12;
    }, "processedData"),
    totalRecordsLength: /* @__PURE__ */ __name(function totalRecordsLength() {
      if (this.lazy) {
        return this.totalRecords;
      } else {
        var data12 = this.processedData;
        return data12 ? data12.length : 0;
      }
    }, "totalRecordsLength"),
    empty: /* @__PURE__ */ __name(function empty2() {
      var data12 = this.processedData;
      return !data12 || data12.length === 0;
    }, "empty"),
    paginatorTop: /* @__PURE__ */ __name(function paginatorTop() {
      return this.paginator && (this.paginatorPosition !== "bottom" || this.paginatorPosition === "both");
    }, "paginatorTop"),
    paginatorBottom: /* @__PURE__ */ __name(function paginatorBottom() {
      return this.paginator && (this.paginatorPosition !== "top" || this.paginatorPosition === "both");
    }, "paginatorBottom"),
    sorted: /* @__PURE__ */ __name(function sorted() {
      return this.d_sortField || this.d_multiSortMeta && this.d_multiSortMeta.length > 0;
    }, "sorted"),
    allRowsSelected: /* @__PURE__ */ __name(function allRowsSelected() {
      var _this13 = this;
      if (this.selectAll !== null) {
        return this.selectAll;
      } else {
        var val = this.frozenValue ? [].concat(_toConsumableArray(this.frozenValue), _toConsumableArray(this.processedData)) : this.processedData;
        return isNotEmpty(val) && this.selection && Array.isArray(this.selection) && val.every(function(v) {
          return _this13.selection.some(function(s) {
            return _this13.equals(s, v);
          });
        });
      }
    }, "allRowsSelected"),
    attributeSelector: /* @__PURE__ */ __name(function attributeSelector2() {
      return UniqueComponentId();
    }, "attributeSelector"),
    groupRowSortField: /* @__PURE__ */ __name(function groupRowSortField() {
      return this.sortMode === "single" ? this.sortField : this.d_groupRowsSortMeta ? this.d_groupRowsSortMeta.field : null;
    }, "groupRowSortField"),
    headerFilterButtonProps: /* @__PURE__ */ __name(function headerFilterButtonProps() {
      return _objectSpread$1(_objectSpread$1({
        filter: {
          severity: "secondary",
          text: true,
          rounded: true
        }
      }, this.filterButtonProps), {}, {
        inline: _objectSpread$1({
          clear: {
            severity: "secondary",
            text: true,
            rounded: true
          }
        }, this.filterButtonProps.inline),
        popover: _objectSpread$1({
          addRule: {
            severity: "info",
            text: true,
            size: "small"
          },
          removeRule: {
            severity: "danger",
            text: true,
            size: "small"
          },
          apply: {
            size: "small"
          },
          clear: {
            outlined: true,
            size: "small"
          }
        }, this.filterButtonProps.popover)
      });
    }, "headerFilterButtonProps"),
    rowEditButtonProps: /* @__PURE__ */ __name(function rowEditButtonProps() {
      return _objectSpread$1(_objectSpread$1({}, {
        init: {
          severity: "secondary",
          text: true,
          rounded: true
        },
        save: {
          severity: "secondary",
          text: true,
          rounded: true
        },
        cancel: {
          severity: "secondary",
          text: true,
          rounded: true
        }
      }), this.editButtonProps);
    }, "rowEditButtonProps"),
    virtualScrollerDisabled: /* @__PURE__ */ __name(function virtualScrollerDisabled() {
      return isEmpty(this.virtualScrollerOptions) || !this.scrollable;
    }, "virtualScrollerDisabled")
  },
  components: {
    DTPaginator: script$l,
    DTTableHeader: script$1,
    DTTableBody: script$7,
    DTTableFooter: script$5,
    DTVirtualScroller: script$H,
    ArrowDownIcon: script$q,
    ArrowUpIcon: script$p,
    SpinnerIcon: script$I
  }
};
function _typeof(o) {
  "@babel/helpers - typeof";
  return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof(o);
}
__name(_typeof, "_typeof");
function ownKeys(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
__name(ownKeys, "ownKeys");
function _objectSpread(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys(Object(t), true).forEach(function(r2) {
      _defineProperty(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
__name(_objectSpread, "_objectSpread");
function _defineProperty(e, r, t) {
  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
__name(_defineProperty, "_defineProperty");
function _toPropertyKey(t) {
  var i = _toPrimitive(t, "string");
  return "symbol" == _typeof(i) ? i : i + "";
}
__name(_toPropertyKey, "_toPropertyKey");
function _toPrimitive(t, r) {
  if ("object" != _typeof(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
__name(_toPrimitive, "_toPrimitive");
function render2(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_SpinnerIcon = resolveComponent("SpinnerIcon");
  var _component_DTPaginator = resolveComponent("DTPaginator");
  var _component_DTTableHeader = resolveComponent("DTTableHeader");
  var _component_DTTableBody = resolveComponent("DTTableBody");
  var _component_DTTableFooter = resolveComponent("DTTableFooter");
  var _component_DTVirtualScroller = resolveComponent("DTVirtualScroller");
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx("root"),
    "data-scrollselectors": ".p-datatable-wrapper"
  }, _ctx.ptmi("root")), [renderSlot(_ctx.$slots, "default"), _ctx.loading ? (openBlock(), createElementBlock("div", mergeProps({
    key: 0,
    "class": _ctx.cx("mask")
  }, _ctx.ptm("mask")), [_ctx.$slots.loading ? renderSlot(_ctx.$slots, "loading", {
    key: 0
  }) : (openBlock(), createElementBlock(Fragment, {
    key: 1
  }, [_ctx.$slots.loadingicon ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.$slots.loadingicon), {
    key: 0,
    "class": normalizeClass(_ctx.cx("loadingIcon"))
  }, null, 8, ["class"])) : _ctx.loadingIcon ? (openBlock(), createElementBlock("i", mergeProps({
    key: 1,
    "class": [_ctx.cx("loadingIcon"), "pi-spin", _ctx.loadingIcon]
  }, _ctx.ptm("loadingIcon")), null, 16)) : (openBlock(), createBlock(_component_SpinnerIcon, mergeProps({
    key: 2,
    spin: "",
    "class": _ctx.cx("loadingIcon")
  }, _ctx.ptm("loadingIcon")), null, 16, ["class"]))], 64))], 16)) : createCommentVNode("", true), _ctx.$slots.header ? (openBlock(), createElementBlock("div", mergeProps({
    key: 1,
    "class": _ctx.cx("header")
  }, _ctx.ptm("header")), [renderSlot(_ctx.$slots, "header")], 16)) : createCommentVNode("", true), $options.paginatorTop ? (openBlock(), createBlock(_component_DTPaginator, {
    key: 2,
    rows: $data.d_rows,
    first: $data.d_first,
    totalRecords: $options.totalRecordsLength,
    pageLinkSize: _ctx.pageLinkSize,
    template: _ctx.paginatorTemplate,
    rowsPerPageOptions: _ctx.rowsPerPageOptions,
    currentPageReportTemplate: _ctx.currentPageReportTemplate,
    "class": normalizeClass(_ctx.cx("pcPaginator", {
      position: "top"
    })),
    onPage: _cache[0] || (_cache[0] = function($event) {
      return $options.onPage($event);
    }),
    alwaysShow: _ctx.alwaysShowPaginator,
    unstyled: _ctx.unstyled,
    pt: _ctx.ptm("pcPaginator")
  }, createSlots({
    _: 2
  }, [_ctx.$slots.paginatorstart ? {
    name: "start",
    fn: withCtx(function() {
      return [renderSlot(_ctx.$slots, "paginatorstart")];
    }),
    key: "0"
  } : void 0, _ctx.$slots.paginatorend ? {
    name: "end",
    fn: withCtx(function() {
      return [renderSlot(_ctx.$slots, "paginatorend")];
    }),
    key: "1"
  } : void 0, _ctx.$slots.paginatorfirstpagelinkicon ? {
    name: "firstpagelinkicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatorfirstpagelinkicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "2"
  } : void 0, _ctx.$slots.paginatorprevpagelinkicon ? {
    name: "prevpagelinkicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatorprevpagelinkicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "3"
  } : void 0, _ctx.$slots.paginatornextpagelinkicon ? {
    name: "nextpagelinkicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatornextpagelinkicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "4"
  } : void 0, _ctx.$slots.paginatorlastpagelinkicon ? {
    name: "lastpagelinkicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatorlastpagelinkicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "5"
  } : void 0, _ctx.$slots.paginatorjumptopagedropdownicon ? {
    name: "jumptopagedropdownicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatorjumptopagedropdownicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "6"
  } : void 0, _ctx.$slots.paginatorrowsperpagedropdownicon ? {
    name: "rowsperpagedropdownicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatorrowsperpagedropdownicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "7"
  } : void 0]), 1032, ["rows", "first", "totalRecords", "pageLinkSize", "template", "rowsPerPageOptions", "currentPageReportTemplate", "class", "alwaysShow", "unstyled", "pt"])) : createCommentVNode("", true), createBaseVNode("div", mergeProps({
    "class": _ctx.cx("tableContainer"),
    style: [_ctx.sx("tableContainer"), {
      maxHeight: $options.virtualScrollerDisabled ? _ctx.scrollHeight : ""
    }]
  }, _ctx.ptm("tableContainer")), [createVNode(_component_DTVirtualScroller, mergeProps({
    ref: "virtualScroller"
  }, _ctx.virtualScrollerOptions, {
    items: $options.processedData,
    columns: $options.columns,
    style: _ctx.scrollHeight !== "flex" ? {
      height: _ctx.scrollHeight
    } : void 0,
    scrollHeight: _ctx.scrollHeight !== "flex" ? void 0 : "100%",
    disabled: $options.virtualScrollerDisabled,
    loaderDisabled: "",
    inline: "",
    autoSize: "",
    showSpacer: false,
    pt: _ctx.ptm("virtualScroller")
  }), {
    content: withCtx(function(slotProps) {
      return [createBaseVNode("table", mergeProps({
        ref: "table",
        role: "table",
        "class": [_ctx.cx("table"), _ctx.tableClass],
        style: [_ctx.tableStyle, slotProps.spacerStyle]
      }, _objectSpread(_objectSpread({}, _ctx.tableProps), _ctx.ptm("table"))), [createVNode(_component_DTTableHeader, {
        columnGroup: $options.headerColumnGroup,
        columns: slotProps.columns,
        rowGroupMode: _ctx.rowGroupMode,
        groupRowsBy: _ctx.groupRowsBy,
        groupRowSortField: $options.groupRowSortField,
        reorderableColumns: _ctx.reorderableColumns,
        resizableColumns: _ctx.resizableColumns,
        allRowsSelected: $options.allRowsSelected,
        empty: $options.empty,
        sortMode: _ctx.sortMode,
        sortField: $data.d_sortField,
        sortOrder: $data.d_sortOrder,
        multiSortMeta: $data.d_multiSortMeta,
        filters: $data.d_filters,
        filtersStore: _ctx.filters,
        filterDisplay: _ctx.filterDisplay,
        filterButtonProps: $options.headerFilterButtonProps,
        filterInputProps: _ctx.filterInputProps,
        first: $data.d_first,
        onColumnClick: _cache[1] || (_cache[1] = function($event) {
          return $options.onColumnHeaderClick($event);
        }),
        onColumnMousedown: _cache[2] || (_cache[2] = function($event) {
          return $options.onColumnHeaderMouseDown($event);
        }),
        onFilterChange: $options.onFilterChange,
        onFilterApply: $options.onFilterApply,
        onColumnDragstart: _cache[3] || (_cache[3] = function($event) {
          return $options.onColumnHeaderDragStart($event);
        }),
        onColumnDragover: _cache[4] || (_cache[4] = function($event) {
          return $options.onColumnHeaderDragOver($event);
        }),
        onColumnDragleave: _cache[5] || (_cache[5] = function($event) {
          return $options.onColumnHeaderDragLeave($event);
        }),
        onColumnDrop: _cache[6] || (_cache[6] = function($event) {
          return $options.onColumnHeaderDrop($event);
        }),
        onColumnResizestart: _cache[7] || (_cache[7] = function($event) {
          return $options.onColumnResizeStart($event);
        }),
        onCheckboxChange: _cache[8] || (_cache[8] = function($event) {
          return $options.toggleRowsWithCheckbox($event);
        }),
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["columnGroup", "columns", "rowGroupMode", "groupRowsBy", "groupRowSortField", "reorderableColumns", "resizableColumns", "allRowsSelected", "empty", "sortMode", "sortField", "sortOrder", "multiSortMeta", "filters", "filtersStore", "filterDisplay", "filterButtonProps", "filterInputProps", "first", "onFilterChange", "onFilterApply", "unstyled", "pt"]), _ctx.frozenValue ? (openBlock(), createBlock(_component_DTTableBody, {
        key: 0,
        ref: "frozenBodyRef",
        value: _ctx.frozenValue,
        frozenRow: true,
        columns: slotProps.columns,
        first: $data.d_first,
        dataKey: _ctx.dataKey,
        selection: _ctx.selection,
        selectionKeys: $data.d_selectionKeys,
        selectionMode: _ctx.selectionMode,
        contextMenu: _ctx.contextMenu,
        contextMenuSelection: _ctx.contextMenuSelection,
        rowGroupMode: _ctx.rowGroupMode,
        groupRowsBy: _ctx.groupRowsBy,
        expandableRowGroups: _ctx.expandableRowGroups,
        rowClass: _ctx.rowClass,
        rowStyle: _ctx.rowStyle,
        editMode: _ctx.editMode,
        compareSelectionBy: _ctx.compareSelectionBy,
        scrollable: _ctx.scrollable,
        expandedRowIcon: _ctx.expandedRowIcon,
        collapsedRowIcon: _ctx.collapsedRowIcon,
        expandedRows: _ctx.expandedRows,
        expandedRowGroups: _ctx.expandedRowGroups,
        editingRows: _ctx.editingRows,
        editingRowKeys: $data.d_editingRowKeys,
        templates: _ctx.$slots,
        editButtonProps: $options.rowEditButtonProps,
        isVirtualScrollerDisabled: true,
        onRowgroupToggle: $options.toggleRowGroup,
        onRowClick: _cache[9] || (_cache[9] = function($event) {
          return $options.onRowClick($event);
        }),
        onRowDblclick: _cache[10] || (_cache[10] = function($event) {
          return $options.onRowDblClick($event);
        }),
        onRowRightclick: _cache[11] || (_cache[11] = function($event) {
          return $options.onRowRightClick($event);
        }),
        onRowTouchend: $options.onRowTouchEnd,
        onRowKeydown: $options.onRowKeyDown,
        onRowMousedown: $options.onRowMouseDown,
        onRowDragstart: _cache[12] || (_cache[12] = function($event) {
          return $options.onRowDragStart($event);
        }),
        onRowDragover: _cache[13] || (_cache[13] = function($event) {
          return $options.onRowDragOver($event);
        }),
        onRowDragleave: _cache[14] || (_cache[14] = function($event) {
          return $options.onRowDragLeave($event);
        }),
        onRowDragend: _cache[15] || (_cache[15] = function($event) {
          return $options.onRowDragEnd($event);
        }),
        onRowDrop: _cache[16] || (_cache[16] = function($event) {
          return $options.onRowDrop($event);
        }),
        onRowToggle: _cache[17] || (_cache[17] = function($event) {
          return $options.toggleRow($event);
        }),
        onRadioChange: _cache[18] || (_cache[18] = function($event) {
          return $options.toggleRowWithRadio($event);
        }),
        onCheckboxChange: _cache[19] || (_cache[19] = function($event) {
          return $options.toggleRowWithCheckbox($event);
        }),
        onCellEditInit: _cache[20] || (_cache[20] = function($event) {
          return $options.onCellEditInit($event);
        }),
        onCellEditComplete: _cache[21] || (_cache[21] = function($event) {
          return $options.onCellEditComplete($event);
        }),
        onCellEditCancel: _cache[22] || (_cache[22] = function($event) {
          return $options.onCellEditCancel($event);
        }),
        onRowEditInit: _cache[23] || (_cache[23] = function($event) {
          return $options.onRowEditInit($event);
        }),
        onRowEditSave: _cache[24] || (_cache[24] = function($event) {
          return $options.onRowEditSave($event);
        }),
        onRowEditCancel: _cache[25] || (_cache[25] = function($event) {
          return $options.onRowEditCancel($event);
        }),
        editingMeta: $data.d_editingMeta,
        onEditingMetaChange: $options.onEditingMetaChange,
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["value", "columns", "first", "dataKey", "selection", "selectionKeys", "selectionMode", "contextMenu", "contextMenuSelection", "rowGroupMode", "groupRowsBy", "expandableRowGroups", "rowClass", "rowStyle", "editMode", "compareSelectionBy", "scrollable", "expandedRowIcon", "collapsedRowIcon", "expandedRows", "expandedRowGroups", "editingRows", "editingRowKeys", "templates", "editButtonProps", "onRowgroupToggle", "onRowTouchend", "onRowKeydown", "onRowMousedown", "editingMeta", "onEditingMetaChange", "unstyled", "pt"])) : createCommentVNode("", true), createVNode(_component_DTTableBody, {
        ref: "bodyRef",
        value: $options.dataToRender(slotProps.rows),
        "class": normalizeClass(slotProps.styleClass),
        columns: slotProps.columns,
        empty: $options.empty,
        first: $data.d_first,
        dataKey: _ctx.dataKey,
        selection: _ctx.selection,
        selectionKeys: $data.d_selectionKeys,
        selectionMode: _ctx.selectionMode,
        contextMenu: _ctx.contextMenu,
        contextMenuSelection: _ctx.contextMenuSelection,
        rowGroupMode: _ctx.rowGroupMode,
        groupRowsBy: _ctx.groupRowsBy,
        expandableRowGroups: _ctx.expandableRowGroups,
        rowClass: _ctx.rowClass,
        rowStyle: _ctx.rowStyle,
        editMode: _ctx.editMode,
        compareSelectionBy: _ctx.compareSelectionBy,
        scrollable: _ctx.scrollable,
        expandedRowIcon: _ctx.expandedRowIcon,
        collapsedRowIcon: _ctx.collapsedRowIcon,
        expandedRows: _ctx.expandedRows,
        expandedRowGroups: _ctx.expandedRowGroups,
        editingRows: _ctx.editingRows,
        editingRowKeys: $data.d_editingRowKeys,
        templates: _ctx.$slots,
        editButtonProps: $options.rowEditButtonProps,
        virtualScrollerContentProps: slotProps,
        isVirtualScrollerDisabled: $options.virtualScrollerDisabled,
        onRowgroupToggle: $options.toggleRowGroup,
        onRowClick: _cache[26] || (_cache[26] = function($event) {
          return $options.onRowClick($event);
        }),
        onRowDblclick: _cache[27] || (_cache[27] = function($event) {
          return $options.onRowDblClick($event);
        }),
        onRowRightclick: _cache[28] || (_cache[28] = function($event) {
          return $options.onRowRightClick($event);
        }),
        onRowTouchend: $options.onRowTouchEnd,
        onRowKeydown: /* @__PURE__ */ __name(function onRowKeydown($event) {
          return $options.onRowKeyDown($event, slotProps);
        }, "onRowKeydown"),
        onRowMousedown: $options.onRowMouseDown,
        onRowDragstart: _cache[29] || (_cache[29] = function($event) {
          return $options.onRowDragStart($event);
        }),
        onRowDragover: _cache[30] || (_cache[30] = function($event) {
          return $options.onRowDragOver($event);
        }),
        onRowDragleave: _cache[31] || (_cache[31] = function($event) {
          return $options.onRowDragLeave($event);
        }),
        onRowDragend: _cache[32] || (_cache[32] = function($event) {
          return $options.onRowDragEnd($event);
        }),
        onRowDrop: _cache[33] || (_cache[33] = function($event) {
          return $options.onRowDrop($event);
        }),
        onRowToggle: _cache[34] || (_cache[34] = function($event) {
          return $options.toggleRow($event);
        }),
        onRadioChange: _cache[35] || (_cache[35] = function($event) {
          return $options.toggleRowWithRadio($event);
        }),
        onCheckboxChange: _cache[36] || (_cache[36] = function($event) {
          return $options.toggleRowWithCheckbox($event);
        }),
        onCellEditInit: _cache[37] || (_cache[37] = function($event) {
          return $options.onCellEditInit($event);
        }),
        onCellEditComplete: _cache[38] || (_cache[38] = function($event) {
          return $options.onCellEditComplete($event);
        }),
        onCellEditCancel: _cache[39] || (_cache[39] = function($event) {
          return $options.onCellEditCancel($event);
        }),
        onRowEditInit: _cache[40] || (_cache[40] = function($event) {
          return $options.onRowEditInit($event);
        }),
        onRowEditSave: _cache[41] || (_cache[41] = function($event) {
          return $options.onRowEditSave($event);
        }),
        onRowEditCancel: _cache[42] || (_cache[42] = function($event) {
          return $options.onRowEditCancel($event);
        }),
        editingMeta: $data.d_editingMeta,
        onEditingMetaChange: $options.onEditingMetaChange,
        unstyled: _ctx.unstyled,
        pt: _ctx.pt
      }, null, 8, ["value", "class", "columns", "empty", "first", "dataKey", "selection", "selectionKeys", "selectionMode", "contextMenu", "contextMenuSelection", "rowGroupMode", "groupRowsBy", "expandableRowGroups", "rowClass", "rowStyle", "editMode", "compareSelectionBy", "scrollable", "expandedRowIcon", "collapsedRowIcon", "expandedRows", "expandedRowGroups", "editingRows", "editingRowKeys", "templates", "editButtonProps", "virtualScrollerContentProps", "isVirtualScrollerDisabled", "onRowgroupToggle", "onRowTouchend", "onRowKeydown", "onRowMousedown", "editingMeta", "onEditingMetaChange", "unstyled", "pt"]), $options.hasSpacerStyle(slotProps.spacerStyle) ? (openBlock(), createElementBlock("tbody", mergeProps({
        key: 1,
        "class": _ctx.cx("virtualScrollerSpacer"),
        style: {
          height: "calc(".concat(slotProps.spacerStyle.height, " - ").concat(slotProps.rows.length * slotProps.itemSize, "px)")
        }
      }, _ctx.ptm("virtualScrollerSpacer")), null, 16)) : createCommentVNode("", true), createVNode(_component_DTTableFooter, {
        columnGroup: $options.footerColumnGroup,
        columns: slotProps.columns,
        pt: _ctx.pt
      }, null, 8, ["columnGroup", "columns", "pt"])], 16)];
    }),
    _: 1
  }, 16, ["items", "columns", "style", "scrollHeight", "disabled", "pt"])], 16), $options.paginatorBottom ? (openBlock(), createBlock(_component_DTPaginator, {
    key: 3,
    rows: $data.d_rows,
    first: $data.d_first,
    totalRecords: $options.totalRecordsLength,
    pageLinkSize: _ctx.pageLinkSize,
    template: _ctx.paginatorTemplate,
    rowsPerPageOptions: _ctx.rowsPerPageOptions,
    currentPageReportTemplate: _ctx.currentPageReportTemplate,
    "class": normalizeClass(_ctx.cx("pcPaginator", {
      position: "bottom"
    })),
    onPage: _cache[43] || (_cache[43] = function($event) {
      return $options.onPage($event);
    }),
    alwaysShow: _ctx.alwaysShowPaginator,
    unstyled: _ctx.unstyled,
    pt: _ctx.ptm("pcPaginator")
  }, createSlots({
    _: 2
  }, [_ctx.$slots.paginatorstart ? {
    name: "start",
    fn: withCtx(function() {
      return [renderSlot(_ctx.$slots, "paginatorstart")];
    }),
    key: "0"
  } : void 0, _ctx.$slots.paginatorend ? {
    name: "end",
    fn: withCtx(function() {
      return [renderSlot(_ctx.$slots, "paginatorend")];
    }),
    key: "1"
  } : void 0, _ctx.$slots.paginatorfirstpagelinkicon ? {
    name: "firstpagelinkicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatorfirstpagelinkicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "2"
  } : void 0, _ctx.$slots.paginatorprevpagelinkicon ? {
    name: "prevpagelinkicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatorprevpagelinkicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "3"
  } : void 0, _ctx.$slots.paginatornextpagelinkicon ? {
    name: "nextpagelinkicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatornextpagelinkicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "4"
  } : void 0, _ctx.$slots.paginatorlastpagelinkicon ? {
    name: "lastpagelinkicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatorlastpagelinkicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "5"
  } : void 0, _ctx.$slots.paginatorjumptopagedropdownicon ? {
    name: "jumptopagedropdownicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatorjumptopagedropdownicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "6"
  } : void 0, _ctx.$slots.paginatorrowsperpagedropdownicon ? {
    name: "rowsperpagedropdownicon",
    fn: withCtx(function(slotProps) {
      return [renderSlot(_ctx.$slots, "paginatorrowsperpagedropdownicon", {
        "class": normalizeClass(slotProps["class"])
      })];
    }),
    key: "7"
  } : void 0]), 1032, ["rows", "first", "totalRecords", "pageLinkSize", "template", "rowsPerPageOptions", "currentPageReportTemplate", "class", "alwaysShow", "unstyled", "pt"])) : createCommentVNode("", true), _ctx.$slots.footer ? (openBlock(), createElementBlock("div", mergeProps({
    key: 4,
    "class": _ctx.cx("footer")
  }, _ctx.ptm("footer")), [renderSlot(_ctx.$slots, "footer")], 16)) : createCommentVNode("", true), createBaseVNode("div", mergeProps({
    ref: "resizeHelper",
    "class": _ctx.cx("columnResizeIndicator"),
    style: {
      "display": "none"
    }
  }, _ctx.ptm("columnResizeIndicator")), null, 16), _ctx.reorderableColumns ? (openBlock(), createElementBlock("span", mergeProps({
    key: 5,
    ref: "reorderIndicatorUp",
    "class": _ctx.cx("rowReorderIndicatorUp"),
    style: {
      "position": "absolute",
      "display": "none"
    }
  }, _ctx.ptm("rowReorderIndicatorUp")), [(openBlock(), createBlock(resolveDynamicComponent(_ctx.$slots.rowreorderindicatorupicon || _ctx.$slots.reorderindicatorupicon || "ArrowDownIcon")))], 16)) : createCommentVNode("", true), _ctx.reorderableColumns ? (openBlock(), createElementBlock("span", mergeProps({
    key: 6,
    ref: "reorderIndicatorDown",
    "class": _ctx.cx("rowReorderIndicatorDown"),
    style: {
      "position": "absolute",
      "display": "none"
    }
  }, _ctx.ptm("rowReorderIndicatorDown")), [(openBlock(), createBlock(resolveDynamicComponent(_ctx.$slots.rowreorderindicatordownicon || _ctx.$slots.reorderindicatordownicon || "ArrowUpIcon")))], 16)) : createCommentVNode("", true)], 16);
}
__name(render2, "render");
script.render = render2;
export {
  script as a,
  script$r as s
};
//# sourceMappingURL=index-DpF-ptbJ.js.map
