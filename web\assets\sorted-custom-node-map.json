{"PreviewImage": 8794, "CLIPTextEncode": 6888, "VAEDecode": 4671, "LoadImage": 4085, "SaveImage": 3438, "KSampler": 3324, "CheckpointLoaderSimple": 2958, "EmptyLatentImage": 2014, "VAEEncode": 1627, "VAELoader": 1376, "ControlNetLoader": 1372, "ShowText|pysssss": 1346, "LoraLoader": 1229, "VHS_VideoCombine": 1172, "UpscaleModelLoader": 1079, "MaskToImage": 998, "ControlNetApplyAdvanced": 939, "Text Concatenate": 831, "ImageScale": 765, "SDXLPromptStyler": 758, "Text _O": 725, "UltralyticsDetectorProvider": 657, "ControlNetLoaderAdvanced": 653, "KSamplerAdvanced": 642, "ImageUpscaleWithModel": 640, "CLIPSetLastLayer": 628, "CLIPTextEncodeSDXL": 618, "CLIPVisionLoader": 612, "ImageScaleBy": 606, "CR Prompt Text": 591, "ACN_AdvancedControlNetApply": 572, "IPAdapterModelLoader": 570, "PrepImageForClipVision": 565, "PreviewBridge": 547, "ControlNetApply": 535, "UltimateSDUpscale": 510, "KSampler (Efficient)": 495, "SAMLoader": 470, "Text Concatenate (JPS)": 449, "ToBasicPipe": 419, "Text Multiline": 416, "KSamplerSelect": 410, "CM_NumberBinaryOperation": 393, "IPAdapterUnifiedLoader": 388, "MaskPreview+": 384, "String Literal": 383, "ImpactSwitch": 381, "ImageResize+": 371, "InvertMask": 362, "IPAdapterAdvanced": 358, "UNETLoader": 349, "AIO_Preprocessor": 346, "ConditioningConcat": 334, "CR Text Input Switch": 327, "If ANY execute A else B": 326, "FaceDetailer": 312, "LoraLoaderModelOnly": 304, "ConditioningCombine": 300, "ImageCompositeMasked": 299, "BasicScheduler": 294, "FreeU_V2": 294, "KSampler Adv. (Efficient)": 293, "CR Text": 284, "FromBasicPipe_v2": 278, "DualCLIPLoader": 278, "ImageToMask": 278, "MathExpression|pysssss": 278, "SDXLResolutionPresets": 274, "SamplerCustomAdvanced": 274, "Paste By Mask": 273, "Efficient Loader": 273, "CR Text Input Switch (4 way)": 272, "CM_IntToNumber": 268, "DWPreprocessor": 267, "Concat Text _O": 266, "Int Literal": 265, "WD14Tagger|pysssss": 263, "CR LoRA Stack": 260, "ImageInvert": 260, "GrowMask": 259, "mape Variable": 258, "BasicGuider": 246, "CM_IntBinaryOperation": 246, "RandomNoise": 245, "CR Apply LoRA Stack": 245, "Image Filter Adjustments": 243, "GroundingDinoSAMSegment (segment anything)": 238, "Image Overlay": 238, "FromBasicPipe": 234, "LatentUpscaleBy": 232, "CR Text Concatenate": 232, "VHS_LoadVideo": 231, "ImageBatch": 225, "GetImageSize+": 224, "Logic Boolean": 219, "Image Save": 218, "SimpleMath+": 216, "VAEDecodeTiled": 215, "SetLatentNoiseMask": 214, "SamplerCustom": 212, "ADE_AnimateDiffLoaderWithContext": 209, "StringFunction|pysssss": 207, "CR Image Input Switch": 206, "PlaySound|pysssss": 205, "ReActorFaceSwap": 203, "FloatConstant": 200, "GroundingDinoModelLoader (segment anything)": 197, "LineArtPreprocessor": 196, "CLIPTextEncodeSDXLRefiner": 189, "ImageBlend": 187, "ConcatStringSingle": 186, "ImpactInt": 185, "ADE_AnimateDiffUniformContextOptions": 184, "LoraLoader|pysssss": 183, "RIFE VFI": 181, "Save Text File": 180, "ImageScaleToTotalPixels": 180, "Number to Text": 180, "GrowMaskWithBlur": 179, "VAEEncodeForInpaint": 177, "FluxGuidance": 171, "CR Integer To String": 171, "ttN imageOutput": 170, "Text to Number": 170, "CR Overlay Text": 169, "CannyEdgePreprocessor": 164, "ImageCrop+": 164, "Image Resize": 162, "Automatic CFG": 162, "EmptyImage": 162, "ImagePadForOutpaint": 156, "SolidMask": 150, "CR Upscale Image": 150, "DisplayText_Zho": 150, "ConstrainImage|pysssss": 149, "Change Channel Count": 149, "CR Simple Binary Pattern": 147, "VHS_SplitImages": 146, "DeepTranslatorTextNode": 144, "ConcatText_Zho": 144, "ReroutePrimitive|pysssss": 143, "CheckpointLoader|pysssss": 143, "SaveImagetoPath": 141, "DPMagicPrompt": 140, "BboxDetectorSEGS": 137, "Seed Everywhere": 137, "DepthAnythingPreprocessor": 137, "Text Load Line From File": 137, "InstantIDModelLoader": 135, "DetailerForEach": 135, "MaskComposite": 135, "InstantIDFaceAnalysis": 133, "ImageSharpen": 131, "ConditioningZeroOut": 131, "ModelSamplingDiscrete": 128, "Image To Mask": 125, "SDPromptSaver": 125, "Cfg Literal": 122, "Text Random Line": 122, "SegsToCombinedMask": 122, "ConditioningSetTimestepRange": 121, "ColorMatch": 121, "Image Size to Number": 120, "SimpleText": 118, "ApplyInstantID": 117, "BNK_CLIPTextEncodeAdvanced": 117, "CLIPTextEncodeFlux": 116, "CR SDXL Aspect Ratio": 116, "smZ CLIPTextEncode": 114, "Bus Node": 114, "PreviewTextNode": 114, "PatchModelAddDownscale": 113, "Seed Generator": 112, "SAMModelLoader (segment anything)": 112, "ScaledSoftControlNetWeights": 111, "HighRes-Fix Script": 111, "IPAdapter": 110, "CM_FloatToNumber": 110, "Cut By Mask": 109, "ttN text7BOX_concat": 109, "ADE_UseEvolvedSampling": 108, "CR Multi-ControlNet Stack": 108, "MiDaS-DepthMapPreprocessor": 107, "IPAdapterEncoder": 106, "ImpactSimpleDetectorSEGS": 105, "ImageOnlyCheckpointLoader": 104, "Get Image Size": 104, "LoRA Stacker": 103, "Float": 103, "GetImageSize": 101, "ImageResizeKJ": 101, "DifferentialDiffusion": 101, "LoadImagesFromDirectory": 101, "ttN textDebug": 101, "OllamaGenerate": 100, "ImageSelector": 99, "FaceRestoreModelLoader": 98, "SelfAttentionGuidance": 98, "VideoLinearCFGGuidance": 98, "Image Blending Mode": 97, "ImageCASharpening+": 96, "ImageSender": 96, "Zoe-DepthMapPreprocessor": 95, "SVD_img2vid_Conditioning": 95, "ImageReceiver": 95, "Image Rembg (Remove Background)": 93, "RescaleCFG": 93, "FaceRestoreCFWithModel": 90, "ModelSamplingFlux": 90, "OpenposePreprocessor": 89, "BatchPromptSchedule": 89, "GetImageSizeAndCount": 89, "AlignYourStepsScheduler": 88, "ADE_AnimateDiffLoRALoader": 87, "TextInput_": 87, "ADE_LoadAnimateDiffModel": 87, "EmptySD3LatentImage": 86, "JoinImageWithAlpha": 86, "SplitImageWithAlpha": 86, "PerturbedAttentionGuidance": 86, "InpaintModelConditioning": 85, "ImageCrop": 85, "Text Prompt (JPS)": 85, "Florence2Run": 84, "Text Find and Replace": 84, "VAEEncodeTiled": 83, "OllamaVision": 83, "LoadImageMask": 83, "ImpactGaussianBlurMask": 82, "DPRandomGenerator": 82, "FaceDetailerPipe": 79, "ImpactImageBatchToImageList": 79, "CR Image Output": 79, "CR Aspect Ratio": 78, "MaskBlur+": 77, "LoadAndApplyICLightUnet": 77, "ICLightConditioning": 77, "BRIA_RMBG_Zho": 77, "Image to Noise": 76, "AV_ControlNetPreprocessor": 76, "Canny": 75, "Lora Loader": 75, "Control Net Stacker": 75, "RemapImageRange": 75, "InpaintPreprocessor": 74, "ImageListToImageBatch": 74, "Image Transpose": 74, "CR Apply Multi-ControlNet": 74, "Image Blend": 72, "JWImageResizeByLongerSide": 71, "DownloadAndLoadFlorence2Model": 71, "ADE_AnimateDiffSamplingSettings": 70, "ImageGenResolutionFromImage": 70, "IPAdapterFaceID": 70, "HintImageEnchance": 70, "CR Conditioning Input Switch": 70, "CR Seed": 69, "VHS_LoadVideoPath": 69, "Preview Chooser": 69, "CM_NumberToInt": 69, "BLIP Analyze Image": 68, "ImpactMakeImageBatch": 68, "CheckpointLoaderSimpleWithNoiseSelect": 68, "CM_FloatBinaryOperation": 68, "BLIP Model Loader": 67, "RepeatLatentBatch": 67, "Mask Crop Region": 67, "ViewText": 67, "Int": 66, "IPAdapterBatch": 66, "KSampler //Inspire": 65, "BRIA_RMBG_ModelLoader_Zho": 65, "CR Conditioning Mixer": 65, "IPAdapterUnifiedLoaderFaceID": 63, "easy imageSize": 63, "Image Blend by Mask": 63, "RepeatImageBatch": 62, "Number Operation": 62, "INPAINT_ApplyFooocusInpaint": 62, "Text String": 61, "BrushNetLoader": 61, "Image Levels Adjustment": 61, "INPAINT_LoadFooocusInpaint": 61, "TilePreprocessor": 60, "SDXLEmptyLatentSizePicker+": 60, "ImpactSEGSOrderedFilter": 60, "ConditioningSetMask": 60, "SDLoraLoader": 60, "RemapMaskRange": 59, "easy cleanGpuUsed": 59, "SEGSPreview": 59, "Upscale Model Loader": 58, "RebatchImages": 58, "FeatherMask": 58, "ColorCorrect": 58, "Image Remove Background (rembg)": 58, "Image Crop Location": 58, "HEDPreprocessor": 57, "ToBinaryMask": 57, "unCLIPConditioning": 57, "LayerMask: MaskPreview": 57, "JWImageResize": 57, "VHS_LoadImagesPath": 56, "GlobalSeed //Inspire": 56, "CLIPLoader": 56, "SUPIR_first_stage": 56, "SUPIR_decode": 56, "SUPIR_sample": 56, "SUPIR_conditioner": 56, "String": 56, "SaveText|pysssss": 55, "ImpactKSamplerBasicPipe": 55, "SUPIR_encode": 55, "CM_NearestSDXLResolution": 55, "MaskToSEGS": 55, "Image Paste Crop by Location": 55, "ADE_ApplyAnimateDiffModelSimple": 54, "EditBasicPipe": 54, "Text Random Prompt": 53, "INPAINT_VAEEncodeInpaintConditioning": 53, "IPAdapterNoise": 53, "BrushNet": 53, "FILM VFI": 53, "gcLatentTunnel": 53, "Getter": 52, "StableCascade_StageB_Conditioning": 51, "LayerUtility: ImageBlendAdvance": 51, "SEGSPaste": 51, "CLIPVisionEncode": 51, "CR Text Replace": 51, "EG_RY_HT": 51, "Yoloworld_ESAM_Zho": 50, "SUPIR_model_loader_v2": 50, "CLIPSeg Masking": 50, "ConditioningSetArea": 50, "ADE_LoopedUniformContextOptions": 49, "LatentUpscale": 49, "CR Simple Image Compare": 48, "MaskFromRGBCMYBW+": 48, "MaskFromColor+": 48, "NNLatentUpscale": 48, "CropFace": 48, "Constant Number": 48, "Int to Text": 48, "IPAdapterStyleComposition": 47, "easy loraStack": 47, "ConditioningAverage": 47, "Text to Conditioning": 47, "BasicPipeToDetailerPipe": 46, "PixelPerfectResolution": 46, "CLIPTextEncode (BlenderNeko Advanced + NSP)": 46, "INPAINT_MaskedFill": 45, "INTConstant": 45, "ADE_StandardUniformContextOptions": 45, "CM_FloatToInt": 45, "Combine Masks": 45, "LayerUtility: ImageBlend": 45, "Random Number": 45, "SDTurboScheduler": 44, "ModelSamplingSD3": 44, "Integer Switch (JPS)": 44, "String to Text": 43, "Image Lucy Sharpen": 43, "ADE_AnimateDiffLoaderGen1": 43, "FrequencyCombination": 43, "Mask Morphology": 43, "StableCascade_EmptyLatentImage": 42, "AddLabel": 42, "Image Blank": 42, "ColorMatchImage": 42, "ImpactKSamplerAdvancedBasicPipe": 42, "LeReS-DepthMapPreprocessor": 41, "INPAINT_MaskedBlur": 41, "CR Float To Integer": 41, "LayerMask: SegmentAnythingUltra V2": 41, "easy showAnything": 41, "ADE_MultivalDynamic": 41, "AnimeLineArtPreprocessor": 41, "Yoloworld_ModelLoader_Zho": 40, "ESAM_ModelLoader_Zho": 40, "ADE_StandardStaticContextOptions": 40, "KSamplerAdvanced //Inspire": 40, "ImageBlur": 40, "ADE_ApplyAnimateDiffModel": 40, "CLIPSeg": 40, "MeshGraphormer-DepthMapPreprocessor": 39, "FreeU": 39, "ResizeImageMixlab": 39, "ImageRemoveBackground+": 39, "ImageCompositeFromMaskBatch+": 39, "LayerUtility: PurgeVRAM": 39, "ImpactControlNetApplySEGS": 38, "RemBGSession+": 38, "MaskFix+": 38, "ImageMaskSwitch": 38, "CR Simple Value Scheduler": 38, "SubtractMask": 37, "ImageConcanate": 37, "CR Latent Input Switch": 37, "UnetLoaderGGUF": 37, "VHS_MergeImages": 37, "PulidModelLoader": 36, "PulidEvaClipLoader": 36, "IPAdapterRegionalConditioning": 36, "LatentBlend": 36, "Gemini_API_S_Zho": 36, "DepthAnythingV2Preprocessor": 36, "SEGSDetailerForAnimateDiff": 36, "VAEEncodeArgMax": 36, "SomethingToString": 36, "CreateShapeMask": 36, "Power KSampler Advanced (PPF Noise)": 36, "Image Crop Face": 36, "easy imageRemBg": 35, "ApplyFluxControlNet": 35, "LoadFluxControlNet": 35, "IPAdapterTiled": 35, "ImpactSimpleDetectorSEGS_for_AD": 35, "Checkpoint Selector": 35, "Text Input [Dream]": 35, "LivePortraitProcess": 34, "UltimateSDUpscaleNoUpscale": 34, "ImageScaleToMegapixels": 34, "ImpactDilateMask": 34, "CM_NumberToFloat": 34, "CR Color Panel": 34, "CR VAE Input Switch": 34, "Resize Image for SDXL": 34, "Setter": 34, "ApplyPulid": 33, "XlabsSampler": 33, "ModelMergeSimple": 33, "SDXL Prompt Handling (JPS)": 33, "CreateFadeMaskAdvanced": 33, "CR Model Input Switch": 33, "DiffControlNetLoader": 32, "PulidInsightFaceLoader": 32, "CR Load LoRA": 32, "ttN hiresfixScale": 32, "Latent Noise Injection": 31, "PainterNode": 31, "OneButtonPrompt": 31, "easy int": 31, "ControlNetApplySD3": 31, "ADE_EmptyLatentImageLarge": 31, "LayerUtility: ColorPicker": 31, "Image Paste Crop": 31, "DetailerForEachDebug": 31, "Create Solid Color": 31, "OneFormer-COCO-SemSegPreprocessor": 30, "VHS_GetImageCount": 30, "CR Simple Meme Template": 30, "ACN_SparseCtrlLoaderAdvanced": 30, "BNK_InjectNoise": 30, "SDXLAspectRatioSelector": 30, "ImageFromBatch": 30, "RebatchLatents": 30, "CR Module Input": 30, "DownloadAndLoadLivePortraitModels": 29, "easy float": 29, "ReActorRestoreFace": 29, "ImageCompositeAbsolute": 29, "LatentGaussianNoise": 29, "LayerMask: MaskGrow": 29, "CR Image Input Switch (4 way)": 29, "SaveAnimatedWEBP": 29, "CR Draw Text": 29, "ScribblePreprocessor": 28, "AddMask": 28, "Image Remove Background (Alpha)": 28, "IPAdapterInsightFaceLoader": 28, "ImpactImageInfo": 28, "DetailerForEachDebugPipe": 28, "Blur": 28, "VHS_LoadImages": 28, "ttN text": 28, "AnyLineArtPreprocessor_aux": 28, "FilmGrain": 28, "SAMDetectorCombined": 28, "KarrasScheduler": 28, "ApplyInstantIDAdvanced": 27, "CM_SDXLResolution": 27, "Seed": 27, "Text List to Text": 27, "Width/Height Literal": 27, "LayerUtility: ColorImage V2": 27, "Perlin Power Fractal Settings (PPF Noise)": 27, "DownloadAndLoadSAM2Model": 27, "CR Overlay Transparent Image": 27, "Deep Bump (mtb)": 27, "Replace Text _O": 27, "Load Image Batch": 26, "ImageConcatMulti": 26, "ImageBatchMulti": 26, "ImpactFloat": 26, "Mask To Region": 26, "ImageEffectsAdjustment": 26, "LatentKeyframeTiming": 26, "TimestepKeyframe": 26, "Sam2Segmentation": 26, "IPAdapterMS": 26, "ImageGaussianBlur": 26, "Note _O": 26, "TonemapNoiseWithRescaleCFG": 25, "easy stylesSelector": 25, "LayerUtility: ImageScaleByAspectRatio V2": 25, "Zoe_DepthAnythingPreprocessor": 25, "Cross-Hatch Power Fractal Settings (PPF Noise)": 25, "ResizeMask": 25, "ReActorLoadFaceModel": 25, "Number to String": 25, "PrimereSamplersSteps": 25, "ImageExpandBatch+": 25, "FL_ImageCaptionSaver": 25, "floatToText _O": 24, "LayerUtility: SimpleTextImage": 24, "ImageResize": 24, "DetailTransfer": 24, "ImpactWildcardProcessor": 24, "Bounded Image Crop with Mask": 24, "CR Float To String": 24, "easy positive": 24, "BiRefNet_Zho": 24, "BasicPipeToDetailerPipeSDXL": 24, "Text_Image_Zho": 24, "LayerColor: Brightness & Contrast": 24, "Any To String (mtb)": 24, "Bounded Image Crop": 23, "SplineEditor": 23, "TripleCLIPLoader": 23, "SUPIR_Upscale": 23, "CR Image Grid Panel": 23, "SamplerLCMCycle": 23, "PixelKSampleUpscalerProvider": 23, "Apply ControlNet Stack": 23, "CR Select Model": 23, "LatentKeyframeBatchedGroup": 23, "BiRefNet_ModelLoader_Zho": 23, "DeepTranslatorCLIPTextEncodeNode": 23, "CoreMLDetailerHookProvider": 23, "MultiplicationNode": 23, "CR Color Tint": 23, "ColorToMask": 22, "ColorPreprocessor": 22, "GetImageSize_": 22, "LCMScheduler": 22, "CLIPTextEncodeSD3": 22, "ACN_SparseCtrlSpreadMethodNode": 22, "CogVideoTextEncode": 22, "AutoNegativePrompt": 22, "AlphaChanelRemove": 22, "CR Latent Batch Size": 22, "SDXLPromptStylerbyMood": 22, "Image Threshold": 22, "JWImageResizeByFactor": 22, "ADE_AnimateDiffCombine": 22, "Conditioning Switch (JPS)": 22, "DPCombinatorialGenerator": 21, "LoadImagesFromDir //Inspire": 21, "ACN_SparseCtrlRGBPreprocessor": 21, "LivePortraitCropper": 21, "CM_IntToFloat": 21, "BatchAverageImage": 21, "ICLightAppply": 21, "INPAINT_LoadInpaintModel": 21, "INPAINT_InpaintWithModel": 21, "IterativeLatentUpscale": 21, "Load RetinaFace": 21, "Crop Face": 21, "ImageScaleDownBy": 21, "Text List": 21, "SDXLPromptStylerbyMileHigh": 21, "ImageCompositeBy_Zho": 21, "CR Color Gradient": 21, "Image Color Shift [Dream]": 21, "CR Image Border": 20, "LlavaClipLoader": 20, "LLava Loader Simple": 20, "ModelSamplingContinuousEDM": 20, "Convert Masks to Images": 20, "Separate Mask Components": 20, "CR SD1.5 Aspect Ratio": 20, "LoadImagesFromURL": 20, "GoogleTranslateTextNode": 20, "ttN concat": 20, "Florence2toCoordinates": 20, "easy ipadapterApply": 20, "Text Input Switch": 20, "JoinStringMulti": 20, "ADE_AnimateDiffModelSettingsSimple": 19, "Image Bounds": 19, "LLavaSamplerSimple": 19, "SDPromptReader": 19, "KSampler SDXL (Eff.)": 19, "OllamaGenerateAdvance": 19, "Mask Invert": 19, "CR Thumbnail Preview": 19, "Mask Dominant Region": 19, "MaskListToMaskBatch": 19, "SDXLPromptStylerbyLighting": 19, "SDXLPromptStylerbyCamera": 19, "SDXLPromptStylerbyFilter": 19, "InvertMask (segment anything)": 19, "RemoveNoiseMask": 19, "NormalizedAmplitudeToNumber": 19, "RegionalConditioningSimple //Inspire": 19, "TiledDiffusion": 18, "Mask Fill Holes": 18, "LineartStandardPreprocessor": 18, "Image Saver": 18, "Mask Gaussian Region": 18, "IPAdapterCombineEmbeds": 18, "IPAdapterEmbeds": 18, "Sampler Selector": 18, "CLIPMergeSimple": 18, "KRestartSampler": 18, "Eff. Loader SDXL": 18, "RandomInt": 18, "Seed String": 18, "ArgosTranslateTextNode": 18, "Unpack SDXL Tuple": 18, "Mask To Image (mtb)": 18, "CR Split String": 18, "DynamicThresholdingFull": 18, "CR Simple Text Watermark": 18, "AnyLinePreprocessor": 18, "SplitSigmas": 18, "SDXLPromptStylerbyDepth": 18, "SDXLPromptStylerbyTimeofDay": 18, "SDXLPromptStylerbyFocus": 18, "SDXLPromptStylerbySubject": 18, "SDXLPromptStylerbyArtist": 18, "Upscale by Factor with Model (WLSH)": 18, "NormalizedAmplitudeToGraph": 18, "BatchAmplitudeSchedule": 18, "VHS_VideoInfo": 17, "Color Correct (mtb)": 17, "FrequencySeparationHSV": 17, "intToFloat _O": 17, "ImpactCombineConditionings": 17, "ICLightApplyMaskGrey": 17, "Images to RGB": 17, "AnimalPosePreprocessor": 17, "PromptSchedule": 17, "FreeU (Advanced)": 17, "LatentCompositeMasked": 17, "Image Paste Face": 17, "CR Module Pipe Loader": 17, "APersonMaskGenerator": 17, "PiDiNetPreprocessor": 16, "LoraLoaderTagsQuery": 16, "EnhanceImage": 16, "KSamplerAdvancedProvider": 16, "IterativeImageUpscale": 16, "ImpactSEGSRangeFilter": 16, "Image Select Color": 16, "Text_Image_Multiline_Zho": 16, "show_text_party": 16, "ACN_ControlNet++LoaderSingle": 16, "LayerUtility: GetImageSize": 16, "Color": 16, "LatentKeyframe": 16, "ConsoleDebug+": 16, "BatchCount+": 16, "ReActorSaveFaceModel": 16, "TextBox": 16, "KSampler Cycle": 16, "Create QR Code": 16, "DeepDanbooruCaption": 16, "FileNamePrefix": 16, "CLIPSeg Model Loader": 16, "ColorizeDepthmap": 16, "LayerUtility: CropByMask V2": 16, "UpscaleImageByUsingModel": 16, "BAE-NormalMapPreprocessor": 15, "Inset Image Bounds": 15, "Scheduler Selector": 15, "CR Page Layout": 15, "CascadeResolutions": 15, "LayerMask: PersonMaskUltra V2": 15, "ScreenShare": 15, "LLMSampler": 15, "BNK_CutoffSetRegions": 15, "ImpactStringSelector": 15, "ImpactMakeImageList": 15, "SDXLPromptStylerbyImpressionism": 15, "SDXL Prompt Styler (JPS)": 15, "ImageCompositeRelative": 15, "Textbox": 15, "Fans Text Concatenate": 15, "easy pipeOut": 15, "easy kSampler": 15, "ImageResizeAndCropNode": 15, "VHS_DuplicateImages": 15, "ImageAndMaskPreview": 14, "IPAdapterCombineParams": 14, "easy seed": 14, "BNK_Unsampler": 14, "MarigoldDepthEstimation": 14, "BatchPromptScheduleLatentInput": 14, "LLM": 14, "IPAdapterWeights": 14, "Image Remove Background Rembg (mtb)": 14, "FloatSlider": 14, "Robust Video Matting": 14, "CR String To Combo": 14, "easy fullkSampler": 14, "easy negative": 14, "T5TextEncode": 14, "Manga2Anime_LineArt_Preprocessor": 14, "SDXLPromptStylerbyMythicalCreature": 14, "SDXLPromptStylerbyFantasySetting": 14, "SDXLPromptbyWildlifeArt": 14, "SDXLPromptStylerHorror": 14, "SDXLPromptStylerMisc": 14, "SDXLPromptStylerbyEnvironment": 14, "SDXLPromptStylerbySurrealism": 14, "SDXLPromptbyStreetArt": 14, "SDXLPromptStylerbyComposition": 14, "SDXLPromptbyGothicRevival": 14, "CR Multi Upscale Stack": 14, "CR Apply Multi Upscale": 14, "DensePosePreprocessor": 14, "SDTypeConverter": 14, "PreviewAudio": 14, "LayerFilter: GaussianBlur": 14, "ImpactSEGSToMaskList": 14, "T5TextEncode #ELLA": 14, "LamaRemover": 14, "LayerUtility: ImageMaskScaleAs": 14, "ToonCrafterInterpolation": 14, "Image Generate Gradient": 14, "ChangeImageBatchSize //Inspire": 14, "Mask Smooth Region": 14, "Number Input Condition": 14, "Int-🔬": 14, "RawText": 14, "Create Rect Mask": 13, "TransitionMask+": 13, "Scribble_XDoG_Preprocessor": 13, "Load Lora": 13, "StableCascade_StageC_VAEEncode": 13, "LayerStyle: DropShadow": 13, "StyleAlignedBatchAlign": 13, "CR Simple Prompt List": 13, "SegmDetectorSEGS": 13, "SetUnionControlNetType": 13, "BlendInpaint": 13, "Noise Control Script": 13, "SDXLPromptStylerAll": 13, "Empty Latent Ratio Select SDXL": 13, "ImageClamp": 13, "ImageCropByRatio": 13, "LoadFluxIPAdapter": 13, "FaceAnalysisModels": 13, "TextNode": 13, "VividSharpen": 13, "LoadAndResizeImage": 13, "InpaintCrop": 13, "FreeU_V2 (Advanced)": 13, "FlatLatentsIntoSingleGrid": 13, "AudioPlay": 13, "JjkShowText": 13, "Number Counter": 13, "ImpactControlBridge": 13, "BNK_TiledKSampler": 12, "IPAdapterFromParams": 12, "BooleanPrimitive": 12, "ModelSamplingStableCascade": 12, "ExpressionEditor": 12, "PhotoMakerStyles": 12, "LivePortraitComposite": 12, "LLM_api_loader": 12, "LayerUtility: ImageRemoveAlpha": 12, "PhotoMakerLoader": 12, "SDXL Resolutions (JPS)": 12, "Gemini_API_Zho": 12, "unCLIPCheckpointLoader": 12, "ApplyFluxIPAdapter": 12, "ADE_AnimateDiffModelSettings": 12, "0246.JunctionBatch": 12, "AB SamplerCustom (experimental)": 12, "LatentFromBatch": 12, "LayerUtility: ImageCombineAlpha": 12, "mxSlider2D": 12, "GateNormalizedAmplitude": 12, "Masks Add": 12, "FluxTrainSave": 12, "FluxTrainLoop": 12, "FluxTrainValidate": 12, "VisualizeLoss": 12, "RegionalIPAdapterColorMask //Inspire": 12, "ConvertImg": 12, "ImpactRemoteBoolean": 12, "easy ipadapterApplyADV": 12, "ConditioningCombineMultiple+": 11, "InstantX Flux Union ControlNet Loader": 11, "ImageScaleDownToSize": 11, "Automatic CFG - Warp Drive": 11, "easy boolean": 11, "LayeredDiffusionApply": 11, "CogVideoSampler": 11, "CogVideoDecode": 11, "DownloadAndLoadCogVideoModel": 11, "ImageFlip+": 11, "LayerUtility: LaMa": 11, "RecommendedResCalc": 11, "ImageDesaturate+": 11, "easy imageScaleDownToSize": 11, "ttN pipeKSampler": 11, "RegionalPrompt": 11, "Image Input Switch": 11, "LivePortraitLoadCropper": 11, "easy clearCacheAll": 11, "ImageBatchMultiple+": 11, "LayerUtility: ExtendCanvas": 11, "FaceKeypointsPreprocessor": 11, "CR Comic Panel Templates": 11, "easy fullLoader": 11, "ETN_ApplyMaskToImage": 11, "easy imageChooser": 11, "DualCLIPLoaderGGUF": 11, "AnimateDiffModuleLoader": 11, "AnimateDiffSampler": 11, "Aegisflow Image Pass": 11, "LatentSwitch": 11, "LayerColor: ColorAdapter": 11, "LaMaInpaint": 11, "CropMask": 11, "easy promptReplace": 11, "CR Draw Shape": 11, "easy comfyLoader": 11, "Prompt Weight (WLSH)": 11, "RandomPrompt": 11, "CR Random Hex Color": 11, "ImageFromBatch+": 11, "PrimerePrompt": 11, "ImpactCompare": 11, "CR Data Bus Out": 11, "FluxLoraLoader": 11, "ModelClamp": 11, "LatentUpscaler": 11, "easy controlnetLoader": 11, "NormalizeAmplitude": 11, "FusionText": 11, "VAEDecodeTiled_TiledDiffusion": 10, "StableZero123_Conditioning": 10, "HyperTile": 10, "VHS_VAEEncodeBatched": 10, "LivePortraitLoadMediaPipeCropper": 10, "DownloadAndLoadChatGLM3": 10, "IPAdapterClipVisionEnhancer": 10, "CreateGradientFromCoords": 10, "Wildcard Processor": 10, "VHS_DuplicateLatents": 10, "WildcardEncode //Inspire": 10, "ADMD_TrainLora": 10, "ADMD_SaveLora": 10, "ADMD_ValidationSampler": 10, "FeatheredMask": 10, "IsMaskEmpty": 10, "Pack SDXL Tuple": 10, "CheckpointSave": 10, "PhotoMakerEncode": 10, "DiffControlNetLoaderAdvanced": 10, "SaveImageExtended": 10, "ImageSizeAndBatchSize": 10, "CR Simple Banner": 10, "LayerUtility: TextJoin": 10, "LayerMask: BiRefNetUltra": 10, "Blend Latents": 10, "AnimateDiffCombine": 10, "CfgScheduleHookProvider": 10, "VHS_LoadAudio": 10, "SEGSToImageList": 10, "SeargePromptText": 10, "CR Clip Input Switch": 10, "TextSplitByDelimiter": 10, "LayerMask: MaskInvert": 10, "FlipSigmas": 10, "SDXLPromptStylerbyOriginal": 10, "ShufflePreprocessor": 10, "LatentInterpolate": 10, "CR Random Shape Pattern": 10, "DownloadAndLoadCLIPVisionModel": 10, "DownloadAndLoadCLIPModel": 10, "BNK_CLIPTextEncodeSDXLAdvanced": 10, "ImpactValueReceiver": 10, "DownloadAndLoadDynamiCrafterModel": 10, "LayerUtility: ImageBlendAdvance V2": 10, "LayerMask: SegformerB2ClothesUltra": 10, "Width/Height Literal (Image Saver)": 10, "CR Integer Multiple": 10, "ImpactRemoteInt": 10, "String Literal (Image Saver)": 10, "AV_ControlNetEfficientStacker": 10, "easy pipeIn": 10, "SD_4XUpscale_Conditioning": 9, "FakeScribblePreprocessor": 9, "DetailerForEachPipe": 9, "DWPreprocessor_Provider_for_SEGS //Inspire": 9, "Image Padding": 9, "Save Image w/Metadata": 9, "MediaPipeFaceMeshDetectorProvider //Inspire": 9, "Image Style Filter": 9, "LoadAudio": 9, "RegionalConditioningColorMask //Inspire": 9, "ttN pipeLoader": 9, "JoinStrings": 9, "MaskDetailerPipe": 9, "PerturbedAttention": 9, "InspyrenetRembg": 9, "PhotoMakerEncodePlus": 9, "PixelKSampleUpscalerProviderPipe": 9, "MaskToSEGS_for_AnimateDiff": 9, "VideoTriangleCFGGuidance": 9, "MediaPipe-FaceMeshPreprocessor": 9, "ReActorMaskHelper": 9, "SDXLPromptStylerAdvanced": 9, "LoadImageListFromDir //Inspire": 9, "AnimeFace_SemSegPreprocessor": 9, "VHS_SelectEveryNthImage": 9, "Image Select Channel": 9, "ELLALoader": 9, "T5TextEncoderLoader #ELLA": 9, "ReActorFaceBoost": 9, "XY Plot": 9, "DepthAnything_V2": 9, "DownloadAndLoadDepthAnythingV2Model": 9, "ImpactConcatConditionings": 9, "easy imagePixelPerfect": 9, "PixArtCheckpointLoader": 9, "SwitchByIndex": 9, "SeargePreviewImage": 9, "Resolutions by Ratio (WLSH)": 9, "Image Crop Square Location": 9, "LayerUtility: QWenImage2Prompt": 9, "ColorBlend": 9, "FaceEmbedDistance": 9, "CosyVoiceNode": 9, "AutoCropFaces": 9, "ImageTextOutlined": 9, "IPAdapterModelHelper //Inspire": 9, "StringToInt": 9, "easy preSampling": 9, "ChatGPT compact _O": 9, "SDAnyConverter": 9, "ImpactValueSender": 9, "Image Canny Filter": 9, "SeargeIntegerPair": 9, "PortraitMaster": 9, "InspyrenetRembgAdvanced": 9, "BRIAAI Matting": 9, "ConditioningSetAreaPercentage": 9, "easy imageScaleDown": 9, "FadeMaskEdges": 9, "CR Seamless Checker": 9, "ImageSizeInfo": 9, "DownloadAndLoadKolorsModel": 9, "KolorsSampler": 9, "KolorsTextEncode": 9, "SolidColorRGB": 9, "LayerFilter: HDREffects": 9, "Load Text File": 9, "StableAudio_": 9, "easy imageColorMatch": 8, "AuraSR.AuraSRUpscaler": 8, "PreviewLatentAdvanced": 8, "M-LSDPreprocessor": 8, "UniFormer-SemSegPreprocessor": 8, "MiDaS-NormalMapPreprocessor": 8, "DZ_Face_Detailer": 8, "ADE_BatchedContextOptions": 8, "Model Switch (JPS)": 8, "LightSource": 8, "CR Text Blacklist": 8, "LayeredDiffusionDecodeRGBA": 8, "PowerPaint": 8, "PowerPaintCLIPLoader": 8, "Image Rotate": 8, "ImageGridComposite2x2": 8, "CR Prompt List": 8, "LoadVideo": 8, "PerpNegGuider": 8, "CR Apply Model Merge": 8, "CR Model Merge Stack": 8, "ToDetailerPipe": 8, "SEGSDetailer": 8, "CR Integer Range List": 8, "ReverseImageBatch": 8, "SD3NegativeConditioning+": 8, "FloatingVideo": 8, "SaveImageWithMetaData": 8, "ImpactDecomposeSEGS": 8, "ImpactFrom_SEG_ELT": 8, "LayerUtility: ColorImage": 8, "PixelKSampleHookCombine": 8, "DenoiseScheduleHookProvider": 8, "ChromaticAberration": 8, "CR Set Value On Boolean": 8, "ImpactMinMax": 8, "Unary Image Op": 8, "RemoveControlNet //Inspire": 8, "LayerMask: PersonMaskUltra": 8, "ImpactMakeTileSEGS": 8, "ImageQuantize": 8, "easy promptConcat": 8, "CR Image Pipe Out": 8, "LatentComposite": 8, "PrimereDynamicParser": 8, "DownloadAndLoadMimicMotionModel": 8, "MimicMotionSampler": 8, "Bounded Image Blend with Mask": 8, "LayerUtility: RestoreCropBox": 8, "OutlineMask": 8, "CreatePromptVariant": 8, "VHS_GetLatentCount": 8, "ImpactConvertDataType": 8, "RemapDepth": 8, "LatentCrop": 8, "CR Apply ControlNet": 8, "ImageLuminanceDetector": 8, "Save Images Mikey": 8, "LoraInfo": 8, "BlendModes": 8, "RetrieveBackendData //Inspire": 8, "easy controlnetLoaderADV": 8, "Joytag": 8, "Image Stitch": 8, "Any To Any": 8, "BNK_GetSigma": 8, "SDXLAspectRatio": 7, "SmoothMask": 7, "OneFormer-ADE20K-SemSegPreprocessor": 7, "LatentBatchSeedBehavior": 7, "CR Combine Prompt": 7, "SamplerDPMPP_2M_SDE": 7, "FrequencySeparation": 7, "LayerMask: MaskEdgeUltraDetail V2": 7, "Masks Subtract": 7, "PoseNode": 7, "Date Time Format": 7, "IPAdapterPromptScheduleFromWeightsStrategy": 7, "CR Simple Prompt List Keyframes": 7, "Image Flip": 7, "AlphaChanelAddByMask": 7, "MiDaS_DepthMap_Preprocessor_Provider_for_SEGS //Inspire": 7, "PrepareImageAndMaskForInpaint": 7, "ApplyPulidAdvanced": 7, "SubtractMaskForEach": 7, "Image High Pass Filter": 7, "Image Film Grain": 7, "CR Batch Images From List": 7, "LayeredDiffusionDecode": 7, "ImageBatchRepeatInterleaving": 7, "VRAM_Debug": 7, "ImpactEdit_SEG_ELT": 7, "ImpactAssembleSEGS": 7, "ImpactSEGSPicker": 7, "Image Batch": 7, "Image Monitor Effects Filter": 7, "T5v11Loader": 7, "LatentPixelScale": 7, "Canvas_Tab": 7, "ttN pipeKSamplerAdvanced": 7, "MiniCPM_VQA": 7, "Glow": 7, "ImageFilterGaussianBlur": 7, "KSamplerAdvanced (WLSH)": 7, "MeshGraphormer+ImpactDetector-DepthMapPreprocessor": 7, "Colored Image (mtb)": 7, "easy compare": 7, "easy imageSwitch": 7, "AV_ControlNetEfficientLoaderAdvanced": 7, "Float Literal (Image Saver)": 7, "ImageGenResolutionFromLatent": 7, "AV_VAELoader": 7, "ImageCombine": 7, "MimicMotionDecode": 7, "MimicMotionGetPoses": 7, "ToonCrafterDecode": 7, "Crop Image TargetSize (JPS)": 7, "ImpactSegsAndMask": 7, "CM_NumberUnaryOperation": 7, "CR Data Bus In": 7, "BboxDetectorCombined_v2": 7, "KepStringLiteral": 7, "InpaintStitch": 7, "PrimereAnyOutput": 7, "EmbeddingPrompt": 7, "YANC.ConcatStrings": 7, "Mask Dilate Region": 7, "MaskBatch+": 7, "🔹Photoshop ComfyUI Plugin": 7, "🔹SendTo Photoshop Plugin": 7, "RawTextCombine": 7, "Vae Decode (mtb)": 7, "BNK_NoisyLatentImage": 6, "VHS_VAEDecodeBatched": 6, "PortraitMasterSkinDetails": 6, "LoadImageFromUrl": 6, "ImageRepeat": 6, "BatchValueScheduleLatentInput": 6, "FL_SDUltimate_Slices": 6, "ImageComposite+": 6, "FacialPartColoringFromPoseKps": 6, "PhotoMakerLoaderPlus": 6, "LivePortraitNode": 6, "LivePortraitLoadFaceAlignmentCropper": 6, "DetailerForEachPipeForAnimateDiff": 6, "DenoiseSchedulerDetailerHookProvider": 6, "ADE_AnimateDiffUnload": 6, "SAMDetectorSegmented": 6, "ImageEffectsGrayscale": 6, "VHS_BatchManager": 6, "LayerMask: RemBgUltra": 6, "StringConstantMultiline": 6, "JDCN_SwapInputs": 6, "PointsEditor": 6, "ImageListToBatch+": 6, "IFRNet VFI": 6, "Joy_caption": 6, "Joy_caption_load": 6, "AnimateDiffSlidingWindowOptions": 6, "SDXL Resolutions (WLSH)": 6, "CR Color Bars": 6, "DrawText+": 6, "Int To Number (mtb)": 6, "easy promptList": 6, "NewLayer": 6, "MasksToMaskList": 6, "BLIPCaption": 6, "ReActorFaceSwapOpt": 6, "CR Image Panel": 6, "BatchCreativeInterpolation": 6, "SV3D_Conditioning": 6, "BilboXPhotoPrompt": 6, "CR Text Operation": 6, "LayerUtility: SaveImagePlus": 6, "CR Binary Pattern": 6, "CR String To Number": 6, "PrimereClearPrompt": 6, "BatchValueSchedule": 6, "Image Load": 6, "DiffusersGenerator": 6, "PreviewMask_": 6, "LoadCLIPSegModels+": 6, "ApplyCLIPSeg+": 6, "LayerUtility: String": 6, "AnyNodeLocal": 6, "Logic Boolean Primitive": 6, "LayerColor: AutoAdjustV2": 6, "SEGSSwitch": 6, "LayerMask: RmBgUltra V2": 6, "ComfyUIStyler": 6, "ChineseCLIPEncode": 6, "CatVTONWrapper": 6, "MeshGraphormerDepthMapPreprocessorProvider_for_SEGS //Inspire": 6, "EllaEncode": 6, "String Replace (mtb)": 6, "Conditioning Input Switch": 6, "MaskFromList+": 6, "ConditioningSetAreaStrength": 6, "Image Power Noise": 6, "Unary Mask Op": 6, "ConditioningClamp": 6, "Intrinsic_lora_sampling": 6, "Checkpoint Loader with Name (Image Saver)": 6, "Int Literal (Image Saver)": 6, "Number to Int": 6, "ImpactNegativeConditioningPlaceholder": 6, "MorphologicOperation": 6, "PreviewBridgeLatent": 6, "LayerUtility: ImageScaleRestore V2": 6, "LayerMask: MaskBoxDetect": 6, "IF_PromptMkr": 6, "easy a1111Loader": 6, "LoadPromptsFromFile //Inspire": 6, "UnzipPrompt //Inspire": 6, "ImpactDilateMaskInSEGS": 6, "Griptape Create: Rules": 6, "FlowBuilder": 6, "CR Text Cycler": 6, "DualCFGGuider": 5, "InstructPixToPixConditioning": 5, "VAEEncodeTiled_TiledDiffusion": 5, "EmptyLatentImagePresets": 5, "BinaryPreprocessor": 5, "Save Images No Display": 5, "Florence2ModelLoader": 5, "IPAdapterTiledBatch": 5, "PortraitMasterStylePose": 5, "PortraitMasterMakeup": 5, "PortraitMasterBaseCharacter": 5, "MaraScottUpscalerRefinerNode_v3": 5, "SUPIR_model_loader": 5, "PreviewPopup": 5, "ACN_SparseCtrlIndexMethodNode": 5, "GenerateNoise": 5, "Image Color Palette": 5, "CombineRegionalPrompts": 5, "PrepImagesForClipVisionFromPath": 5, "PhotoMakerInsightFaceLoader": 5, "LayerColor: Levels": 5, "Mix Color By Mask": 5, "GetKeyFrames": 5, "ttN text3BOX_3WAYconcat": 5, "PrimereCKPTLoader": 5, "PrimereSeed": 5, "SetEllaTimesteps": 5, "CR SDXL Base Prompt Encoder": 5, "CR Text List": 5, "LoraTagLoader": 5, "FS: Fit Size From Image": 5, "LivePortraitRetargeting": 5, "Range(Num Steps) - Float": 5, "Model Input Switch": 5, "ACN_ControlNet++InputNode": 5, "ACN_ControlNet++LoaderAdvanced": 5, "Number Input Switch": 5, "PencilSketch": 5, "LoadImagesToBatch": 5, "easy if": 5, "GridAnnotation": 5, "Simple String Combine (WLSH)": 5, "LatentBatch": 5, "0246.Merge": 5, "QWenVL_API_S_Zho": 5, "Image Pixelate": 5, "BLIPLoader": 5, "ImageRGBA2RGB": 5, "PromptGenerator": 5, "SplitSigmasDenoise": 5, "CircularVAEDecode": 5, "Integer Variable [n-suite]": 5, "String Variable [n-suite]": 5, "CR Text List To String": 5, "CR Random Multiline Values": 5, "preview_mask": 5, "AudioToFFTs": 5, "AV_ControlNetEfficientLoader": 5, "GlobalSampler //Inspire": 5, "TTPlanet_TileSimple_Preprocessor": 5, "CCSR_Upscale": 5, "CCSR_Model_Select": 5, "TrainDatasetAdd": 5, "SaveVideo [n-suite]": 5, "LayerColor: Exposure": 5, "Color Blend": 5, "BNK_SlerpLatent": 5, "MoondreamQuery": 5, "GetImageRangeFromBatch": 5, "ModelSamplingAuraFlow": 5, "CR Composite Text": 5, "PixArtResolutionSelect": 5, "LLavaSamplerAdvanced": 5, "TEEDPreprocessor": 5, "CivitAI_Checkpoint_Loader": 5, "ImageBatchGet": 5, "DSINE-NormalMapPreprocessor": 5, "ColorModEdges": 5, "ColorModPivot": 5, "ImpactWildcardEncode": 5, "HYDiTTextEncodeSimple": 5, "xy_Tiling_KSampler": 5, "ToImageList": 5, "easy humanSegmentation": 5, "LayerUtility: ImageBlend V2": 5, "LayerMask: CreateGradientMask": 5, "SimpleMath": 5, "easy imageToMask": 5, "AlphaChanelAsMask": 5, "EmbeddingPicker": 5, "CR Load Image List": 5, "Latent Switch (JPS)": 5, "Mask Batch to Mask": 5, "ModelMergeSubtract": 4, "VHS_LoadAudioUpload": 4, "SAMPreprocessor": 4, "PreviewAnimation": 4, "JDC_Contrast": 4, "SDXL Empty Latent Image": 4, "HDR Effects (SuperBeasts.AI)": 4, "LayerColor: AutoBrightness": 4, "TCDScheduler": 4, "NoiseInjectionDetailerHookProvider": 4, "InjectNoiseToLatent": 4, "CompositeImages_": 4, "ImageBatchSplitter //Inspire": 4, "BOPBTL_ScratchMask": 4, "BOPBTL_RestoreOldPhotos": 4, "BOPBTL_LoadScratchMaskModel": 4, "BOPBTL_LoadRestoreOldPhotosModel": 4, "ComfyUI_Image_Round__ImageRoundAdvanced": 4, "ComfyUI_Image_Round__ImageCropAdvanced": 4, "Hina.PoseEditor3D": 4, "FloatRange //Inspire": 4, "LayerUtility: GetColorTone": 4, "RegionalSampler": 4, "PreViewVideo": 4, "TomePatchModel": 4, "Int2String": 4, "YouTubeVideoLoader": 4, "Automatic CFG - Preset Loader": 4, "Image Compare (mtb)": 4, "load_persona": 4, "ImageCompositeAbsoluteByContainer": 4, "GMFSS Fortuna VFI": 4, "BNK_CutoffBasePrompt": 4, "ImpactSEGSLabelFilter": 4, "PrimerePreviewImage": 4, "RAUNet": 4, "CR Save Text To File": 4, "Mix Images By Mask": 4, "VHS_VideoInfoLoaded": 4, "SimpleMathInt+": 4, "TextConcat": 4, "get_string": 4, "brushnet_model_loader": 4, "brushnet_sampler": 4, "IPAdapterClipVisionEnhancerBatch": 4, "VHS_SplitMasks": 4, "smZ Settings": 4, "JDCN_VHSFileMover": 4, "LayerUtility: TextBox": 4, "ComfyWordCloud": 4, "LayerUtility: TextImage": 4, "ImageColorToMask": 4, "XY Input: CFG Scale": 4, "seed _O": 4, "Solarize": 4, "LayerUtility: SD3NegativeConditioning": 4, "XY Input: Sampler/Scheduler": 4, "If ANY return A else B-🔬": 4, "FaceBoundingBox": 4, "reBatchImage": 4, "VHS_AudioToVHSAudio": 4, "Apply Whisper": 4, "Any List": 4, "JWImageResizeByShorterSide": 4, "ttN float": 4, "ImagesGridByRows": 4, "Grayscale Image (WLSH)": 4, "MergeLayers": 4, "LayerMask: MaskGradient": 4, "EllaApply": 4, "KSamplerRAVE": 4, "Remove Image Background (abg)": 4, "ReActorOptions": 4, "CLIP Temperature": 4, "BlurMaskFast": 4, "LoadRandomImage": 4, "PGSD3LatentGenerator": 4, "RandomIntegerNode": 4, "DisableNoise": 4, "InjectLatentNoise+": 4, "STMFNet VFI": 4, "FromListGet1Image": 4, "Kep_RepeatList": 4, "CR Intertwine Lists": 4, "LayerUtility: PromptTagger": 4, "Latent Input Switch": 4, "CLIP Positive-Negative (WLSH)": 4, "PrimerePromptOrganizer": 4, "PrimereVisualStyle": 4, "PrimereVisualCKPT": 4, "PrimerePromptSwitch": 4, "PrimereEmbeddingHandler": 4, "PrimereNetworkTagLoader": 4, "PrimereResolution": 4, "PrimereLatentNoise": 4, "PrimereCLIPEncoder": 4, "PrimereKSampler": 4, "segformer_clothes": 4, "DiffusersScheduler": 4, "DynamiCrafterModelLoader": 4, "DiffusersCompelPromptEmbedding": 4, "DiffusersPipeline": 4, "Style Conditioner": 4, "XY Input: Clip Skip": 4, "ZeST: Grayout Subject": 4, "LayerMask: SegmentAnythingUltra": 4, "StringConcatenate": 4, "ConditioningMultiCombine": 4, "CM_FloatUnaryOperation": 4, "OneButtonFlufferize": 4, "SeargeIntegerMath": 4, "ToIPAdapterPipe //Inspire": 4, "ApplyRegionalIPAdapters //Inspire": 4, "FileNamePrefixDateDirFirst": 4, "ImageFilterBlur": 4, "Seed Generator (Image Saver)": 4, "CLIPTextEncodeSDXL+": 4, "Empty Latent by Ratio (WLSH)": 4, "LayerUtility: Florence2Image2Prompt": 4, "LayerMask: LoadFlorence2Model": 4, "PromptComposerTextSingle": 4, "LatentInterposer": 4, "FLUXResolutions": 4, "PrimereRefinerPrompt": 4, "PrimereImageSegments": 4, "PrimereAnyDetailer": 4, "CLIPTextEncodeWithWeight //Inspire": 4, "TagsSelector": 4, "LoraLoaderStackedAdvanced": 4, "Cfg Literal (Image Saver)": 4, "VaeClamp": 4, "easy prompt": 4, "Remap": 4, "FromListGetImages": 4, "ADE_AnimateDiffLoaderV1Advanced": 4, "ImageColorMatch+": 4, "GLIGENTextBoxApply": 4, "DynamiCrafterI2V": 4, "SaveImageAndMetadata_": 4, "SimpleMathSlider+": 4, "TagsFormater": 4, "CR Value": 4, "BiRefNet": 4, "CR Text Length": 4, "LoadImageWithAlpha": 4, "EmptyLatentAudio": 4, "VAEDecodeAudio": 4, "PromptControlSimple": 4, "JjkText": 4, "ttN pipeIN": 4, "LayerStyle: Stroke V2": 4, "DiffusersControlnetUnit": 4, "MicrosoftSpeech_TTS": 4, "Play Sound": 4, "ImageGridComposite3x3": 4, "IF_DisplayText": 4, "CR Select ISO Size": 4, "IPAdapterUnifiedLoaderCommunity": 4, "FrameMakerBatch": 4, "Number to Float": 4, "ConcatConditionEllaEmbeds": 4, "LayerColor: Color of Shadow & Highlight": 4, "CalculateFrameOffset": 4, "Masks Combine Regions": 4, "YANC.MultilineString": 4, "LayerUtility: GetColorToneV2": 4, "LayerUtility: ExtendCanvasV2": 4, "JDCN_SplitString": 4, "ModelMergeAdd": 3, "Add Magic Clothing Attention": 3, "Load Magic Clothing Model": 3, "Image Resize To Width": 3, "LayerMask: ImageToMask": 3, "Video Dump Frames": 3, "easy applyFooocusInpaint": 3, "CreateShapeImageOnPath": 3, "UltraPixelLoad": 3, "UltraPixelProcess": 3, "ArgosTranslateCLIPTextEncodeNode": 3, "LoraLoaderStackedVanilla": 3, "KRestartSamplerAdv": 3, "LoraStackLoader_PoP": 3, "LatentSender": 3, "LatentReceiver": 3, "LoadImage //Inspire": 3, "KfEvaluateCurveAtT": 3, "KfCurveFromString": 3, "WebcamCaptureCV2": 3, "Screencap_mss": 3, "LoadChatGLM3": 3, "Tensor Batch to Image": 3, "OneButtonPreset": 3, "Linear Camera Zoom [DVB]": 3, "CutForInpaint": 3, "EllaTextEncode": 3, "LayerMask: Shadow & Highlight Mask": 3, "ImageApplyLUT+": 3, "GPT4VisionNode": 3, "VHS_VideoInfoSource": 3, "HD UltimateSDUpscale": 3, "LayerColor: AutoAdjust": 3, "LayerColor: ColorTemperature": 3, "load_file": 3, "advance_ebd_tool": 3, "FluxSamplerParams+": 3, "CreateShapeMaskOnPath": 3, "ImagePadForOutpaintMasked": 3, "Tiled Upscaler Script": 3, "VHS_SplitLatents": 3, "ImageSmartSharpen+": 3, "GetMaskSizeAndCount": 3, "easy sliderControl": 3, "Blur (mtb)": 3, "LLMLoader": 3, "ttN seed": 3, "Big Image Switch [Dream]": 3, "EulerLightingNode": 3, "PreViewAudio": 3, "Image Shadows and Highlights": 3, "ColorTint": 3, "Parabolize": 3, "Quantize": 3, "SineWave": 3, "AsciiArt": 3, "Aegisflow controlnet preprocessor bus": 3, "PromptBuilder //Inspire": 3, "DynamiCrafterLoader": 3, "CLIPSegDetectorProvider": 3, "XY Input: Seeds++ Batch": 3, "Compare-🔬": 3, "TextImage": 3, "PromptExtractor //Inspire": 3, "String Input": 3, "FaceCropInfo": 3, "GetImage_(Width&Height) _O": 3, "CreateGradientMask": 3, "SeargeSDXLSampler": 3, "ImagesGridByColumns": 3, "Griptape Display: Text": 3, "PipelineLoader": 3, "IDM-VTON": 3, "CogVideoImageEncode": 3, "TransparentImage": 3, "MediaPipeFaceMeshToSEGS": 3, "Eden_Compare": 3, "LuminaT2ISampler": 3, "LuminaGemmaTextEncode": 3, "DownloadAndLoadGemmaModel": 3, "DownloadAndLoadLuminaModel": 3, "color2RGB": 3, "CropImage_AS": 3, "Mask By Text": 3, "ConditioningSetMaskAndCombine": 3, "ImpactIPAdapterApplySEGS": 3, "CLIP_Interrogator": 3, "LayerMask: MaskEdgeUltraDetail": 3, "CR VAE Decode": 3, "CR Halftone Grid": 3, "AdainImage": 3, "StringMergerNode": 3, "DownloadAndLoadFlorence2Lora": 3, "TileMerge": 3, "TileCalc": 3, "TileSplit": 3, "Batch Unsampler": 3, "Iterative Mixing KSampler Advanced": 3, "PortraitMaster_中文版": 3, "ArithmeticBlend": 3, "SamplerTCD EulerA": 3, "ToonCrafterNode": 3, "ToStringList": 3, "easy globalSeed": 3, "CR Image Pipe In": 3, "Masks Combine Batch": 3, "AnyNode": 3, "PrimereModelConceptSelector": 3, "PrimereConceptDataTuple": 3, "ReActorBuildFaceModel": 3, "JjkConcat": 3, "LayerUtility: CropByMask": 3, "SDXLPromptStylerbySteamPunkRealism": 3, "Sam2VideoSegmentationAddPoints": 3, "Mask Contour": 3, "MakeBasicPipe //Inspire": 3, "InitFluxLoRATraining": 3, "OptimizerConfig": 3, "FluxTrainValidationSettings": 3, "FluxTrainModelSelect": 3, "FluxTrainEnd": 3, "TrainDatasetGeneralConfig": 3, "ImageConcatFromBatch": 3, "LoadVideo [n-suite]": 3, "CR Image Size": 3, "JWImageContrast": 3, "Miaoshouai_Tagger": 3, "JWMaskResize": 3, "Image Edge Detection Filter": 3, "ttN imageREMBG": 3, "ImpactQueueTriggerCountdown": 3, "PreviewLatent": 3, "Suggester": 3, "AttentionCoupleRegion": 3, "BatchUncropAdvanced": 3, "PrimereModelKeyword": 3, "PrimereAestheticCKPTScorer": 3, "Sampler Selector (Image Saver)": 3, "Scheduler Selector (Image Saver)": 3, "ImageIntensityDetector": 3, "SemSegPreprocessor": 3, "Image Median Filter": 3, "SaveImageOpenEXR": 3, "ValueSchedule": 3, "EG_ZY_WBK": 3, "SeargeFloatConstant": 3, "SeargeSDXLPromptEncoder": 3, "CR Switch Model and CLIP": 3, "SetMetadataForSaveVideo [n-suite]": 3, "HYDiTCheckpointLoader": 3, "HYDiTTextEncoderLoader": 3, "ImageTransformCropAbsolute": 3, "JWImageFlip": 3, "GLIGENLoader": 3, "KSamplerPipe //Inspire": 3, "Fooocus_KSampler": 3, "LatentRotate": 3, "Attention couple": 3, "LayerMask: YoloV8Detect": 3, "CacheBackendData //Inspire": 3, "SimpleMathFloat+": 3, "easy preSamplingAdvanced": 3, "easy imageSave": 3, "TCDModelSamplingDiscrete": 3, "TaraPrompter": 3, "TaraApiKeyLoader": 3, "IPAdapterWeightsFromStrategy": 3, "OmostRenderCanvasConditioningNode": 3, "OmostLayoutCondNode": 3, "Mask Erode Region": 3, "JWImageExtractFromBatch": 3, "LatentMultiply": 3, "LayerMask: MaskStroke": 3, "SeedSelector": 3, "OneButtonArtify": 3, "ConditionText": 3, "MultiLoraLoader-70bf3d77": 3, "LoraTextExtractor-b1f83aa2": 3, "ADE_AnimateDiffModelSettings_Release": 3, "ImageMerger": 3, "BatchCropFromMask": 3, "LayerUtility: CropBoxResolve": 3, "Image Tile Offset (mtb)": 3, "DiffusersControlnetLoader": 3, "segformer_b2_clothes": 3, "Power-Law Noise (PPF Noise)": 3, "KepRotateImage": 3, "LatentFlip": 3, "TransientAmplitudeBasic": 3, "Gemini_15P_API_S_Advance_Zho": 3, "Text String Truncate": 3, "MuseTalkRun": 3, "CR Trigger": 3, "Text Compare": 3, "Generation Parameter Output": 3, "LeRes_DepthMap_Preprocessor_Provider_for_SEGS //Inspire": 3, "PromptExpansion": 3, "ACN_ReferencePreprocessor": 3, "FaceParsingResultsParser(FaceParsing)": 3, "easy instantIDApply": 3, "> Text": 3, "> Save Image": 3, "CXH_Min2_6_prompt_Run": 3, "CXH_HG_Model_Load": 3, "LayerColor: ColorBalance": 3, "Crop (mtb)": 3, "Kep_JoinListAny": 3, "mxSlider": 3, "easy preSamplingDynamicCFG": 3, "Latent Adjustment (PPF Noise)": 3, "SelectNthMask //Inspire": 3, "easy imageDetailTransfer": 3, "diffusers_model_loader": 3, "LoadICLightUnetDiffusers": 3, "iclight_diffusers_sampler": 3, "easy string": 3, "ConcatenateText": 3, "Eden_Int": 3, "FL_ColorPicker": 3, "ttN pipeOUT": 3, "LoraListNames": 3, "AddAlpha": 3, "IPAdapterFaceIDKolors": 2, "Image Voronoi Noise Filter": 2, "EmptyLatentImageScaleBy": 2, "easy ckptNames": 2, "GetImagesFromBatchIndexed": 2, "AnyAspectRatio": 2, "CLIP Vector Sculptor text encode": 2, "JDC_RandNoise": 2, "LayerFilter: SoftLight": 2, "DebugTensorShape+": 2, "ETN_CropImage": 2, "RGB_Image_Zho": 2, "LLLiteLoader": 2, "AdvancedLivePortrait": 2, "CaptureWebcam": 2, "LayerUtility: GradientImage V2": 2, "ADMD_ValidationSettings": 2, "ADMD_InitializeTraining": 2, "ADMD_AdditionalModelSelect": 2, "ADMD_CheckpointLoader": 2, "Flatten Colors": 2, "MimicMotionNode": 2, "DeepCache": 2, "BizyAir_MinusZoneChatGLM3TextEncode": 2, "FluxPromptGenerator": 2, "ReferenceOnlySimple": 2, "RegionalPromptColorMask //Inspire": 2, "CLIP Positive-Negative XL w/Text (WLSH)": 2, "AV_CheckpointMerge": 2, "ttN multiModelMerge": 2, "SV3D_BatchSchedule": 2, "Unwrap Frame Set [DVB]": 2, "Sine Camera Zoom [DVB]": 2, "Linear Camera Pan [DVB]": 2, "Create Frame Set [DVB]": 2, "BNK_CutoffRegionsToConditioning_ADV": 2, "Image Mix RGB Channels": 2, "Get Image Size (JPS)": 2, "ImageColorTransfer": 2, "Seed_": 2, "CR Simple Prompt Scheduler": 2, "LayerFilter: SkinBeauty": 2, "ImageComposite_Zho": 2, "CR Font File List": 2, "flux_persona": 2, "json_get_value": 2, "extra_parameters": 2, "RescaleClassifierFreeGuidanceTest": 2, "ApplyMSWMSAAttentionSimple": 2, "OllamaImageDescriber": 2, "Text to Console": 2, "Logic NOT": 2, "JDCN_BoolInt": 2, "Demofusion From Single File": 2, "ConditioningBlend": 2, "ImageFilterBilateralBlur": 2, "ImageEffectsLensVignette": 2, "EGIPAdapter_Mad_Assistant": 2, "RGB_Picker": 2, "easy textSwitch": 2, "CFGGuider": 2, "Save Image With Prompt Data": 2, "SamplerEulerCFGpp": 2, "PlayMusic": 2, "Send_To_Editor": 2, "LivePortraitVideoNode": 2, "ImageNoiseGaussian": 2, "FaceFixerOpenCV": 2, "Any Converter": 2, "comfyui-easy-padding": 2, "Load CheckPoint DragNUWA": 2, "DragNUWA Run": 2, "ImageEffectsLensBokeh": 2, "chaosaiart_TextCLIPEncode": 2, "CR ControlNet Input Switch": 2, "Calculation [Dream]": 2, "CXH_JoyTag": 2, "SizeSelector": 2, "int _O": 2, "SmoothVideo": 2, "ImageTransformCropCorners": 2, "XY Grid Helper": 2, "SeargePromptCombiner": 2, "Griptape Create: Agent": 2, "LayerFilter: Sharp & Soft": 2, "0246.Hub": 2, "Image Adaptive Crop With Mask": 2, "CombineClipEllaEmbeds": 2, "ApplyRAUNet": 2, "ApplyMSWMSAAttention": 2, "AddMetaData": 2, "Moondream Interrogator": 2, "ImageContainer": 2, "ImageSegmentation": 2, "ImpactSchedulerAdapter": 2, "BNK_AddCLIPSDXLRParams": 2, "GradientPatchModelAddDownscale": 2, "MovementsImage_Zho": 2, "ttN int": 2, "AnyListCartesianProduct": 2, "FromListGet1Latent": 2, "ImageText": 2, "ScheduleToModel": 2, "ScheduleToCond": 2, "PromptToSchedule": 2, "Int to String": 2, "Prompt Multiple Styles Selector": 2, "SpeechRecognition": 2, "NDI_LoadImage": 2, "NDI_SendImage": 2, "CR Feathered Border": 2, "ImageCropByAlpha": 2, "StableAudioPrompt": 2, "segformer_agnostic": 2, "stabel_vition": 2, "Latent Upscale by Factor (WAS)": 2, "YANC.FormatDatetimeString": 2, "StringListToString": 2, "Fetch widget value": 2, "SDXLPromptStylerbyCyberpunkSurrealism": 2, "SDXLPromptbyRomanticNationalismArt": 2, "SDXLPromptStylerbyQuantumRealism": 2, "SDXLPromptbyIrishFolkArt": 2, "SDXLPromptbyVikingArt": 2, "SDXLPromptbyCelticArt": 2, "SDXLPromptbyFashionArt": 2, "SDXLPromptStylerbyWyvern": 2, "SDXLPromptStylerbyTheme": 2, "SDXLPromptbySportsArt": 2, "SDXLPromptbyContemporaryNordicArt": 2, "Sam2VideoSegmentation": 2, "UploadToHuggingFace": 2, "LayerUtility: ImageTaggerSave": 2, "ScaledSoftMaskedUniversalWeights": 2, "RestoreDetail": 2, "SeargeFloatMath": 2, "Float Input [Dream]": 2, "ControlNextGetPoses": 2, "MoonDream": 2, "CM_IntToBool": 2, "CM_BoolToInt": 2, "ThresholdMask": 2, "easy imageInterrogator": 2, "WrapText": 2, "EG_WB_KSH": 2, "Image Resize To Height": 2, "LayerUtility: BooleanOperator": 2, "Miaoshouai_Flux_CLIPTextEncode": 2, "sdBxb": 2, "KRestartSamplerSimple": 2, "BitwiseAndMask": 2, "easy imageBatchToImageList": 2, "TTP_condsetarea_merge": 2, "TTP_condtobatch": 2, "TTP_CoordinateSplitter": 2, "TTP_Image_Tile_Batch": 2, "TTP_Tile_image_size": 2, "Pick From Batch (mtb)": 2, "BNK_TiledKSamplerAdvanced": 2, "easy pulIDApply": 2, "promptComposerTextMultiple": 2, "ImageDrawEllipse": 2, "ModelMergeFlux1": 2, "EGIPAdapter_Mad_AssistantV2": 2, "Scribble_PiDiNet_Preprocessor": 2, "AttentionCouple": 2, "ADE_ConditioningSetMaskAndCombine": 2, "ADE_RegisterLoraHook": 2, "Image Text Overlay": 2, "Create Grid Image from Batch": 2, "SUPIR_tiles": 2, "BatchCropFromMaskAdvanced": 2, "Mask Threshold Region": 2, "SeamlessTile": 2, "ReencodeLatent": 2, "AspectRatioSelector": 2, "TextTransformer": 2, "PM_RetinaFace": 2, "PrimereVAE": 2, "PrimereVisualEmbedding": 2, "PrimereVisualLORA": 2, "PrimereVisualLYCORIS": 2, "CR Simple Text Panel": 2, "CustomSigmas": 2, "ImageLoader": 2, "DiffusionEdge_Preprocessor": 2, "Depth_fm": 2, "EG_TX_CCHQ": 2, "ADE_AnimateDiffModelSettingsAdvancedAttnStrengths": 2, "CR Simple List": 2, "StableCascade_SuperResolutionControlnet": 2, "StableCascade_CheckpointLoader //Inspire": 2, "CLIPTextEncodeHunyuanDiT": 2, "TTPlanet_Tile_Preprocessor_Simple": 2, "SeargeIntegerConstant": 2, "CDL.OpenPoseEditorPlus": 2, "BatchStringSchedule": 2, "ImageScaleFactor _O": 2, "YARS": 2, "MimicBrushNode": 2, "DreamViewer": 2, "SampleColorHSV": 2, "NaiveAutoKMeansColor": 2, "InRange (hsv)": 2, "BuildColorRangeHSV (hsv)": 2, "easy ipadapterStyleComposition": 2, "OffsetImage": 2, "LuminaDiffusersNode": 2, "easy imageConcat": 2, "HfImageToRGB": 2, "MaskExpansion": 2, "CR Batch Process Switch": 2, "Mask Ops": 2, "LayerUtility: HLFrequencyDetailRestore": 2, "SDXL Quick Empty Latent (WLSH)": 2, "DynamiCrafterProcessor": 2, "IF_ImagePrompt": 2, "Load Face Analysis Model (mtb)": 2, "Load Face Swap Model (mtb)": 2, "Face Swap (mtb)": 2, "CR Random RGB Gradient": 2, "LayerUtility: BooleanOperatorV2": 2, "ConditioningSetMaskAndCombine4": 2, "BizyAirSiliconCloudLLMAPI": 2, "ttN pipeLoader_v2": 2, "Latent Size to Number": 2, "MS kosmos-2 Interrogator": 2, "Text Shuffle": 2, "ProPainterInpaint": 2, "SimpleMathDual+": 2, "easy hiresFix": 2, "HypernetworkLoader": 2, "LoraTagsOnly": 2, "ImageSimpleResize": 2, "LLM_local_loader": 2, "LLM_local": 2, "Stablezero123": 2, "SDZero ImageSplit": 2, "Image to Latent Mask": 2, "MaraScottUpscalerRefinerNode_v2": 2, "BaseModel_Loader_local": 2, "PhotoMakerAdapter_Loader_fromhub": 2, "Ref_Image_Preprocessing": 2, "NEW_PhotoMaker_Generation": 2, "LoRALoader": 2, "Prompt_Styler": 2, "PolyexponentialScheduler": 2, "LayerStyle: ColorOverlay V2": 2, "PromptComposerEffect": 2, "QRCodeGenerator": 2, "INPAINT_ExpandMask": 2, "LayerUtility: LayerMaskTransform": 2, "LayerMask: PixelSpread": 2, "LayerMask: MaskByColor": 2, "Image Dragan Photography Filter": 2, "Text List Concatenate": 2, "CivitaiLoraLoaderStacked": 2, "ImageBatchRemove": 2, "OneButtonSuperPrompt": 2, "Fans Prompt Styler Negative": 2, "StringMlStaticPrimitive": 2, "Text Parse A1111 Embeddings": 2, "FaceWarp": 2, "CLIPTextEncode (NSP)": 2, "KSamplerCacheable": 2, "UltimateVideoLoader": 2, "VHS_DuplicateMasks": 2, "TTPlanet_TileGF_Preprocessor": 2, "ImageTransformResizeClip": 2, "Color Transfer": 2, "ImageFilterGaussianBlurAdvanced": 2, "Blend Latents (PPF Noise)": 2, "EG_TX_SFBLS": 2, "Qwen2_ModelLoader_Zho": 2, "Qwen2_Zho": 2, "PreviewDetailerHookProvider": 2, "Pixel Deflicker - Experimental (SuperBeasts.AI)": 2, "ImpactSimpleDetectorSEGSPipe": 2, "DetailerPipeToBasicPipe": 2, "ClipAmplitude": 2, "OverlayInpaintedImage": 2, "OverlayInpaintedLatent": 2, "OmostDenseDiffusionLayoutNode": 2, "IF_SaveText": 2, "VHS_FILENAMES_STRING_MuseTalk": 2, "MuseTalkCupAudio": 2, "CR Set Value On Binary": 2, "CR Radial Gradient": 2, "KeyframeInterpolationPart": 2, "TripoSRViewer": 2, "TripoSRSampler": 2, "TripoSRModelLoader": 2, "MixNoiseNode": 2, "LamaRemoverIMG": 2, "FL_ImageNotes": 2, "ACN_ReferenceControlNet": 2, "FaceParsingProcessorLoader(FaceParsing)": 2, "FaceParsingModelLoader(FaceParsing)": 2, "FaceParse(FaceParsing)": 2, "FL_ImageRandomizer": 2, "FrameMaker": 2, "UformGen2QwenNode": 2, "BNK_CutoffRegionsToConditioning": 2, "CXH_DownloadAndLoadFlorence2Model": 2, "CXH_Florence2Run": 2, "ShowImages": 2, "LoraLoaderStackedWithPreviews": 2, "Make Image Batch": 2, "CLIPSeg Batch Masking": 2, "CR Index Multiply": 2, "ConcatConditioningsWithMultiplier //Inspire": 2, "SaveAudio": 2, "Image Perlin Noise": 2, "IntStaticPrimitive": 2, "CR Random LoRA Stack": 2, "Get Date Time String (JPS)": 2, "BackgroundScaler": 2, "JDC_ResizeFactor": 2, "easy pipeEdit": 2, "LayeredDiffusionDiffApply": 2, "DynamicDelayProcessor": 2, "FluxSampler": 2, "IF_ChatPrompt": 2, "LayerStyle: OuterGlow V2": 2, "ToDetailerPipeSDXL": 2, "PixArtT5FromSD3CLIP": 2, "MasaCtrlConcatImage": 2, "PromptNode": 2, "EndQueue": 2, "RawTextEncode": 2, "Text_Image_Frame_Zho": 2, "CR Checker Pattern": 2, "CR Starburst Colors": 2, "ColorDictionary": 2, "Color Clip (advanced)": 2, "FindComplementaryColor": 2, "BLVAEEncode": 2, "LDSRUpscaler": 2, "float _O": 2, "SALVTON_Apply": 1, "D_DreamTalk": 1, "ImageAddMulti": 1, "Boolean To Text": 1, "CheckpointLoaderSimpleExtended": 1, "JWImageResizeToSquare": 1, "CleanFileNameNode": 1, "Iterative Mixing KSampler": 1, "RelightSimple": 1, "AV_LoraLoader": 1, "easy imagesSplitImage": 1, "CR Vignette Filter": 1, "Fooocus_KSamplerAdvanced": 1, "RegionalCFG //Inspire": 1, "ColorMaskToDepthMask //Inspire": 1, "Checkpoint Loader Simple Mikey": 1, "WildcardAndLoraSyntaxProcessor": 1, "Batch Shape (mtb)": 1, "HyperSDXL1StepUnetScheduler": 1, "Batch Make (mtb)": 1, "LoadImagesFromPath": 1, "VAE Switch (JPS)": 1, "KG_neo_toolkit_developer": 1, "Image fDOF Filter": 1, "pipe-util-to-basic-pipe": 1, "Ood_CXH": 1, "BNK_AddCLIPSDXLParams": 1, "LDSRModelLoader": 1, "LDSRUpscale": 1, "APISR_upscale": 1, "CombineSegMasks": 1, "PrimereCKPT": 1, "PrimereLORA": 1, "ImageTransformResizeAbsolute": 1, "ModelDownloader": 1, "LoRADownloader": 1, "ella_model_loader": 1, "ella_t5_embeds": 1, "ella_sampler": 1, "Save Image If True": 1, "translate_persona": 1, "classify_persona": 1, "custom_persona": 1, "classify_function": 1, "CR Random Multiline Colors": 1, "CR Multiline Text": 1, "XY Input: Manual XY Entry": 1, "AnimateDiffLoaderV1": 1, "RemapRange": 1, "ONNXDetectorProvider": 1, "ONNXDetectorSEGS": 1, "SimpleMathBoolean+": 1, "SimpleCondition+": 1, "Mask Crop Dominant Region": 1, "ImageTransformCropRelative": 1, "ImageEffectsLensZoomBurst": 1, "ImpactLatentInfo": 1, "ModelPassThrough": 1, "Aligned Scheduler": 1, "Multiply sigmas": 1, "SaveAudioNode": 1, "ChatMusician": 1, "LoadWebcamImage": 1, "LoadImagePath": 1, "HalloNode": 1, "UVR5_Node": 1, "LoadAudioPath": 1, "Unique3DRun - MVPrediction": 1, "Unique3DLoadPipeline": 1, "Unique3DRun - Geo Reconstruct": 1, "Pixelize": 1, "SigmoidCorrection": 1, "Apply Instagram Filter": 1, "GlitchThis Effect": 1, "DynamiCrafter Simple": 1, "PDFToImage": 1, "DocumentLoader": 1, "TextChunker": 1, "DragNUWAImageCanvas": 1, "ImageEffectsLensChromaticAberration": 1, "Image Save with Prompt/Info (WLSH)": 1, "ImpactIsNotEmptySEGS": 1, "ImpactSEGSClassify": 1, "ImpactHFTransformersClassifierProvider": 1, "CLIPTextEncodeAdvancedNSuite [n-suite]": 1, "AudioSeparation": 1, "AudioCrop": 1, "OllamaSaveContext": 1, "Griptape Run: Image Description": 1, "Griptape Convert: Text to CLIP Encode": 1, "0246.CastReroute": 1, "JagsClipseg": 1, "Lora Input Switch": 1, "AlphaChanelAdd": 1, "llava": 1, "StableMakeup_Sampler": 1, "StableMakeup_LoadModel": 1, "AnimeLineArt_Preprocessor_Provider_for_SEGS //Inspire": 1, "AIraster": 1, "MMDetDetectorProvider": 1, "Comfy Couple": 1, "OnCompleteEmailMe": 1, "ID_Prompt_Styler": 1, "ArtistsImage_Zho": 1, "XY Input: Steps": 1, "Cozy Pose Body Reference": 1, "Cozy Sampler Options": 1, "SentenceMixerNode": 1, "ImageToMask_AS": 1, "HSVThresholdMask": 1, "AdaptiveGuidance": 1, "OpenSoraPlanLoader": 1, "OpenSoraPlanDecode": 1, "OpenSoraPlanSample": 1, "LineArt_Preprocessor_Provider_for_SEGS //Inspire": 1, "FloodGate": 1, "easy imageToBase64": 1, "StringConstant": 1, "ImageBatchToList": 1, "ExtendLatentList": 1, "LoadFlorence2Model": 1, "Florence2": 1, "CR Set Value on String": 1, "tool_combine": 1, "StableAudioLoadModel": 1, "StableAudioConditioning": 1, "StableAudioSampler": 1, "MagicAnimateModelLoader": 1, "MagicAnimate": 1, "ImpactInversedSwitch": 1, "Swap Color Mode": 1, "ImageTextMultilineOutlined": 1, "easy portraitMaster": 1, "easy kolorsLoader": 1, "CR Module Output": 1, "text-util-prompt-join": 1, "ImpactSEGSToMaskBatch": 1, "Float to String": 1, "BlurImageFast": 1, "SegmDetectorCombined_v2": 1, "TGateApplySimple": 1, "Load BiseNet": 1, "Segment Face": 1, "CR XY Save Grid Image": 1, "CR XY From Folder": 1, "CR XY List": 1, "FrameInterpolator [n-suite]": 1, "ApplyAdvancedFluxIPAdapter": 1, "ImagePaste": 1, "SeedExplorer //Inspire": 1, "SaveSVG": 1, "ConvertRasterToVector": 1, "ControlNextDiffusersScheduler": 1, "DownloadAndLoadControlNeXt": 1, "ControlNextSampler": 1, "ControlNextDecode": 1, "CM_IntBinaryCondition": 1, "LayerUtility: QueueStop": 1, "ComfyUISaveAs": 1, "Empty Latent by Size (WLSH)": 1, "DepthViewer": 1, "Image Switch (JPS)": 1, "Aspect Ratios Node": 1, "string_util_StrEqual": 1, "ScheduledCFGGuider //Inspire": 1, "CR Math Operation": 1, "Combine and Paste": 1, "Inpaint Segments": 1, "LatentDuplicator": 1, "UltimateSDUpscaleCustomSample": 1, "easy imageInsetCrop": 1, "BLEND (JOV) ⚗️": 1, "ADJUST (JOV) 🕸️": 1, "NX_PromptStyler": 1, "MoondreamQueryCaptions": 1, "ControlNextSVDApply": 1, "AttentionCoupleRegions": 1, "MaskFlip+": 1, "BatchPromptScheduleEncodeSDXL": 1, "BatchCLIPSeg": 1, "PromptComposerCustomLists": 1, "Int To Bool (mtb)": 1, "Lora": 1, "Checkpoint": 1, "Image Resize Factor (mtb)": 1, "PrimereEmotionsStyles": 1, "PrimereUpscaleModel": 1, "PrimereMidjourneyStyles": 1, "PrimereStylePile": 1, "PrimereMetaCollector": 1, "PrimereMetaHandler": 1, "PrimereMetaDistributor": 1, "PrimereMetaDistributorStage2": 1, "PrimereVAELoader": 1, "PrimereEmbeddingKeywordMerger": 1, "PrimereLoraKeywordMerger": 1, "PrimereVisualHypernetwork": 1, "PrimereLycorisKeywordMerger": 1, "PrimereResolutionMultiplierMPX": 1, "PrimereMetaSave": 1, "PrimereTextOutput": 1, "easy imageScaleDownBy": 1, "WebcamCapture": 1, "ImageEnhanceDifference+": 1, "DynamicThresholdingSimple": 1, "Simple Wildcards": 1, "DisplayText": 1, "MultiplePathsInput": 1, "Qwen2_VQA": 1, "Load Image From Url (mtb)": 1, "geowizard_model_loader": 1, "geowizard_sampler": 1, "AdaptiveCannyDetector_PoP": 1, "ttN KSampler_v2": 1, "3DImage": 1, "IFUnet VFI": 1, "LoraLoaderBlockWeight //Inspire": 1, "LoraBlockInfo //Inspire": 1, "HYDiTTextEncode": 1, "ImageTransformRotate": 1, "ImageFilterMedianBlur": 1, "SeamlessClone (simple)": 1, "OtsuThreshold": 1, "RemapToInnerCylinder": 1, "RemapInsideParabolas": 1, "💾 Save Text File With Path": 1, "GITSScheduler": 1, "Recenter XL": 1, "PixArtControlNetCond": 1, "MakeCircularVAE": 1, "Tiled KSampler": 1, "JDC_GaussianBlur": 1, "TextureViewer": 1, "AspectSizeV2": 1, "CLIP Positive-Negative w/Text (WLSH)": 1, "LatentSelector": 1, "LayerColor: HSV": 1, "AV_ControlNetEfficientStackerSimple": 1, "SDPromptMerger": 1, "MaraScottMcBoatyUpscalerRefiner_v5": 1, "DynamiCrafterLoadInitNoise": 1, "KeypointsToImage": 1, "Load Face Enhance Model (mtb)": 1, "Restore Face (mtb)": 1, "Sharpen (mtb)": 1, "AudioLDM2Node": 1, "AV_LLMChat": 1, "AV_LLMMessage": 1, "AV_LLMApiConfig": 1, "AV_OpenAIApi": 1, "KSamplerVariationsWithNoise+": 1, "ttN pipeLoraStack": 1, "KSampler Gradually Adding More Denoise (efficient)": 1, "MMakerColorEnhance": 1, "LoraLoaderVanilla": 1, "AppInfo": 1, "brushnet_ipadapter_matteo": 1, "ImpactLogicalOperators": 1, "easy LLLiteLoader": 1, "easy XYPlotAdvanced": 1, "easy latentCompositeMaskedWithCond": 1, "easy XYInputs: PositiveCondList": 1, "easy latentNoisy": 1, "easy unSampler": 1, "HyperTile //Inspire": 1, "SDXLPromptStylerPreview": 1, "clear_model": 1, "OllamaImageToText": 1, "ETN_LoadImageBase64": 1, "ETN_SendImageWebSocket": 1, "TaraApiKeySaver": 1, "TaraDaisyChainNode": 1, "OmostLLMLoaderNode": 1, "OmostLLMChatNode": 1, "FaceSegmentation": 1, "GridStringList": 1, "Griptape Combine: Rules List": 1, "Griptape Run: Agent": 1, "ConditioningSetMaskAndCombine3": 1, "ExtraVAELoader": 1, "SamplerDPMPP_3M_SDE_DynETA": 1, "SamplerCustomNoise": 1, "Output min/max sigmas": 1, "ModelMergeSDXL": 1, "SplitImageChannels": 1, "PromptComposerMerge": 1, "PromptComposerGrouping": 1, "JWImageLevels": 1, "OptimizerConfigAdafactor": 1, "LayerMask: MaskGrain": 1, "LayerMask: MediapipeFacialSegment": 1, "LayerMask: MaskEdgeShrink": 1, "LayerMask: MaskByDifferent": 1, "LayerMask: BlendIf Mask": 1, "easy imageCount": 1, "CivitaiCheckpointLoaderSimple": 1, "EG_ZZ_SSKZ": 1, "ER_TX_ZZCJ": 1, "EG_TC_Node": 1, "EG_ZZ_MHHT": 1, "EG_TX_CJPJ": 1, "EG_TX_LJBC": 1, "Text Overlay": 1, "ApplyRaveAttentionNode": 1, "InstantIDAttentionPatch": 1, "google_tool": 1, "check_web_tool": 1, "load_ebd": 1, "IPAdapterPreciseStyleTransfer": 1, "Image Perlin Power Fractal": 1, "SaveTiff": 1, "CR Clamp Value": 1, "SDBatchLoader": 1, "Load Composition CSV": 1, "Load Lighting CSV": 1, "Load Styles CSV": 1, "MuseVPredictor V1 (comfyui_musev_evolved)": 1, "MuseVImg2Vid V1 (comfyui_musev_evolved)": 1, "FaceShaper": 1, "FaceShaperModels": 1, "FaceAlign": 1, "TwoAdvancedSamplersForMask": 1, "Text To Image (mtb)": 1, "easy promptLine": 1, "CondPassThrough": 1, "Bounded Image Blend": 1, "CLIPConditioning": 1, "CheckpointLoaderMixWithDiffusers": 1, "SelfGuidanceSampler": 1, "Auto-MSG-ALL": 1, "Auto-LLM-Text-Vision": 1, "Model Patch Seamless (mtb)": 1, "Image Tiled": 1, "Image Seamless Texture": 1, "ImageScaleDown": 1, "TextEncodeForSamplerParams+": 1, "ImpactSEGSConcat": 1, "NegiTools_OpenAiDalle3": 1, "DDColor_Colorize": 1, "ChangeLatentBatchSize //Inspire": 1, "file_padding": 1, "TextCombinations": 1, "EG_TSCMB_GL": 1, "SeargeControlnetAdapterV2": 1, "MaskFromBatch+": 1, "LensBlur": 1, "FromDetailerPipe_v2": 1, "EditDetailerPipe": 1, "Storydiffusion_Model_Loader": 1, "Storydiffusion_Sampler": 1, "OpNovelty": 1, "OpHarmonic": 1, "OpPercussive": 1, "OpSqrt": 1, "OpPow2": 1, "OpStretch": 1, "OpNormalize": 1, "OpAbs": 1, "OpRms": 1, "LLavaOptionalMemoryFreeAdvanced": 1, "ADE_CustomCFGSimple": 1, "CLIPMergeAdd": 1, "LoadTextFile": 1, "AudioToAudioData": 1, "Mask Switch (JPS)": 1, "CannyEdgeMask": 1, "CR Set Switch From String": 1, "CR Aspect Ratio Social Media": 1, "CR Select Resize Method": 1, "OmostLoadCanvasConditioningNode": 1, "Demofusion": 1, "ImageAlphaComposite": 1, "GPT4MiniNode": 1, "InpaintExtendOutpaint": 1, "SystemNotification|pysssss": 1, "PromptGenerateAPI": 1, "easy imageCropFromMask": 1, "easy pulIDApplyADV": 1, "IterativeMixingSampler": 1, "IterativeMixingScheduler": 1, "KeyframeApply": 1, "easy convertAnything": 1, "SaveImageToLocal": 1, "CombineNoiseLatentNode": 1, "SamplerInversedEulerNode": 1, "Generation Parameter Input": 1, "CR Img2Img Process Switch": 1, "SeargeSamplerInputs": 1, "SeargeSDXLSamplerV3": 1, "ImpactControlNetClearSEGS": 1, "TilePreprocessor_Provider_for_SEGS //Inspire": 1, "ADE_ApplyAnimateDiffModelWithPIA": 1, "ADE_InputPIA_PaperPresets": 1, "ADE_MultivalDynamicFloatInput": 1, "ADE_InputPIA_Multival": 1, "KeyInput": 1, "CharacterCount": 1, "ABCondition": 1, "FaceBBoxDetect(FaceParsing)": 1, "FaceBBoxDetectorLoader(FaceParsing)": 1, "ImageCropWithBBox(FaceParsing)": 1, "BBoxListItemSelect(FaceParsing)": 1, "ColorAdjust(FaceParsing)": 1, "ImageInsertWithBBox(FaceParsing)": 1, "BatchUncrop": 1, "LayerUtility: BatchSelector": 1, "PromptInjection": 1, "SeargeSDXLBasePromptEncoder": 1, "LongCLIPTextEncodeFlux": 1, "Fans Styler": 1, "KuwaharaBlur": 1, "Saturation": 1, "StyleModelLoader": 1, "StyleModelApply": 1, "CR Load Flow Frames": 1, "ShowSelfAttn": 1, "HFModelLoader": 1, "Text2ImageInference": 1, "DecodeLatent": 1, "ShowCrossAttn": 1, "PromptUtilitiesFormatString": 1, "StructuredOutput": 1, "CheckpointLoaderSimpleWithPreviews": 1, "LayerUtility: ImageScaleRestore": 1, "ExpressionEditor_": 1, "ADE_IterationOptsFreeInit": 1, "ADE_LoadAnimateLCMI2VModel": 1, "ADE_UpscaleAndVAEEncode": 1, "ADE_ApplyAnimateLCMI2VModel": 1, "DeepfuzePreview": 1, "DeepFuzeFaceSwap": 1, "LoadEmbedding": 1, "Prompts": 1, "OffsetMask": 1, "TaraPresetLLMConfig": 1, "TaraAdvancedComposition": 1, "MergeModels": 1, "Multiply Int Float (JPS)": 1, "MuseVRun": 1, "SEGSOrderedFilterDetailerHookProvider": 1, "StyleAlignedBatchAlign_": 1, "Gemini_15P_API_S_Chat_Advance_Zho": 1, "SamplerEulerAncestral": 1, "StyleAlignedReferenceSampler": 1, "CR Index": 1, "Generate Noise Image": 1, "ImagePadForBetterOutpaint": 1, "SeedGenerator": 1, "CR Interpolate Latents": 1, "Load Cache": 1, "Cache Node": 1, "Sleep": 1, "EG_SZ_JDYS": 1, "Asymmetric_Tiling_KSampler": 1, "SMoE_ModelLoader_Zho": 1, "SMoE_Generation_Zho": 1, "FaceEmbed": 1, "LCMLora": 1, "FaceSwapSetupPipeline": 1, "FaceSwapGenerationInpaint": 1, "Resolution Multiply (JPS)": 1, "ImageOverlay": 1, "ApplyVisualStyle": 1, "omost_decode": 1, "DYNAMIC_TRT_MODEL_CONVERSION": 1, "APISR_ModelLoader_Zho": 1, "APISR_Zho": 1, "easy applyPowerPaint": 1, "easy applyBrushNet": 1, "easy icLightApply": 1, "easy controlnetStack": 1, "Metric3D-DepthMapPreprocessor": 1, "Metric3D-NormalMapPreprocessor": 1, "JDCN_BatchImageLoadFromDir": 1, "sdBxb_textInput": 1, "LayeredDiffusionCondApply": 1, "CreativeArtPromptGenerator": 1, "LayerColor: RGB": 1, "Gemini_API_S_Chat_Zho": 1, "EZLoadImgBatchFromUrlsNode": 1, "ListSplit_": 1, "EG_HT_YSTZ": 1, "JsonToText": 1, "KeywordExtraction": 1, "LLavaPromptGenerator": 1, "CLIPTextEncode_party": 1, "Fooocus negative": 1, "Fooocus PreKSampler": 1, "Fooocus KSampler": 1, "Fooocus Controlnet": 1, "Fooocus Loader": 1, "Fooocus Styles": 1, "Fooocus positive": 1, "Sine Camera Pan [DVB]": 1, "XY Input: Lora Block Weight //Inspire": 1, "XY Input: Checkpoint": 1, "ClipClamp": 1, "DodgeAndBurn": 1, "FluxResolutionNode": 1, "MirroredImage": 1, "DiffusersTextureInversionLoader": 1, "LoRA Stack to String converter": 1, "JNodes_TokenCounter": 1, "Text to String": 1, "VHS_SelectEveryNthLatent": 1, "ollama": 1, "LayerUtility: ImageScaleByAspectRatio": 1, "Image Save with Prompt File (WLSH)": 1, "LayerStyle: Gradient Map": 1, "Load Image Based on Number": 1, "Canny_Preprocessor_Provider_for_SEGS //Inspire": 1, "LayerUtility: ImageAutoCrop V2": 1, "OllamaNode": 1, "ADE_CustomCFG": 1, "LayerStyle: Stroke": 1, "NoiseImage": 1, "FL_Ascii": 1, "TextCombinerTwo": 1, "MutualSelfAttentionControlSampler": 1, "MasaCtrlModelLoader": 1, "MasaCtrlInversion": 1, "MutualSelfAttentionControlMaskAutoSampler": 1, "MasaCtrlLoadImage": 1, "DirectSampler": 1, "JDCN_AnyFileSelector": 1, "JDCN_AnyFileList": 1, "VHS_PruneOutputs": 1, "ttN pipeLoaderSDXL": 1, "ttN pipeKSamplerSDXL": 1, "chaosaiart_Any_Switch_small": 1, "ImageFilterStackBlur": 1, "JDC_Plasma": 1, "CR Repeater": 1, "lavibridge_model_loader": 1, "lavi_bridge_t5_encoder": 1, "lavibridge_sampler": 1, "JWImageMix": 1, "String to Float": 1, "IF_DisplayOmni": 1, "OmostGreedyBagsTextEmbeddingNode": 1, "LoadImageFromPath": 1, "AnimateDiffLoraLoader": 1, "DiffusersModelMakeup": 1, "CreateIntListNode": 1, "LcmLoraLoader": 1, "DiffusersVaeLoader": 1, "StreamDiffusionCreateStream": 1, "DiffusersSchedulerLoader": 1, "StreamDiffusionSampler": 1, "DiffusersPipelineLoader": 1, "DiffusersClipTextEncode": 1, "MiDaS Model Loader": 1, "MiDaS Depth Approximation": 1, "LayerFilter: MotionBlur": 1, "Image size to string": 1, "Echo_LoadModel": 1, "Echo_Sampler": 1, "KSamplerProgress //Inspire": 1, "UnsamplerFlattenNode": 1, "TrajectoryNode": 1, "KSamplerFlattenNode": 1, "FlattenCheckpointLoaderNode": 1, "roop": 1, "Image Contrast Adjustment [Dream]": 1, "MotionLoraLoader": 1, "I2V_AdapterNode": 1, "Export API": 1, "MaskFrameSkipping": 1, "scale": 1, "Loop": 1, "LoopEnd": 1, "LoopStart": 1, "LayerFilter: ChannelShake": 1, "LayerFilter: LightLeak": 1, "LayerFilter: ColorMap": 1, "LayerFilter: AddGrain": 1, "LayerFilter: FilmV2": 1, "CamerasImage_Zho": 1, "SaveLatent": 1, "CopyMakeBorder": 1, "UnGridify (image)": 1, "Repeat Into Grid (image)": 1, "Color (hexadecimal)": 1, "Contours": 1, "Draw Contour(s)": 1, "ToModelList": 1, "Framed Mask Grab Cut 2": 1, "InpaintResize": 1, "PerpNegAdaptiveGuidanceGuider": 1, "ImpactDummyInput": 1, "Images as Latents (PPF Noise)": 1, "Perlin Power Fractal Latent (PPF Noise)": 1, "Cross-Hatch Power Fractal (PPF Noise)": 1, "Linear Cross-Hatch Power Fractal (PPF Noise)": 1, "CLIPTextEncodeControlnet": 1}