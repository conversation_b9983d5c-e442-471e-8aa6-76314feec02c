/* this CSS contains only the basic CSS needed to run the app and use it */

.lgraphcanvas {
  /*cursor: crosshair;*/
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  outline: none;
  font-family: Tahoma, sans-serif;
}

.lgraphcanvas * {
  box-sizing: border-box;
}

.litegraph.litecontextmenu {
  font-family: Tahoma, sans-serif;
  position: fixed;
  top: 100px;
  left: 100px;
  min-width: 100px;
  color: #aaf;
  padding: 0;
  box-shadow: 0 0 10px black !important;
  background-color: #2e2e2e !important;
  z-index: 10;
  max-height: -webkit-fill-available;
  overflow-y: auto;
}

/* Enable scrolling overflow in Firefox */
@supports not (max-height: -webkit-fill-available) {
  .litegraph.litecontextmenu {
    max-height: 80vh;
    overflow-y: scroll;
  }
}

.litegraph.litecontextmenu.dark {
  background-color: #000 !important;
}

.litegraph.litecontextmenu .litemenu-title img {
  margin-top: 2px;
  margin-left: 2px;
  margin-right: 4px;
}

.litegraph.litecontextmenu .litemenu-entry {
  margin: 2px;
  padding: 2px;
}

.litegraph.litecontextmenu .litemenu-entry.submenu {
  background-color: #2e2e2e !important;
}

.litegraph.litecontextmenu.dark .litemenu-entry.submenu {
  background-color: #000 !important;
}

.litegraph .litemenubar ul {
  font-family: Tahoma, sans-serif;
  margin: 0;
  padding: 0;
}

.litegraph .litemenubar li {
  font-size: 14px;
  color: #999;
  display: inline-block;
  min-width: 50px;
  padding-left: 10px;
  padding-right: 10px;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  cursor: pointer;
}

.litegraph .litemenubar li:hover {
  background-color: #777;
  color: #eee;
}

.litegraph .litegraph .litemenubar-panel {
  position: absolute;
  top: 5px;
  left: 5px;
  min-width: 100px;
  background-color: #444;
  box-shadow: 0 0 3px black;
  padding: 4px;
  border-bottom: 2px solid #aaf;
  z-index: 10;
}

.litegraph .litemenu-entry,
.litemenu-title {
  font-size: 12px;
  color: #aaa;
  padding: 0 0 0 4px;
  margin: 2px;
  padding-left: 2px;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  cursor: pointer;
}

.litegraph .litemenu-entry .icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin: 2px;
  vertical-align: top;
}

.litegraph .litemenu-entry.checked .icon {
  background-color: #aaf;
}

.litegraph .litemenu-entry .more {
  float: right;
  padding-right: 5px;
}

.litegraph .litemenu-entry.disabled {
  opacity: 0.5;
  cursor: default;
}

.litegraph .litemenu-entry.separator {
  display: block;
  border-top: 1px solid #333;
  border-bottom: 1px solid #666;
  width: 100%;
  height: 0px;
  margin: 3px 0 2px 0;
  background-color: transparent;
  padding: 0 !important;
  cursor: default !important;
}

.litegraph .litemenu-entry.has_submenu {
  border-right: 2px solid cyan;
}

.litegraph .litemenu-title {
  color: #dde;
  background-color: #111;
  margin: 0;
  padding: 2px;
  cursor: default;
}

.litegraph .litemenu-entry:hover:not(.disabled):not(.separator) {
  background-color: #444 !important;
  color: #eee;
  transition: all 0.2s;
}

.litegraph .litemenu-entry .property_name {
  display: inline-block;
  text-align: left;
  min-width: 80px;
  min-height: 1.2em;
}

.litegraph .litemenu-entry .property_value {
  display: inline-block;
  background-color: rgba(0, 0, 0, 0.5);
  text-align: right;
  min-width: 80px;
  min-height: 1.2em;
  vertical-align: middle;
  padding-right: 10px;
}

.litegraph.litesearchbox {
  font-family: Tahoma, sans-serif;
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  padding-top: 4px;
}

.litegraph.litesearchbox input,
.litegraph.litesearchbox select {
  margin-top: 3px;
  min-width: 60px;
  min-height: 1.5em;
  background-color: black;
  border: 0;
  color: white;
  padding-left: 10px;
  margin-right: 5px;
  max-width: 300px;
}

.litegraph.litesearchbox .name {
  display: inline-block;
  min-width: 60px;
  min-height: 1.5em;
  padding-left: 10px;
}

.litegraph.litesearchbox .helper {
  overflow: auto;
  max-height: 200px;
  margin-top: 2px;
}

.litegraph.lite-search-item {
  font-family: Tahoma, sans-serif;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding-top: 2px;
}

.litegraph.lite-search-item.not_in_filter {
  /*background-color: rgba(50, 50, 50, 0.5);*/
  /*color: #999;*/
  color: #b99;
  font-style: italic;
}

.litegraph.lite-search-item.generic_type {
  /*background-color: rgba(50, 50, 50, 0.5);*/
  /*color: #DD9;*/
  color: #999;
  font-style: italic;
}

.litegraph.lite-search-item:hover,
.litegraph.lite-search-item.selected {
  cursor: pointer;
  background-color: white;
  color: black;
}

.litegraph.lite-search-item-type {
  display: inline-block;
  background: rgba(0, 0, 0, 0.2);
  margin-left: 5px;
  font-size: 14px;
  padding: 2px 5px;
  position: relative;
  top: -2px;
  opacity: 0.8;
  border-radius: 4px;
}

/* DIALOGs ******/

.litegraph .dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -150px;
  margin-left: -200px;

  background-color: #2a2a2a;

  min-width: 400px;
  min-height: 200px;
  box-shadow: 0 0 4px #111;
  border-radius: 6px;
}

.litegraph .dialog.settings {
  left: 10px;
  top: 10px;
  height: calc(100% - 20px);
  margin: auto;
  max-width: 50%;
}

.litegraph .dialog.centered {
  top: 50px;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
  min-width: 600px;
  min-height: 300px;
  height: calc(100% - 100px);
  margin: auto;
}

.litegraph .dialog .close {
  float: right;
  margin: 4px;
  margin-right: 10px;
  cursor: pointer;
  font-size: 1.4em;
}

.litegraph .dialog .close:hover {
  color: white;
}

.litegraph .dialog .dialog-header {
  color: #aaa;
  border-bottom: 1px solid #161616;
  height: 40px;
}
.litegraph .dialog .dialog-footer {
  height: 50px;
  padding: 10px;
  border-top: 1px solid #1a1a1a;
}

.litegraph .dialog .dialog-header .dialog-title {
  font: 20px "Arial";
  margin: 4px;
  padding: 4px 10px;
  display: inline-block;
}

.litegraph .dialog .dialog-content,
.litegraph .dialog .dialog-alt-content {
  height: calc(100% - 90px);
  width: 100%;
  min-height: 100px;
  display: inline-block;
  color: #aaa;
  /*background-color: black;*/
  overflow: auto;
}

.litegraph .dialog .dialog-content h3 {
  margin: 10px;
}

.litegraph .dialog .dialog-content .connections {
  flex-direction: row;
}

.litegraph .dialog .dialog-content .connections .connections_side {
  width: calc(50% - 5px);
  min-height: 100px;
  background-color: black;
  display: flex;
}

.litegraph .dialog .node_type {
  font-size: 1.2em;
  display: block;
  margin: 10px;
}

.litegraph .dialog .node_desc {
  opacity: 0.5;
  display: block;
  margin: 10px;
}

.litegraph .dialog .separator {
  display: block;
  width: calc(100% - 4px);
  height: 1px;
  border-top: 1px solid #000;
  border-bottom: 1px solid #333;
  margin: 10px 2px;
  padding: 0;
}

.litegraph .dialog .property {
  margin-bottom: 2px;
  padding: 4px;
}

.litegraph .dialog .property:hover {
  background: #545454;
}

.litegraph .dialog .property_name {
  color: #737373;
  display: inline-block;
  text-align: left;
  vertical-align: top;
  width: 160px;
  padding-left: 4px;
  overflow: hidden;
  margin-right: 6px;
}

.litegraph .dialog .property:hover .property_name {
  color: white;
}

.litegraph .dialog .property_value {
  display: inline-block;
  text-align: right;
  color: #aaa;
  background-color: #1a1a1a;
  /*width: calc( 100% - 122px );*/
  max-width: calc(100% - 162px);
  min-width: 200px;
  max-height: 300px;
  min-height: 20px;
  padding: 4px;
  padding-right: 12px;
  overflow: hidden;
  cursor: pointer;
  border-radius: 3px;
}

.litegraph .dialog .property_value:hover {
  color: white;
}

.litegraph .dialog .property.boolean .property_value {
  padding-right: 30px;
  color: #a88;
  /*width: auto;
    float: right;*/
}

.litegraph .dialog .property.boolean.bool-on .property_name {
  color: #8a8;
}
.litegraph .dialog .property.boolean.bool-on .property_value {
  color: #8a8;
}

.litegraph .dialog .btn {
  border: 0;
  border-radius: 4px;
  padding: 4px 20px;
  margin-left: 0px;
  background-color: #060606;
  color: #8e8e8e;
}

.litegraph .dialog .btn:hover {
  background-color: #111;
  color: #fff;
}

.litegraph .dialog .btn.delete:hover {
  background-color: #f33;
  color: black;
}

.litegraph .subgraph_property {
  padding: 4px;
}

.litegraph .subgraph_property:hover {
  background-color: #333;
}

.litegraph .subgraph_property.extra {
  margin-top: 8px;
}

.litegraph .subgraph_property span.name {
  font-size: 1.3em;
  padding-left: 4px;
}

.litegraph .subgraph_property span.type {
  opacity: 0.5;
  margin-right: 20px;
  padding-left: 4px;
}

.litegraph .subgraph_property span.label {
  display: inline-block;
  width: 60px;
  padding: 0px 10px;
}

.litegraph .subgraph_property input {
  width: 140px;
  color: #999;
  background-color: #1a1a1a;
  border-radius: 4px;
  border: 0;
  margin-right: 10px;
  padding: 4px;
  padding-left: 10px;
}

.litegraph .subgraph_property button {
  background-color: #1c1c1c;
  color: #aaa;
  border: 0;
  border-radius: 2px;
  padding: 4px 10px;
  cursor: pointer;
}

.litegraph .subgraph_property.extra {
  color: #ccc;
}

.litegraph .subgraph_property.extra input {
  background-color: #111;
}

.litegraph .bullet_icon {
  margin-left: 10px;
  border-radius: 10px;
  width: 12px;
  height: 12px;
  background-color: #666;
  display: inline-block;
  margin-top: 2px;
  margin-right: 4px;
  transition: background-color 0.1s ease 0s;
  -moz-transition: background-color 0.1s ease 0s;
}

.litegraph .bullet_icon:hover {
  background-color: #698;
  cursor: pointer;
}

/* OLD */

.graphcontextmenu {
  padding: 4px;
  min-width: 100px;
}

.graphcontextmenu-title {
  color: #dde;
  background-color: #222;
  margin: 0;
  padding: 2px;
  cursor: default;
}

.graphmenu-entry {
  box-sizing: border-box;
  margin: 2px;
  padding-left: 20px;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  transition: all linear 0.3s;
}

.graphmenu-entry.event,
.litemenu-entry.event {
  border-left: 8px solid orange;
  padding-left: 12px;
}

.graphmenu-entry.disabled {
  opacity: 0.3;
}

.graphmenu-entry.submenu {
  border-right: 2px solid #eee;
}

.graphmenu-entry:hover {
  background-color: #555;
}

.graphmenu-entry.separator {
  background-color: #111;
  border-bottom: 1px solid #666;
  height: 1px;
  width: calc(100% - 20px);
  -moz-width: calc(100% - 20px);
  -webkit-width: calc(100% - 20px);
}

.graphmenu-entry .property_name {
  display: inline-block;
  text-align: left;
  min-width: 80px;
  min-height: 1.2em;
}

.graphmenu-entry .property_value,
.litemenu-entry .property_value {
  display: inline-block;
  background-color: rgba(0, 0, 0, 0.5);
  text-align: right;
  min-width: 80px;
  min-height: 1.2em;
  vertical-align: middle;
  padding-right: 10px;
}

.graphdialog {
  position: absolute;
  top: 10px;
  left: 10px;
  min-height: 2em;
  background-color: #333;
  font-size: 1.2em;
  box-shadow: 0 0 10px black !important;
  z-index: 10;
}

.graphdialog.rounded {
  border-radius: 12px;
  padding-right: 2px;
}

.graphdialog .name {
  display: inline-block;
  min-width: 60px;
  min-height: 1.5em;
  padding-left: 10px;
}

.graphdialog input,
.graphdialog textarea,
.graphdialog select {
  margin: 3px;
  min-width: 60px;
  min-height: 1.5em;
  background-color: black;
  border: 0;
  color: white;
  padding-left: 10px;
  outline: none;
}

.graphdialog textarea {
  min-height: 150px;
}

.graphdialog button {
  margin-top: 3px;
  vertical-align: top;
  background-color: #999;
  border: 0;
}

.graphdialog button.rounded,
.graphdialog input.rounded {
  border-radius: 0 12px 12px 0;
}

.graphdialog .helper {
  overflow: auto;
  max-height: 200px;
}

.graphdialog .help-item {
  padding-left: 10px;
}

.graphdialog .help-item:hover,
.graphdialog .help-item.selected {
  cursor: pointer;
  background-color: white;
  color: black;
}

.litegraph .dialog {
  min-height: 0;
}
.litegraph .dialog .dialog-content {
  display: block;
}
.litegraph .dialog .dialog-content .subgraph_property {
  padding: 5px;
}
.litegraph .dialog .dialog-footer {
  margin: 0;
}
.litegraph .dialog .dialog-footer .subgraph_property {
  margin-top: 0;
  display: flex;
  align-items: center;
  padding: 5px;
}
.litegraph .dialog .dialog-footer .subgraph_property .name {
  flex: 1;
}
.litegraph .graphdialog {
  display: flex;
  align-items: center;
  border-radius: 20px;
  padding: 4px 10px;
  position: fixed;
}
.litegraph .graphdialog .name {
  padding: 0;
  min-height: 0;
  font-size: 16px;
  vertical-align: middle;
}
.litegraph .graphdialog .value {
  font-size: 16px;
  min-height: 0;
  margin: 0 10px;
  padding: 2px 5px;
}
.litegraph .graphdialog input[type="checkbox"] {
  width: 16px;
  height: 16px;
}
.litegraph .graphdialog button {
  padding: 4px 18px;
  border-radius: 20px;
  cursor: pointer;
}
@font-face {
    font-family: 'primeicons';
    font-display: block;
    src: url('./primeicons-DMOk5skT.eot');
    src: url('./primeicons-DMOk5skT.eot?#iefix') format('embedded-opentype'), url('./primeicons-C6QP2o4f.woff2') format('woff2'), url('./primeicons-WjwUDZjB.woff') format('woff'), url('./primeicons-MpK4pl85.ttf') format('truetype'), url('./primeicons-Dr5RGzOO.svg?#primeicons') format('svg');
    font-weight: normal;
    font-style: normal;
}

.pi {
    font-family: 'primeicons';
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    display: inline-block;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.pi:before {
    --webkit-backface-visibility:hidden;
    backface-visibility: hidden;
}

.pi-fw {
    width: 1.28571429em;
    text-align: center;
}

.pi-spin {
    animation: fa-spin 2s infinite linear;
}

@media (prefers-reduced-motion: reduce) {
  .pi-spin {
    animation-delay: -1ms;
    animation-duration: 1ms;
    animation-iteration-count: 1;
    transition-delay: 0s;
    transition-duration: 0s;
  }
}

@keyframes fa-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(359deg);
    }
}

.pi-folder-plus:before {
    content: "\ea05";
}

.pi-receipt:before {
    content: "\ea06";
}

.pi-asterisk:before {
    content: "\ea07";
}

.pi-face-smile:before {
    content: "\ea08";
}

.pi-pinterest:before {
    content: "\ea09";
}

.pi-expand:before {
    content: "\ea0a";
}

.pi-pen-to-square:before {
    content: "\ea0b";
}

.pi-wave-pulse:before {
    content: "\ea0c";
}

.pi-turkish-lira:before {
    content: "\ea0d";
}

.pi-spinner-dotted:before {
    content: "\ea0e";
}

.pi-crown:before {
    content: "\ea0f";
}

.pi-pause-circle:before {
    content: "\ea10";
}

.pi-warehouse:before {
    content: "\ea11";
}

.pi-objects-column:before {
    content: "\ea12";
}

.pi-clipboard:before {
    content: "\ea13";
}

.pi-play-circle:before {
    content: "\ea14";
}

.pi-venus:before {
    content: "\ea15";
}

.pi-cart-minus:before {
    content: "\ea16";
}

.pi-file-plus:before {
    content: "\ea17";
}

.pi-microchip:before {
    content: "\ea18";
}

.pi-twitch:before {
    content: "\ea19";
}

.pi-building-columns:before {
    content: "\ea1a";
}

.pi-file-check:before {
    content: "\ea1b";
}

.pi-microchip-ai:before {
    content: "\ea1c";
}

.pi-trophy:before {
    content: "\ea1d";
}

.pi-barcode:before {
    content: "\ea1e";
}

.pi-file-arrow-up:before {
    content: "\ea1f";
}

.pi-mars:before {
    content: "\ea20";
}

.pi-tiktok:before {
    content: "\ea21";
}

.pi-arrow-up-right-and-arrow-down-left-from-center:before {
    content: "\ea22";
}

.pi-ethereum:before {
    content: "\ea23";
}

.pi-list-check:before {
    content: "\ea24";
}

.pi-thumbtack:before {
    content: "\ea25";
}

.pi-arrow-down-left-and-arrow-up-right-to-center:before {
    content: "\ea26";
}

.pi-equals:before {
    content: "\ea27";
}

.pi-lightbulb:before {
    content: "\ea28";
}

.pi-star-half:before {
    content: "\ea29";
}

.pi-address-book:before {
    content: "\ea2a";
}

.pi-chart-scatter:before {
    content: "\ea2b";
}

.pi-indian-rupee:before {
    content: "\ea2c";
}

.pi-star-half-fill:before {
    content: "\ea2d";
}

.pi-cart-arrow-down:before {
    content: "\ea2e";
}

.pi-calendar-clock:before {
    content: "\ea2f";
}

.pi-sort-up-fill:before {
    content: "\ea30";
}

.pi-sparkles:before {
    content: "\ea31";
}

.pi-bullseye:before {
    content: "\ea32";
}

.pi-sort-down-fill:before {
    content: "\ea33";
}

.pi-graduation-cap:before {
    content: "\ea34";
}

.pi-hammer:before {
    content: "\ea35";
}

.pi-bell-slash:before {
    content: "\ea36";
}

.pi-gauge:before {
    content: "\ea37";
}

.pi-shop:before {
    content: "\ea38";
}

.pi-headphones:before {
    content: "\ea39";
}

.pi-eraser:before {
    content: "\ea04";
}

.pi-stopwatch:before {
    content: "\ea01";
}

.pi-verified:before {
    content: "\ea02";
}

.pi-delete-left:before {
    content: "\ea03";
}

.pi-hourglass:before {
    content: "\e9fe";
}

.pi-truck:before {
    content: "\ea00";
}

.pi-wrench:before {
    content: "\e9ff";
}

.pi-microphone:before {
    content: "\e9fa";
}

.pi-megaphone:before {
    content: "\e9fb";
}

.pi-arrow-right-arrow-left:before {
    content: "\e9fc";
}

.pi-bitcoin:before {
    content: "\e9fd";
}

.pi-file-edit:before {
    content: "\e9f6";
}

.pi-language:before {
    content: "\e9f7";
}

.pi-file-export:before {
    content: "\e9f8";
}

.pi-file-import:before {
    content: "\e9f9";
}

.pi-file-word:before {
    content: "\e9f1";
}

.pi-gift:before {
    content: "\e9f2";
}

.pi-cart-plus:before {
    content: "\e9f3";
}

.pi-thumbs-down-fill:before {
    content: "\e9f4";
}

.pi-thumbs-up-fill:before {
    content: "\e9f5";
}

.pi-arrows-alt:before {
    content: "\e9f0";
}

.pi-calculator:before {
    content: "\e9ef";
}

.pi-sort-alt-slash:before {
    content: "\e9ee";
}

.pi-arrows-h:before {
    content: "\e9ec";
}

.pi-arrows-v:before {
    content: "\e9ed";
}

.pi-pound:before {
    content: "\e9eb";
}

.pi-prime:before {
    content: "\e9ea";
}

.pi-chart-pie:before {
    content: "\e9e9";
}

.pi-reddit:before {
    content: "\e9e8";
}

.pi-code:before {
    content: "\e9e7";
}

.pi-sync:before {
    content: "\e9e6";
}

.pi-shopping-bag:before {
    content: "\e9e5";
}

.pi-server:before {
    content: "\e9e4";
}

.pi-database:before {
    content: "\e9e3";
}

.pi-hashtag:before {
    content: "\e9e2";
}

.pi-bookmark-fill:before {
    content: "\e9df";
}

.pi-filter-fill:before {
    content: "\e9e0";
}

.pi-heart-fill:before {
    content: "\e9e1";
}

.pi-flag-fill:before {
    content: "\e9de";
}

.pi-circle:before {
    content: "\e9dc";
}

.pi-circle-fill:before {
    content: "\e9dd";
}

.pi-bolt:before {
    content: "\e9db";
}

.pi-history:before {
    content: "\e9da";
}

.pi-box:before {
    content: "\e9d9";
}

.pi-at:before {
    content: "\e9d8";
}

.pi-arrow-up-right:before {
    content: "\e9d4";
}

.pi-arrow-up-left:before {
    content: "\e9d5";
}

.pi-arrow-down-left:before {
    content: "\e9d6";
}

.pi-arrow-down-right:before {
    content: "\e9d7";
}

.pi-telegram:before {
    content: "\e9d3";
}

.pi-stop-circle:before {
    content: "\e9d2";
}

.pi-stop:before {
    content: "\e9d1";
}

.pi-whatsapp:before {
    content: "\e9d0";
}

.pi-building:before {
    content: "\e9cf";
}

.pi-qrcode:before {
    content: "\e9ce";
}

.pi-car:before {
    content: "\e9cd";
}

.pi-instagram:before {
    content: "\e9cc";
}

.pi-linkedin:before {
    content: "\e9cb";
}

.pi-send:before {
    content: "\e9ca";
}

.pi-slack:before {
    content: "\e9c9";
}

.pi-sun:before {
    content: "\e9c8";
}

.pi-moon:before {
    content: "\e9c7";
}

.pi-vimeo:before {
    content: "\e9c6";
}

.pi-youtube:before {
    content: "\e9c5";
}

.pi-flag:before {
    content: "\e9c4";
}

.pi-wallet:before {
    content: "\e9c3";
}

.pi-map:before {
    content: "\e9c2";
}

.pi-link:before {
    content: "\e9c1";
}

.pi-credit-card:before {
    content: "\e9bf";
}

.pi-discord:before {
    content: "\e9c0";
}

.pi-percentage:before {
    content: "\e9be";
}

.pi-euro:before {
    content: "\e9bd";
}

.pi-book:before {
    content: "\e9ba";
}

.pi-shield:before {
    content: "\e9b9";
}

.pi-paypal:before {
    content: "\e9bb";
}

.pi-amazon:before {
    content: "\e9bc";
}

.pi-phone:before {
    content: "\e9b8";
}

.pi-filter-slash:before {
    content: "\e9b7";
}

.pi-facebook:before {
    content: "\e9b4";
}

.pi-github:before {
    content: "\e9b5";
}

.pi-twitter:before {
    content: "\e9b6";
}

.pi-step-backward-alt:before {
    content: "\e9ac";
}

.pi-step-forward-alt:before {
    content: "\e9ad";
}

.pi-forward:before {
    content: "\e9ae";
}

.pi-backward:before {
    content: "\e9af";
}

.pi-fast-backward:before {
    content: "\e9b0";
}

.pi-fast-forward:before {
    content: "\e9b1";
}

.pi-pause:before {
    content: "\e9b2";
}

.pi-play:before {
    content: "\e9b3";
}

.pi-compass:before {
    content: "\e9ab";
}

.pi-id-card:before {
    content: "\e9aa";
}

.pi-ticket:before {
    content: "\e9a9";
}

.pi-file-o:before {
    content: "\e9a8";
}

.pi-reply:before {
    content: "\e9a7";
}

.pi-directions-alt:before {
    content: "\e9a5";
}

.pi-directions:before {
    content: "\e9a6";
}

.pi-thumbs-up:before {
    content: "\e9a3";
}

.pi-thumbs-down:before {
    content: "\e9a4";
}

.pi-sort-numeric-down-alt:before {
    content: "\e996";
}

.pi-sort-numeric-up-alt:before {
    content: "\e997";
}

.pi-sort-alpha-down-alt:before {
    content: "\e998";
}

.pi-sort-alpha-up-alt:before {
    content: "\e999";
}

.pi-sort-numeric-down:before {
    content: "\e99a";
}

.pi-sort-numeric-up:before {
    content: "\e99b";
}

.pi-sort-alpha-down:before {
    content: "\e99c";
}

.pi-sort-alpha-up:before {
    content: "\e99d";
}

.pi-sort-alt:before {
    content: "\e99e";
}

.pi-sort-amount-up:before {
    content: "\e99f";
}

.pi-sort-amount-down:before {
    content: "\e9a0";
}

.pi-sort-amount-down-alt:before {
    content: "\e9a1";
}

.pi-sort-amount-up-alt:before {
    content: "\e9a2";
}

.pi-palette:before {
    content: "\e995";
}

.pi-undo:before {
    content: "\e994";
}

.pi-desktop:before {
    content: "\e993";
}

.pi-sliders-v:before {
    content: "\e991";
}

.pi-sliders-h:before {
    content: "\e992";
}

.pi-search-plus:before {
    content: "\e98f";
}

.pi-search-minus:before {
    content: "\e990";
}

.pi-file-excel:before {
    content: "\e98e";
}

.pi-file-pdf:before {
    content: "\e98d";
}

.pi-check-square:before {
    content: "\e98c";
}

.pi-chart-line:before {
    content: "\e98b";
}

.pi-user-edit:before {
    content: "\e98a";
}

.pi-exclamation-circle:before {
    content: "\e989";
}

.pi-android:before {
    content: "\e985";
}

.pi-google:before {
    content: "\e986";
}

.pi-apple:before {
    content: "\e987";
}

.pi-microsoft:before {
    content: "\e988";
}

.pi-heart:before {
    content: "\e984";
}

.pi-mobile:before {
    content: "\e982";
}

.pi-tablet:before {
    content: "\e983";
}

.pi-key:before {
    content: "\e981";
}

.pi-shopping-cart:before {
    content: "\e980";
}

.pi-comments:before {
    content: "\e97e";
}

.pi-comment:before {
    content: "\e97f";
}

.pi-briefcase:before {
    content: "\e97d";
}

.pi-bell:before {
    content: "\e97c";
}

.pi-paperclip:before {
    content: "\e97b";
}

.pi-share-alt:before {
    content: "\e97a";
}

.pi-envelope:before {
    content: "\e979";
}

.pi-volume-down:before {
    content: "\e976";
}

.pi-volume-up:before {
    content: "\e977";
}

.pi-volume-off:before {
    content: "\e978";
}

.pi-eject:before {
    content: "\e975";
}

.pi-money-bill:before {
    content: "\e974";
}

.pi-images:before {
    content: "\e973";
}

.pi-image:before {
    content: "\e972";
}

.pi-sign-in:before {
    content: "\e970";
}

.pi-sign-out:before {
    content: "\e971";
}

.pi-wifi:before {
    content: "\e96f";
}

.pi-sitemap:before {
    content: "\e96e";
}

.pi-chart-bar:before {
    content: "\e96d";
}

.pi-camera:before {
    content: "\e96c";
}

.pi-dollar:before {
    content: "\e96b";
}

.pi-lock-open:before {
    content: "\e96a";
}

.pi-table:before {
    content: "\e969";
}

.pi-map-marker:before {
    content: "\e968";
}

.pi-list:before {
    content: "\e967";
}

.pi-eye-slash:before {
    content: "\e965";
}

.pi-eye:before {
    content: "\e966";
}

.pi-folder-open:before {
    content: "\e964";
}

.pi-folder:before {
    content: "\e963";
}

.pi-video:before {
    content: "\e962";
}

.pi-inbox:before {
    content: "\e961";
}

.pi-lock:before {
    content: "\e95f";
}

.pi-unlock:before {
    content: "\e960";
}

.pi-tags:before {
    content: "\e95d";
}

.pi-tag:before {
    content: "\e95e";
}

.pi-power-off:before {
    content: "\e95c";
}

.pi-save:before {
    content: "\e95b";
}

.pi-question-circle:before {
    content: "\e959";
}

.pi-question:before {
    content: "\e95a";
}

.pi-copy:before {
    content: "\e957";
}

.pi-file:before {
    content: "\e958";
}

.pi-clone:before {
    content: "\e955";
}

.pi-calendar-times:before {
    content: "\e952";
}

.pi-calendar-minus:before {
    content: "\e953";
}

.pi-calendar-plus:before {
    content: "\e954";
}

.pi-ellipsis-v:before {
    content: "\e950";
}

.pi-ellipsis-h:before {
    content: "\e951";
}

.pi-bookmark:before {
    content: "\e94e";
}

.pi-globe:before {
    content: "\e94f";
}

.pi-replay:before {
    content: "\e94d";
}

.pi-filter:before {
    content: "\e94c";
}

.pi-print:before {
    content: "\e94b";
}

.pi-align-right:before {
    content: "\e946";
}

.pi-align-left:before {
    content: "\e947";
}

.pi-align-center:before {
    content: "\e948";
}

.pi-align-justify:before {
    content: "\e949";
}

.pi-cog:before {
    content: "\e94a";
}

.pi-cloud-download:before {
    content: "\e943";
}

.pi-cloud-upload:before {
    content: "\e944";
}

.pi-cloud:before {
    content: "\e945";
}

.pi-pencil:before {
    content: "\e942";
}

.pi-users:before {
    content: "\e941";
}

.pi-clock:before {
    content: "\e940";
}

.pi-user-minus:before {
    content: "\e93e";
}

.pi-user-plus:before {
    content: "\e93f";
}

.pi-trash:before {
    content: "\e93d";
}

.pi-external-link:before {
    content: "\e93c";
}

.pi-window-maximize:before {
    content: "\e93b";
}

.pi-window-minimize:before {
    content: "\e93a";
}

.pi-refresh:before {
    content: "\e938";
}
  
.pi-user:before {
    content: "\e939";
}

.pi-exclamation-triangle:before {
    content: "\e922";
}

.pi-calendar:before {
    content: "\e927";
}

.pi-chevron-circle-left:before {
    content: "\e928";
}

.pi-chevron-circle-down:before {
    content: "\e929";
}

.pi-chevron-circle-right:before {
    content: "\e92a";
}

.pi-chevron-circle-up:before {
    content: "\e92b";
}

.pi-angle-double-down:before {
    content: "\e92c";
}

.pi-angle-double-left:before {
    content: "\e92d";
}

.pi-angle-double-right:before {
    content: "\e92e";
}

.pi-angle-double-up:before {
    content: "\e92f";
}

.pi-angle-down:before {
    content: "\e930";
}

.pi-angle-left:before {
    content: "\e931";
}

.pi-angle-right:before {
    content: "\e932";
}

.pi-angle-up:before {
    content: "\e933";
}

.pi-upload:before {
    content: "\e934";
}

.pi-download:before {
    content: "\e956";
}

.pi-ban:before {
    content: "\e935";
}

.pi-star-fill:before {
    content: "\e936";
}

.pi-star:before {
    content: "\e937";
}

.pi-chevron-left:before {
    content: "\e900";
}

.pi-chevron-right:before {
    content: "\e901";
}

.pi-chevron-down:before {
    content: "\e902";
}

.pi-chevron-up:before {
    content: "\e903";
}

.pi-caret-left:before {
    content: "\e904";
}

.pi-caret-right:before {
    content: "\e905";
}

.pi-caret-down:before {
    content: "\e906";
}

.pi-caret-up:before {
    content: "\e907";
}

.pi-search:before {
    content: "\e908";
}

.pi-check:before {
    content: "\e909";
}

.pi-check-circle:before {
    content: "\e90a";
}

.pi-times:before {
    content: "\e90b";
}

.pi-times-circle:before {
    content: "\e90c";
}

.pi-plus:before {
    content: "\e90d";
}

.pi-plus-circle:before {
    content: "\e90e";
}

.pi-minus:before {
    content: "\e90f";
}

.pi-minus-circle:before {
    content: "\e910";
}

.pi-circle-on:before {
    content: "\e911";
}

.pi-circle-off:before {
    content: "\e912";
}

.pi-sort-down:before {
    content: "\e913";
}

.pi-sort-up:before {
    content: "\e914";
}

.pi-sort:before {
    content: "\e915";
}

.pi-step-backward:before {
    content: "\e916";
}

.pi-step-forward:before {
    content: "\e917";
}

.pi-th-large:before {
    content: "\e918";
}

.pi-arrow-down:before {
    content: "\e919";
}

.pi-arrow-left:before {
    content: "\e91a";
}

.pi-arrow-right:before {
    content: "\e91b";
}

.pi-arrow-up:before {
    content: "\e91c";
}

.pi-bars:before {
    content: "\e91d";
}

.pi-arrow-circle-down:before {
    content: "\e91e";
}

.pi-arrow-circle-left:before {
    content: "\e91f";
}

.pi-arrow-circle-right:before {
    content: "\e920";
}

.pi-arrow-circle-up:before {
    content: "\e921";
}

.pi-info:before {
    content: "\e923";
}

.pi-info-circle:before {
    content: "\e924";
}

.pi-home:before {
    content: "\e925";
}

.pi-spinner:before {
    content: "\e926";
}
@layer primevue, tailwind-utilities;

@layer tailwind-utilities {
  .container{
    width: 100%;
  }
  @media (min-width: 640px){

    .container{
      max-width: 640px;
    }
  }
  @media (min-width: 768px){

    .container{
      max-width: 768px;
    }
  }
  @media (min-width: 1024px){

    .container{
      max-width: 1024px;
    }
  }
  @media (min-width: 1280px){

    .container{
      max-width: 1280px;
    }
  }
  @media (min-width: 1536px){

    .container{
      max-width: 1536px;
    }
  }
  @media (min-width: 1800px){

    .container{
      max-width: 1800px;
    }
  }
  @media (min-width: 2500px){

    .container{
      max-width: 2500px;
    }
  }
  @media (min-width: 3200px){

    .container{
      max-width: 3200px;
    }
  }
  .pointer-events-none{
    pointer-events: none;
  }
  .pointer-events-auto{
    pointer-events: auto;
  }
  .\!visible{
    visibility: visible !important;
  }
  .visible{
    visibility: visible;
  }
  .invisible{
    visibility: hidden;
  }
  .collapse{
    visibility: collapse;
  }
  .static{
    position: static;
  }
  .fixed{
    position: fixed;
  }
  .absolute{
    position: absolute;
  }
  .relative{
    position: relative;
  }
  .inset-0{
    inset: 0px;
  }
  .bottom-\[10px\]{
    bottom: 10px;
  }
  .bottom-full{
    bottom: 100%;
  }
  .left-0{
    left: 0px;
  }
  .left-\[-350px\]{
    left: -350px;
  }
  .right-\[10px\]{
    right: 10px;
  }
  .top-0{
    top: 0px;
  }
  .top-\[50px\]{
    top: 50px;
  }
  .top-auto{
    top: auto;
  }
  .z-10{
    z-index: 10;
  }
  .z-\[1000\]{
    z-index: 1000;
  }
  .z-\[9999\]{
    z-index: 9999;
  }
  .m-0{
    margin: 0px;
  }
  .m-1{
    margin: 0.25rem;
  }
  .m-12{
    margin: 3rem;
  }
  .m-2{
    margin: 0.5rem;
  }
  .mx-1{
    margin-left: 0.25rem;
    margin-right: 0.25rem;
  }
  .mx-2{
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
  .mx-6{
    margin-left: 1.5rem;
    margin-right: 1.5rem;
  }
  .my-0{
    margin-top: 0px;
    margin-bottom: 0px;
  }
  .my-1{
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
  }
  .my-2{
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }
  .my-2\.5{
    margin-top: 0.625rem;
    margin-bottom: 0.625rem;
  }
  .my-4{
    margin-top: 1rem;
    margin-bottom: 1rem;
  }
  .mb-2{
    margin-bottom: 0.5rem;
  }
  .mb-3{
    margin-bottom: 0.75rem;
  }
  .mb-4{
    margin-bottom: 1rem;
  }
  .mb-6{
    margin-bottom: 1.5rem;
  }
  .mb-7{
    margin-bottom: 1.75rem;
  }
  .ml-2{
    margin-left: 0.5rem;
  }
  .ml-\[-13px\]{
    margin-left: -13px;
  }
  .ml-auto{
    margin-left: auto;
  }
  .mr-1{
    margin-right: 0.25rem;
  }
  .mr-2{
    margin-right: 0.5rem;
  }
  .mt-0{
    margin-top: 0px;
  }
  .mt-1{
    margin-top: 0.25rem;
  }
  .mt-2{
    margin-top: 0.5rem;
  }
  .mt-24{
    margin-top: 6rem;
  }
  .mt-4{
    margin-top: 1rem;
  }
  .mt-5{
    margin-top: 1.25rem;
  }
  .block{
    display: block;
  }
  .inline-block{
    display: inline-block;
  }
  .inline{
    display: inline;
  }
  .flex{
    display: flex;
  }
  .inline-flex{
    display: inline-flex;
  }
  .table{
    display: table;
  }
  .grid{
    display: grid;
  }
  .contents{
    display: contents;
  }
  .hidden{
    display: none;
  }
  .h-0{
    height: 0px;
  }
  .h-1{
    height: 0.25rem;
  }
  .h-16{
    height: 4rem;
  }
  .h-6{
    height: 1.5rem;
  }
  .h-64{
    height: 16rem;
  }
  .h-96{
    height: 26rem;
  }
  .h-\[22px\]{
    height: 22px;
  }
  .h-\[30rem\]{
    height: 30rem;
  }
  .h-\[var\(--comfy-topbar-height\)\]{
    height: var(--comfy-topbar-height);
  }
  .h-full{
    height: 100%;
  }
  .h-screen{
    height: 100vh;
  }
  .max-h-96{
    max-height: 26rem;
  }
  .max-h-full{
    max-height: 100%;
  }
  .min-h-8{
    min-height: 2rem;
  }
  .min-h-screen{
    min-height: 100vh;
  }
  .w-1\/2{
    width: 50%;
  }
  .w-12{
    width: 3rem;
  }
  .w-14{
    width: 3.5rem;
  }
  .w-16{
    width: 4rem;
  }
  .w-28{
    width: 7rem;
  }
  .w-3\/12{
    width: 25%;
  }
  .w-44{
    width: 11rem;
  }
  .w-48{
    width: 12rem;
  }
  .w-6{
    width: 1.5rem;
  }
  .w-64{
    width: 16rem;
  }
  .w-8{
    width: 2rem;
  }
  .w-\[22px\]{
    width: 22px;
  }
  .w-\[600px\]{
    width: 600px;
  }
  .w-auto{
    width: auto;
  }
  .w-fit{
    width: -moz-fit-content;
    width: fit-content;
  }
  .w-full{
    width: 100%;
  }
  .w-screen{
    width: 100vw;
  }
  .min-w-0{
    min-width: 0px;
  }
  .min-w-110{
    min-width: 32rem;
  }
  .min-w-84{
    min-width: 22rem;
  }
  .min-w-96{
    min-width: 26rem;
  }
  .max-w-110{
    max-width: 32rem;
  }
  .max-w-64{
    max-width: 16rem;
  }
  .max-w-\[150px\]{
    max-width: 150px;
  }
  .max-w-\[600px\]{
    max-width: 600px;
  }
  .max-w-full{
    max-width: 100%;
  }
  .max-w-screen-sm{
    max-width: 640px;
  }
  .flex-1{
    flex: 1 1 0%;
  }
  .flex-shrink-0{
    flex-shrink: 0;
  }
  .shrink-0{
    flex-shrink: 0;
  }
  .flex-grow{
    flex-grow: 1;
  }
  .grow{
    flex-grow: 1;
  }
  .-translate-y-40{
    --tw-translate-y: -10rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .scale-75{
    --tw-scale-x: .75;
    --tw-scale-y: .75;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .transform{
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .cursor-move{
    cursor: move;
  }
  .cursor-pointer{
    cursor: pointer;
  }
  .select-none{
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none;
  }
  .resize{
    resize: both;
  }
  .list-inside{
    list-style-position: inside;
  }
  .list-disc{
    list-style-type: disc;
  }
  .grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .flex-row{
    flex-direction: row;
  }
  .flex-row-reverse{
    flex-direction: row-reverse;
  }
  .flex-col{
    flex-direction: column;
  }
  .flex-wrap{
    flex-wrap: wrap;
  }
  .flex-nowrap{
    flex-wrap: nowrap;
  }
  .content-center{
    align-content: center;
  }
  .items-center{
    align-items: center;
  }
  .justify-end{
    justify-content: flex-end;
  }
  .justify-center{
    justify-content: center;
  }
  .justify-between{
    justify-content: space-between;
  }
  .justify-around{
    justify-content: space-around;
  }
  .gap-0{
    gap: 0px;
  }
  .gap-2{
    gap: 0.5rem;
  }
  .gap-3{
    gap: 0.75rem;
  }
  .gap-4{
    gap: 1rem;
  }
  .gap-6{
    gap: 1.5rem;
  }
  .gap-8{
    gap: 2rem;
  }
  .space-y-1 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
  }
  .space-y-2 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
  }
  .space-y-4 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }
  .place-self-end{
    place-self: end;
  }
  .justify-self-end{
    justify-self: end;
  }
  .overflow-auto{
    overflow: auto;
  }
  .overflow-hidden{
    overflow: hidden;
  }
  .overflow-y-auto{
    overflow-y: auto;
  }
  .overflow-x-hidden{
    overflow-x: hidden;
  }
  .truncate{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .text-ellipsis{
    text-overflow: ellipsis;
  }
  .whitespace-nowrap{
    white-space: nowrap;
  }
  .whitespace-pre-line{
    white-space: pre-line;
  }
  .whitespace-pre-wrap{
    white-space: pre-wrap;
  }
  .text-wrap{
    text-wrap: wrap;
  }
  .text-nowrap{
    text-wrap: nowrap;
  }
  .rounded{
    border-radius: 0.25rem;
  }
  .rounded-lg{
    border-radius: 0.5rem;
  }
  .rounded-none{
    border-radius: 0px;
  }
  .rounded-t-lg{
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
  }
  .border{
    border-width: 1px;
  }
  .border-0{
    border-width: 0px;
  }
  .border-x-0{
    border-left-width: 0px;
    border-right-width: 0px;
  }
  .border-b{
    border-bottom-width: 1px;
  }
  .border-l{
    border-left-width: 1px;
  }
  .border-r{
    border-right-width: 1px;
  }
  .border-t-0{
    border-top-width: 0px;
  }
  .border-solid{
    border-style: solid;
  }
  .border-none{
    border-style: none;
  }
  .bg-\[var\(--comfy-menu-bg\)\]{
    background-color: var(--comfy-menu-bg);
  }
  .bg-\[var\(--p-tree-background\)\]{
    background-color: var(--p-tree-background);
  }
  .bg-black{
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity));
  }
  .bg-blue-500{
    --tw-bg-opacity: 1;
    background-color: rgb(66 153 225 / var(--tw-bg-opacity));
  }
  .bg-gray-100{
    --tw-bg-opacity: 1;
    background-color: rgb(243 246 250 / var(--tw-bg-opacity));
  }
  .bg-gray-800{
    --tw-bg-opacity: 1;
    background-color: rgb(45 55 72 / var(--tw-bg-opacity));
  }
  .bg-green-500{
    --tw-bg-opacity: 1;
    background-color: rgb(150 206 76 / var(--tw-bg-opacity));
  }
  .bg-neutral-300{
    --tw-bg-opacity: 1;
    background-color: rgb(212 212 212 / var(--tw-bg-opacity));
  }
  .bg-neutral-700{
    --tw-bg-opacity: 1;
    background-color: rgb(64 64 64 / var(--tw-bg-opacity));
  }
  .bg-neutral-800{
    --tw-bg-opacity: 1;
    background-color: rgb(38 38 38 / var(--tw-bg-opacity));
  }
  .bg-neutral-900{
    --tw-bg-opacity: 1;
    background-color: rgb(23 23 23 / var(--tw-bg-opacity));
  }
  .bg-red-500{
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68 / var(--tw-bg-opacity));
  }
  .bg-red-700{
    --tw-bg-opacity: 1;
    background-color: rgb(185 28 28 / var(--tw-bg-opacity));
  }
  .bg-transparent{
    background-color: transparent;
  }
  .bg-opacity-50{
    --tw-bg-opacity: 0.5;
  }
  .bg-\[url\(\'\/assets\/images\/Git-Logo-White\.svg\'\)\]{
    background-image: url('../assets/images/Git-Logo-White.svg');
  }
  .bg-right-top{
    background-position: right top;
  }
  .bg-no-repeat{
    background-repeat: no-repeat;
  }
  .bg-origin-padding{
    background-origin: padding-box;
  }
  .object-contain{
    -o-object-fit: contain;
       object-fit: contain;
  }
  .object-cover{
    -o-object-fit: cover;
       object-fit: cover;
  }
  .p-0{
    padding: 0px;
  }
  .p-1{
    padding: 0.25rem;
  }
  .p-2{
    padding: 0.5rem;
  }
  .p-3{
    padding: 0.75rem;
  }
  .p-4{
    padding: 1rem;
  }
  .p-5{
    padding: 1.25rem;
  }
  .p-6{
    padding: 1.5rem;
  }
  .p-8{
    padding: 2rem;
  }
  .px-0{
    padding-left: 0px;
    padding-right: 0px;
  }
  .px-10{
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
  .px-2{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .px-4{
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .py-0{
    padding-top: 0px;
    padding-bottom: 0px;
  }
  .py-1{
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }
  .pb-0{
    padding-bottom: 0px;
  }
  .pl-4{
    padding-left: 1rem;
  }
  .pl-6{
    padding-left: 1.5rem;
  }
  .pr-0{
    padding-right: 0px;
  }
  .pr-2{
    padding-right: 0.5rem;
  }
  .pt-2{
    padding-top: 0.5rem;
  }
  .pt-4{
    padding-top: 1rem;
  }
  .pt-6{
    padding-top: 1.5rem;
  }
  .pt-8{
    padding-top: 2rem;
  }
  .text-center{
    text-align: center;
  }
  .font-mono{
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  }
  .font-sans{
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }
  .text-2xl{
    font-size: 1.5rem;
  }
  .text-3xl{
    font-size: 1.875rem;
  }
  .text-4xl{
    font-size: 2.25rem;
  }
  .text-lg{
    font-size: 1.125rem;
  }
  .text-sm{
    font-size: 0.875rem;
  }
  .text-xl{
    font-size: 1.25rem;
  }
  .text-xs{
    font-size: 0.75rem;
  }
  .font-bold{
    font-weight: 700;
  }
  .font-light{
    font-weight: 300;
  }
  .font-medium{
    font-weight: 500;
  }
  .font-normal{
    font-weight: 400;
  }
  .font-semibold{
    font-weight: 600;
  }
  .uppercase{
    text-transform: uppercase;
  }
  .italic{
    font-style: italic;
  }
  .text-blue-400{
    --tw-text-opacity: 1;
    color: rgb(99 179 237 / var(--tw-text-opacity));
  }
  .text-gray-400{
    --tw-text-opacity: 1;
    color: rgb(203 213 224 / var(--tw-text-opacity));
  }
  .text-green-500{
    --tw-text-opacity: 1;
    color: rgb(150 206 76 / var(--tw-text-opacity));
  }
  .text-highlight{
    color: var(--p-primary-color);
  }
  .text-muted{
    color: var(--p-text-muted-color);
  }
  .text-neutral-100{
    --tw-text-opacity: 1;
    color: rgb(245 245 245 / var(--tw-text-opacity));
  }
  .text-neutral-200{
    --tw-text-opacity: 1;
    color: rgb(229 229 229 / var(--tw-text-opacity));
  }
  .text-neutral-300{
    --tw-text-opacity: 1;
    color: rgb(212 212 212 / var(--tw-text-opacity));
  }
  .text-neutral-400{
    --tw-text-opacity: 1;
    color: rgb(163 163 163 / var(--tw-text-opacity));
  }
  .text-neutral-800{
    --tw-text-opacity: 1;
    color: rgb(38 38 38 / var(--tw-text-opacity));
  }
  .text-neutral-900{
    --tw-text-opacity: 1;
    color: rgb(23 23 23 / var(--tw-text-opacity));
  }
  .text-red-500{
    --tw-text-opacity: 1;
    color: rgb(239 68 68 / var(--tw-text-opacity));
  }
  .underline{
    text-decoration-line: underline;
  }
  .no-underline{
    text-decoration-line: none;
  }
  .opacity-0{
    opacity: 0;
  }
  .opacity-100{
    opacity: 1;
  }
  .opacity-40{
    opacity: 0.4;
  }
  .opacity-50{
    opacity: 0.5;
  }
  .shadow-lg{
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .outline{
    outline-style: solid;
  }
  .blur{
    --tw-blur: blur(8px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }
  .drop-shadow{
    --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }
  .invert{
    --tw-invert: invert(100%);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }
  .filter{
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }
  .backdrop-filter{
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
            backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  }
  .transition{
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
  .transition-all{
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
  .transition-opacity{
    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
  .duration-100{
    transition-duration: 100ms;
  }
  .duration-300{
    transition-duration: 300ms;
  }
  .ease-in{
    transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  }
  .ease-in-out{
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
  .ease-out{
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
  .content-\[\'\'\]{
    --tw-content: '';
    content: var(--tw-content);
  }
}

:root {
  --fg-color: #000;
  --bg-color: #fff;
  --comfy-menu-bg: #353535;
  --comfy-menu-secondary-bg: #292929;
  --comfy-topbar-height: 2.5rem;
  --comfy-input-bg: #222;
  --input-text: #ddd;
  --descrip-text: #999;
  --drag-text: #ccc;
  --error-text: #ff4444;
  --border-color: #4e4e4e;
  --tr-even-bg-color: #222;
  --tr-odd-bg-color: #353535;
  --primary-bg: #236692;
  --primary-fg: #ffffff;
  --primary-hover-bg: #3485bb;
  --primary-hover-fg: #ffffff;
  --content-bg: #e0e0e0;
  --content-fg: #000;
  --content-hover-bg: #adadad;
  --content-hover-fg: #000;
}

@media (prefers-color-scheme: dark) {
  :root {
    --fg-color: #fff;
    --bg-color: #202020;
    --content-bg: #4e4e4e;
    --content-fg: #fff;
    --content-hover-bg: #222;
    --content-hover-fg: #fff;
  }
}

body {
  width: 100vw;
  height: 100vh;
  margin: 0;
  overflow: hidden;
  grid-template-columns: auto 1fr auto;
  grid-template-rows: auto 1fr auto;
  background: var(--bg-color) var(--bg-img);
  color: var(--fg-color);
  min-height: -webkit-fill-available;
  max-height: -webkit-fill-available;
  min-width: -webkit-fill-available;
  max-width: -webkit-fill-available;
  font-family: Arial, sans-serif;
}

/**
  +------------------+------------------+------------------+
  |                                                        |
  |  .comfyui-body-                                        |
  |       top                                              |
  | (spans all cols)                                       |
  |                                                        |
  +------------------+------------------+------------------+
  |                  |                  |                  |
  | .comfyui-body-   |   #graph-canvas  | .comfyui-body-   |
  |      left        |                  |      right       |
  |                  |                  |                  |
  |                  |                  |                  |
  +------------------+------------------+------------------+
  |                                                        |
  |  .comfyui-body-                                        |
  |      bottom                                            |
  | (spans all cols)                                       |
  |                                                        |
  +------------------+------------------+------------------+
*/

.comfyui-body-top {
  order: -5;
  /* Span across all columns */
  grid-column: 1/-1;
  /* Position at the first row */
  grid-row: 1;
  /* Top menu bar dropdown needs to be above of graph canvas splitter overlay which is z-index: 999 */
  /* Top menu bar z-index needs to be higher than bottom menu bar z-index as by default
  pysssss's image feed is located at body-bottom, and it can overlap with the queue button, which
  is located in body-top. */
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.comfyui-body-left {
  order: -4;
  /* Position in the first column */
  grid-column: 1;
  /* Position below the top element */
  grid-row: 2;
  z-index: 10;
  display: flex;
}

.graph-canvas-container {
  width: 100%;
  height: 100%;
  order: -3;
  grid-column: 2;
  grid-row: 2;
  position: relative;
  overflow: hidden;
}

#graph-canvas {
  width: 100%;
  height: 100%;
  touch-action: none;
}

.comfyui-body-right {
  order: -2;
  z-index: 10;
  grid-column: 3;
  grid-row: 2;
}

.comfyui-body-bottom {
  order: 4;
  /* Span across all columns */
  grid-column: 1/-1;
  grid-row: 3;
  /* Bottom menu bar dropdown needs to be above of graph canvas splitter overlay which is z-index: 999 */
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.comfy-multiline-input {
  background-color: var(--comfy-input-bg);
  color: var(--input-text);
  overflow: hidden;
  overflow-y: auto;
  padding: 2px;
  resize: none;
  border: none;
  box-sizing: border-box;
  font-size: var(--comfy-textarea-font-size);
}

.comfy-markdown {
  /* We assign the textarea and the Tiptap editor to the same CSS grid area to stack them on top of one another. */
  display: grid;
}

.comfy-markdown > textarea {
  grid-area: 1 / 1 / 2 / 2;
}

.comfy-markdown .tiptap {
  grid-area: 1 / 1 / 2 / 2;
  background-color: var(--comfy-input-bg);
  color: var(--input-text);
  overflow: hidden;
  overflow-y: auto;
  resize: none;
  border: none;
  box-sizing: border-box;
  font-size: var(--comfy-textarea-font-size);
  height: 100%;
  padding: 0.5em;
}

.comfy-markdown.editing .tiptap {
  display: none;
}

.comfy-markdown .tiptap :first-child {
  margin-top: 0;
}

.comfy-markdown .tiptap :last-child {
  margin-bottom: 0;
}

.comfy-markdown .tiptap blockquote {
  border-left: medium solid;
  margin-left: 1em;
  padding-left: 0.5em;
}

.comfy-markdown .tiptap pre {
  border: thin dotted;
  border-radius: 0.5em;
  margin: 0.5em;
  padding: 0.5em;
}

.comfy-markdown .tiptap table {
  border-collapse: collapse;
}

.comfy-markdown .tiptap th {
  text-align: left;
  background: var(--comfy-menu-bg);
}

.comfy-markdown .tiptap th,
.comfy-markdown .tiptap td {
  padding: 0.5em;
  border: thin solid;
}

.comfy-modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 100; /* Sit on top */
  padding: 30px 30px 10px 30px;
  background-color: var(--comfy-menu-bg); /* Modal background */
  color: var(--error-text);
  box-shadow: 0 0 20px #888888;
  border-radius: 10px;
  top: 50%;
  left: 50%;
  max-width: 80vw;
  max-height: 80vh;
  transform: translate(-50%, -50%);
  overflow: hidden;
  justify-content: center;
  font-family: monospace;
  font-size: 15px;
}

.comfy-modal-content {
  display: flex;
  flex-direction: column;
}

.comfy-modal p {
  overflow: auto;
  white-space: pre-line; /* This will respect line breaks */
  margin-bottom: 20px; /* Add some margin between the text and the close button*/
}

.comfy-modal select,
.comfy-modal input[type='button'],
.comfy-modal input[type='checkbox'] {
  margin: 3px 3px 3px 4px;
}

.comfy-menu {
  font-size: 15px;
  position: absolute;
  top: 50%;
  right: 0;
  text-align: center;
  z-index: 999;
  width: 190px;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--descrip-text);
  background-color: var(--comfy-menu-bg);
  font-family: sans-serif;
  padding: 10px;
  border-radius: 0 8px 8px 8px;
  box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.4);
}

.comfy-menu-header {
  display: flex;
}

.comfy-menu-actions {
  display: flex;
  gap: 3px;
  align-items: center;
  height: 20px;
  position: relative;
  top: -1px;
  font-size: 22px;
}

.comfy-menu .comfy-menu-actions button {
  background-color: rgba(0, 0, 0, 0);
  padding: 0;
  border: none;
  cursor: pointer;
  font-size: inherit;
}

.comfy-menu .comfy-menu-actions .comfy-settings-btn {
  font-size: 0.6em;
}

button.comfy-close-menu-btn {
  font-size: 1em;
  line-height: 12px;
  color: #ccc;
  position: relative;
  top: -1px;
}

.comfy-menu-queue-size {
  flex: auto;
}

.comfy-menu button,
.comfy-modal button {
  font-size: 20px;
}

.comfy-menu-btns {
  margin-bottom: 10px;
  width: 100%;
}

.comfy-menu-btns button {
  font-size: 10px;
  width: 50%;
  color: var(--descrip-text) !important;
}

.comfy-menu > button {
  width: 100%;
}

.comfy-btn,
.comfy-menu > button,
.comfy-menu-btns button,
.comfy-menu .comfy-list button,
.comfy-modal button {
  color: var(--input-text);
  background-color: var(--comfy-input-bg);
  border-radius: 8px;
  border-color: var(--border-color);
  border-style: solid;
  margin-top: 2px;
}

.comfy-btn:hover:not(:disabled),
.comfy-menu > button:hover,
.comfy-menu-btns button:hover,
.comfy-menu .comfy-list button:hover,
.comfy-modal button:hover,
.comfy-menu-actions button:hover {
  filter: brightness(1.2);
  will-change: transform;
  cursor: pointer;
}

span.drag-handle {
  width: 10px;
  height: 20px;
  display: inline-block;
  overflow: hidden;
  line-height: 5px;
  padding: 3px 4px;
  cursor: move;
  vertical-align: middle;
  margin-top: -0.4em;
  margin-left: -0.2em;
  font-size: 12px;
  font-family: sans-serif;
  letter-spacing: 2px;
  color: var(--drag-text);
  text-shadow: 1px 0 1px black;
  touch-action: none;
}

span.drag-handle::after {
  content: '.. .. ..';
}

.comfy-queue-btn {
  width: 100%;
}

.comfy-list {
  color: var(--descrip-text);
  background-color: var(--comfy-menu-bg);
  margin-bottom: 10px;
  border-color: var(--border-color);
  border-style: solid;
}

.comfy-list-items {
  overflow-y: scroll;
  max-height: 100px;
  min-height: 25px;
  background-color: var(--comfy-input-bg);
  padding: 5px;
}

.comfy-list h4 {
  min-width: 160px;
  margin: 0;
  padding: 3px;
  font-weight: normal;
}

.comfy-list-items button {
  font-size: 10px;
}

.comfy-list-actions {
  margin: 5px;
  display: flex;
  gap: 5px;
  justify-content: center;
}

.comfy-list-actions button {
  font-size: 12px;
}

button.comfy-queue-btn {
  margin: 6px 0 !important;
}

.comfy-modal.comfy-settings,
.comfy-modal.comfy-manage-templates {
  text-align: center;
  font-family: sans-serif;
  color: var(--descrip-text);
  z-index: 99;
}

.comfy-modal.comfy-settings input[type='range'] {
  vertical-align: middle;
}

.comfy-modal.comfy-settings input[type='range'] + input[type='number'] {
  width: 3.5em;
}

.comfy-modal input,
.comfy-modal select {
  color: var(--input-text);
  background-color: var(--comfy-input-bg);
  border-radius: 8px;
  border-color: var(--border-color);
  border-style: solid;
  font-size: inherit;
}

.comfy-tooltip-indicator {
  text-decoration: underline;
  text-decoration-style: dashed;
}

@media only screen and (max-height: 850px) {
  .comfy-menu {
    top: 0 !important;
    bottom: 0 !important;
    left: auto !important;
    right: 0 !important;
    border-radius: 0;
  }

  .comfy-menu span.drag-handle {
    display: none;
  }

  .comfy-menu-queue-size {
    flex: unset;
  }

  .comfy-menu-header {
    justify-content: space-between;
  }
  .comfy-menu-actions {
    gap: 10px;
    font-size: 28px;
  }
}

/* Input popup */

.graphdialog {
  min-height: 1em;
  background-color: var(--comfy-menu-bg);
}

.graphdialog .name {
  font-size: 14px;
  font-family: sans-serif;
  color: var(--descrip-text);
}

.graphdialog button {
  margin-top: unset;
  vertical-align: unset;
  height: 1.6em;
  padding-right: 8px;
}

.graphdialog input,
.graphdialog textarea,
.graphdialog select {
  background-color: var(--comfy-input-bg);
  border: 2px solid;
  border-color: var(--border-color);
  color: var(--input-text);
  border-radius: 12px 0 0 12px;
}

/* Dialogs */

dialog {
  box-shadow: 0 0 20px #888888;
}

dialog::backdrop {
  background: rgba(0, 0, 0, 0.5);
}

.comfy-dialog.comfyui-dialog.comfy-modal {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transform: none;
}

.comfy-dialog.comfy-modal {
  font-family: Arial, sans-serif;
  border-color: var(--bg-color);
  box-shadow: none;
  border: 2px solid var(--border-color);
}

.comfy-dialog .comfy-modal-content {
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
  color: var(--fg-color);
}

.comfy-dialog .comfy-modal-content h3 {
  margin-top: 0;
}

.comfy-dialog .comfy-modal-content > p {
  width: 100%;
}

.comfy-dialog .comfy-modal-content > .comfyui-button {
  flex: 1;
  justify-content: center;
}

#comfy-settings-dialog {
  padding: 0;
  width: 41rem;
}

#comfy-settings-dialog tr > td:first-child {
  text-align: right;
}

#comfy-settings-dialog tbody button,
#comfy-settings-dialog table > button {
  background-color: var(--bg-color);
  border: 1px var(--border-color) solid;
  border-radius: 0;
  color: var(--input-text);
  font-size: 1rem;
  padding: 0.5rem;
}

#comfy-settings-dialog button:hover {
  background-color: var(--tr-odd-bg-color);
}

/* General CSS for tables */

.comfy-table {
  border-collapse: collapse;
  color: var(--input-text);
  font-family: Arial, sans-serif;
  width: 100%;
}

.comfy-table caption {
  position: sticky;
  top: 0;
  background-color: var(--bg-color);
  color: var(--input-text);
  font-size: 1rem;
  font-weight: bold;
  padding: 8px;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
}

.comfy-table caption .comfy-btn {
  position: absolute;
  top: -2px;
  right: 0;
  bottom: 0;
  cursor: pointer;
  border: none;
  height: 100%;
  border-radius: 0;
  aspect-ratio: 1/1;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  font-size: 20px;
}

.comfy-table caption .comfy-btn:focus {
  outline: none;
}

.comfy-table tr:nth-child(even) {
  background-color: var(--tr-even-bg-color);
}

.comfy-table tr:nth-child(odd) {
  background-color: var(--tr-odd-bg-color);
}

.comfy-table td,
.comfy-table th {
  border: 1px solid var(--border-color);
  padding: 8px;
}

/* Context menu */

.litegraph .dialog {
  z-index: 1;
  font-family: Arial, sans-serif;
}

.litegraph .litemenu-entry.has_submenu {
  position: relative;
  padding-right: 20px;
}

.litemenu-entry.has_submenu::after {
  content: '>';
  position: absolute;
  top: 0;
  right: 2px;
}

.litegraph.litecontextmenu,
.litegraph.litecontextmenu.dark {
  z-index: 9999 !important;
  background-color: var(--comfy-menu-bg) !important;
}

.litegraph.litecontextmenu
  .litemenu-entry:hover:not(.disabled):not(.separator) {
  background-color: var(--comfy-menu-hover-bg, var(--border-color)) !important;
  color: var(--fg-color);
}

.litegraph.litecontextmenu .litemenu-entry.submenu,
.litegraph.litecontextmenu.dark .litemenu-entry.submenu {
  background-color: var(--comfy-menu-bg) !important;
  color: var(--input-text);
}

.litegraph.litecontextmenu input {
  background-color: var(--comfy-input-bg) !important;
  color: var(--input-text) !important;
}

.comfy-context-menu-filter {
  box-sizing: border-box;
  border: 1px solid #999;
  margin: 0 0 5px 5px;
  width: calc(100% - 10px);
}

.comfy-img-preview {
  pointer-events: none;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  justify-content: center;
}

.comfy-img-preview img {
  -o-object-fit: contain;
     object-fit: contain;
  width: var(--comfy-img-preview-width);
  height: var(--comfy-img-preview-height);
}

.comfy-missing-nodes li button {
  font-size: 12px;
  margin-left: 5px;
}

/* Search box */

.litegraph.litesearchbox {
  z-index: 9999 !important;
  background-color: var(--comfy-menu-bg) !important;
  overflow: hidden;
  display: block;
}

.litegraph.litesearchbox input,
.litegraph.litesearchbox select {
  background-color: var(--comfy-input-bg) !important;
  color: var(--input-text);
}

.litegraph.lite-search-item {
  color: var(--input-text);
  background-color: var(--comfy-input-bg);
  filter: brightness(80%);
  will-change: transform;
  padding-left: 0.2em;
}

.litegraph.lite-search-item.generic_type {
  color: var(--input-text);
  filter: brightness(50%);
  will-change: transform;
}

@media only screen and (max-width: 450px) {
  #comfy-settings-dialog .comfy-table tbody {
    display: grid;
  }
  #comfy-settings-dialog .comfy-table tr {
    display: grid;
  }
  #comfy-settings-dialog tr > td:first-child {
    text-align: center;
    border-bottom: none;
    padding-bottom: 0;
  }
  #comfy-settings-dialog tr > td:not(:first-child) {
    text-align: center;
    border-top: none;
  }
}

audio.comfy-audio.empty-audio-widget {
  display: none;
}

#vue-app {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* Set auto complete panel's width as it is not accessible within vue-root */
.p-autocomplete-overlay {
  max-width: 25vw;
}

.p-tree-node-content {
  padding: var(--comfy-tree-explorer-item-padding) !important;
}

/* [Desktop] Electron window specific styles */
.app-drag {
  app-region: drag;
}

.no-drag {
  app-region: no-drag;
}

.window-actions-spacer {
  width: calc(100vw - env(titlebar-area-width, 100vw));
}
/* End of [Desktop] Electron window specific styles */
.hover\:bg-neutral-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(64 64 64 / var(--tw-bg-opacity));
}
.hover\:bg-opacity-75:hover{
  --tw-bg-opacity: 0.75;
}
.hover\:text-blue-300:hover{
  --tw-text-opacity: 1;
  color: rgb(144 205 244 / var(--tw-text-opacity));
}
.hover\:opacity-100:hover{
  opacity: 1;
}
@media (min-width: 768px){

  .md\:flex{
    display: flex;
  }

  .md\:hidden{
    display: none;
  }
}
@media (min-width: 1536px){

  .\32xl\:mx-4{
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .\32xl\:w-64{
    width: 16rem;
  }

  .\32xl\:max-w-full{
    max-width: 100%;
  }

  .\32xl\:p-16{
    padding: 4rem;
  }

  .\32xl\:p-4{
    padding: 1rem;
  }

  .\32xl\:p-\[var\(--p-dialog-content-padding\)\]{
    padding: var(--p-dialog-content-padding);
  }

  .\32xl\:p-\[var\(--p-dialog-header-padding\)\]{
    padding: var(--p-dialog-header-padding);
  }

  .\32xl\:px-4{
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .\32xl\:text-sm{
    font-size: 0.875rem;
  }
}
@media (prefers-color-scheme: dark){

  .dark\:bg-gray-800{
    --tw-bg-opacity: 1;
    background-color: rgb(45 55 72 / var(--tw-bg-opacity));
  }
}

.global-dialog .p-dialog-header {
    padding: 0.5rem
}
@media (min-width: 1536px) {
.global-dialog .p-dialog-header {
        padding: var(--p-dialog-header-padding)
}
}
.global-dialog .p-dialog-header {
    padding-bottom: 0px
}
.global-dialog .p-dialog-content {
    padding: 0.5rem
}
@media (min-width: 1536px) {
.global-dialog .p-dialog-content {
        padding: var(--p-dialog-content-padding)
}
}
.global-dialog .p-dialog-content {
    padding-top: 0px
}

.prompt-dialog-content[data-v-3df70997] {
  white-space: pre-wrap;
}

.no-results-placeholder[data-v-f2b77816] .p-card {
  background-color: var(--surface-ground);
  text-align: center;
  box-shadow: unset;
}
.no-results-placeholder h3[data-v-f2b77816] {
  color: var(--text-color);
  margin-bottom: 0.5rem;
}
.no-results-placeholder p[data-v-f2b77816] {
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.comfy-error-report[data-v-09b72a20] {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.action-container[data-v-09b72a20] {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}
.wrapper-pre[data-v-09b72a20] {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.comfy-missing-nodes[data-v-425cc3ac] {
  max-height: 300px;
  overflow-y: auto;
}
.node-hint[data-v-425cc3ac] {
  margin-left: 0.5rem;
  font-style: italic;
  color: var(--text-color-secondary);
}
[data-v-425cc3ac] .p-button {
  margin-left: auto;
}

.comfy-missing-models[data-v-ebf9fccc] {
  max-height: 300px;
  overflow-y: auto;
}

[data-v-53692f7e] .i-badge {

    --tw-bg-opacity: 1;

    background-color: rgb(150 206 76 / var(--tw-bg-opacity));

    --tw-text-opacity: 1;

    color: rgb(255 255 255 / var(--tw-text-opacity))
}
[data-v-53692f7e] .o-badge {

    --tw-bg-opacity: 1;

    background-color: rgb(239 68 68 / var(--tw-bg-opacity));

    --tw-text-opacity: 1;

    color: rgb(255 255 255 / var(--tw-text-opacity))
}
[data-v-53692f7e] .c-badge {

    --tw-bg-opacity: 1;

    background-color: rgb(66 153 225 / var(--tw-bg-opacity));

    --tw-text-opacity: 1;

    color: rgb(255 255 255 / var(--tw-text-opacity))
}
[data-v-53692f7e] .s-badge {

    --tw-bg-opacity: 1;

    background-color: rgb(234 179 8 / var(--tw-bg-opacity))
}

[data-v-ba13476b] .p-inputtext {
  --p-form-field-padding-x: 0.625rem;
}
.p-button.p-inputicon[data-v-ba13476b] {
  width: auto;
  border-style: none;
  padding: 0px;
}

.form-input[data-v-e4e3022d] .input-slider .p-inputnumber input,
.form-input[data-v-e4e3022d] .input-slider .slider-part {

    width: 5rem
}
.form-input[data-v-e4e3022d] .p-inputtext,
.form-input[data-v-e4e3022d] .p-select {

    width: 11rem
}

.settings-tab-panels {
  padding-top: 0px !important;
}

.settings-container[data-v-2e21278f] {
  display: flex;
  height: 70vh;
  width: 60vw;
  max-width: 1024px;
  overflow: hidden;
}
@media (max-width: 768px) {
.settings-container[data-v-2e21278f] {
    flex-direction: column;
    height: auto;
    width: 80vw;
}
.settings-sidebar[data-v-2e21278f] {
    width: 100%;
}
.settings-content[data-v-2e21278f] {
    height: 350px;
}
}

/* Show a separator line above the Keybinding tab */
/* This indicates the start of custom setting panels */
.settings-sidebar[data-v-2e21278f] .p-listbox-option[aria-label='Keybinding'] {
  position: relative;
}
.settings-sidebar[data-v-2e21278f] .p-listbox-option[aria-label='Keybinding']::before {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  --tw-content: '';
  content: var(--tw-content);
  border-top: 1px solid var(--p-divider-border-color);
}

.pi-cog[data-v-43089afc] {
  font-size: 1.25rem;
  margin-right: 0.5rem;
}
.version-tag[data-v-43089afc] {
  margin-left: 0.5rem;
}

.p-card[data-v-ffc83afa] {
  --p-card-body-padding: 10px 0 0 0;
  overflow: hidden;
}
[data-v-ffc83afa] .p-card-subtitle {
  text-align: center;
}

.carousel[data-v-d9962275] {
  width: 66vw;
}
/**
 * Copyright (c) 2014 The xterm.js authors. All rights reserved.
 * Copyright (c) 2012-2013, Christopher Jeffrey (MIT License)
 * https://github.com/chjj/term.js
 * @license MIT
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * Originally forked from (with the author's permission):
 *   Fabrice Bellard's javascript vt100 for jslinux:
 *   http://bellard.org/jslinux/
 *   Copyright (c) 2011 Fabrice Bellard
 *   The original design remains. The terminal itself
 *   has been extended to include xterm CSI codes, among
 *   other features.
 */

/**
 *  Default styles for xterm.js
 */

.xterm {
    cursor: text;
    position: relative;
    -moz-user-select: none;
         user-select: none;
    -ms-user-select: none;
    -webkit-user-select: none;
}

.xterm.focus,
.xterm:focus {
    outline: none;
}

.xterm .xterm-helpers {
    position: absolute;
    top: 0;
    /**
     * The z-index of the helpers must be higher than the canvases in order for
     * IMEs to appear on top.
     */
    z-index: 5;
}

.xterm .xterm-helper-textarea {
    padding: 0;
    border: 0;
    margin: 0;
    /* Move textarea out of the screen to the far left, so that the cursor is not visible */
    position: absolute;
    opacity: 0;
    left: -9999em;
    top: 0;
    width: 0;
    height: 0;
    z-index: -5;
    /** Prevent wrapping so the IME appears against the textarea at the correct position */
    white-space: nowrap;
    overflow: hidden;
    resize: none;
}

.xterm .composition-view {
    /* TODO: Composition position got messed up somewhere */
    background: #000;
    color: #FFF;
    display: none;
    position: absolute;
    white-space: nowrap;
    z-index: 1;
}

.xterm .composition-view.active {
    display: block;
}

.xterm .xterm-viewport {
    /* On OS X this is required in order for the scroll bar to appear fully opaque */
    background-color: #000;
    overflow-y: scroll;
    cursor: default;
    position: absolute;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
}

.xterm .xterm-screen {
    position: relative;
}

.xterm .xterm-screen canvas {
    position: absolute;
    left: 0;
    top: 0;
}

.xterm .xterm-scroll-area {
    visibility: hidden;
}

.xterm-char-measure-element {
    display: inline-block;
    visibility: hidden;
    position: absolute;
    top: 0;
    left: -9999em;
    line-height: normal;
}

.xterm.enable-mouse-events {
    /* When mouse events are enabled (eg. tmux), revert to the standard pointer cursor */
    cursor: default;
}

.xterm.xterm-cursor-pointer,
.xterm .xterm-cursor-pointer {
    cursor: pointer;
}

.xterm.column-select.focus {
    /* Column selection mode */
    cursor: crosshair;
}

.xterm .xterm-accessibility:not(.debug),
.xterm .xterm-message {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 10;
    color: transparent;
    pointer-events: none;
}

.xterm .xterm-accessibility-tree:not(.debug) *::-moz-selection {
  color: transparent;
}

.xterm .xterm-accessibility-tree:not(.debug) *::selection {
  color: transparent;
}

.xterm .xterm-accessibility-tree {
  -webkit-user-select: text;
     -moz-user-select: text;
          user-select: text;
  white-space: pre;
}

.xterm .live-region {
    position: absolute;
    left: -9999px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

.xterm-dim {
    /* Dim should not apply to background, so the opacity of the foreground color is applied
     * explicitly in the generated class and reset to 1 here */
    opacity: 1 !important;
}

.xterm-underline-1 { text-decoration: underline; }
.xterm-underline-2 { -webkit-text-decoration: double underline; text-decoration: double underline; }
.xterm-underline-3 { -webkit-text-decoration: wavy underline; text-decoration: wavy underline; }
.xterm-underline-4 { -webkit-text-decoration: dotted underline; text-decoration: dotted underline; }
.xterm-underline-5 { -webkit-text-decoration: dashed underline; text-decoration: dashed underline; }

.xterm-overline {
    text-decoration: overline;
}

.xterm-overline.xterm-underline-1 { text-decoration: overline underline; }
.xterm-overline.xterm-underline-2 { -webkit-text-decoration: overline double underline; text-decoration: overline double underline; }
.xterm-overline.xterm-underline-3 { -webkit-text-decoration: overline wavy underline; text-decoration: overline wavy underline; }
.xterm-overline.xterm-underline-4 { -webkit-text-decoration: overline dotted underline; text-decoration: overline dotted underline; }
.xterm-overline.xterm-underline-5 { -webkit-text-decoration: overline dashed underline; text-decoration: overline dashed underline; }

.xterm-strikethrough {
    text-decoration: line-through;
}

.xterm-screen .xterm-decoration-container .xterm-decoration {
	z-index: 6;
	position: absolute;
}

.xterm-screen .xterm-decoration-container .xterm-decoration.xterm-decoration-top-layer {
	z-index: 7;
}

.xterm-decoration-overview-ruler {
    z-index: 8;
    position: absolute;
    top: 0;
    right: 0;
    pointer-events: none;
}

.xterm-decoration-top {
    z-index: 2;
    position: relative;
}

[data-v-250ab9af] .p-terminal .xterm {
  overflow-x: auto;
}
[data-v-250ab9af] .p-terminal .xterm-screen {
  background-color: black;
  overflow-y: hidden;
}

[data-v-90a7f075] .p-terminal .xterm {
  overflow-x: auto;
}
[data-v-90a7f075] .p-terminal .xterm-screen {
  background-color: black;
  overflow-y: hidden;
}

[data-v-03daf1c8] .p-terminal .xterm {
  overflow-x: auto;
}
[data-v-03daf1c8] .p-terminal .xterm-screen {
  background-color: black;
  overflow-y: hidden;
}
.mdi.rotate270::before {
  transform: rotate(270deg);
}

/* Generic */
.comfyui-button {
  display: flex;
  align-items: center;
  gap: 0.5em;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  box-sizing: border-box;
  margin: 0;
  transition: box-shadow 0.1s;
}

.comfyui-button:active {
  box-shadow: inset 1px 1px 10px rgba(0, 0, 0, 0.5);
}

.comfyui-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.primary .comfyui-button,
.primary.comfyui-button {
  background-color: var(--primary-bg) !important;
  color: var(--primary-fg) !important;
}

.primary .comfyui-button:not(:disabled):hover,
.primary.comfyui-button:not(:disabled):hover {
  background-color: var(--primary-hover-bg) !important;
  color: var(--primary-hover-fg) !important;
}

/* Popup */
.comfyui-popup {
  position: absolute;
  left: var(--left);
  right: var(--right);
  top: var(--top);
  bottom: var(--bottom);
  z-index: 2000;
  max-height: calc(100vh - var(--limit) - 10px);
  box-shadow: 3px 3px 5px 0px rgba(0, 0, 0, 0.3);
}

.comfyui-popup:not(.open) {
  display: none;
}

.comfyui-popup.right.open {
  border-top-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  overflow: hidden;
}
/* Split button */
.comfyui-split-button {
  position: relative;
  display: flex;
}

.comfyui-split-primary {
  flex: auto;
}

.comfyui-split-primary .comfyui-button {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 1px solid var(--comfy-menu-bg);
  width: 100%;
}

.comfyui-split-arrow .comfyui-button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  padding-left: 2px;
  padding-right: 2px;
}

.comfyui-split-button-popup {
  white-space: nowrap;
  background-color: var(--content-bg);
  color: var(--content-fg);
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.comfyui-split-button-popup.hover {
  z-index: 2001;
}
.comfyui-split-button-popup > .comfyui-button {
  border: none;
  background-color: transparent;
  color: var(--fg-color);
  padding: 8px 12px 8px 8px;
}

.comfyui-split-button-popup > .comfyui-button:not(:disabled):hover {
  background-color: var(--comfy-input-bg);
}

/* Button group */
.comfyui-button-group {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
}

.comfyui-button-group:empty {
  display: none;
}
.comfyui-button-group > .comfyui-button,
.comfyui-button-group > .comfyui-button-wrapper > .comfyui-button {
  padding: 4px 10px;
  border-radius: 0;
}

/* Menu */
.comfyui-menu .mdi::before {
  font-size: 18px;
}

.comfyui-menu .comfyui-button {
  background: var(--comfy-input-bg);
  color: var(--fg-color);
  white-space: nowrap;
}

.comfyui-menu .comfyui-button:not(:disabled):hover {
  background: var(--border-color);
  color: var(--content-fg);
}

.comfyui-menu .comfyui-split-button-popup > .comfyui-button {
  border-radius: 0;
  background-color: transparent;
}

.comfyui-menu .comfyui-split-button-popup > .comfyui-button:not(:disabled):hover {
  background-color: var(--comfy-input-bg);
}

.comfyui-menu .comfyui-split-button-popup.left {
  border-top-right-radius: 4px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.comfyui-menu .comfyui-button.popup-open {
  background-color: var(--content-bg);
  color: var(--content-fg);
}

.comfyui-menu-push {
  margin-left: -0.8em;
  flex: auto;
}

/** Send to workflow widget selection dialog */
.comfy-widget-selection-dialog {
  border: none;
}

.comfy-widget-selection-dialog div {
  color: var(--fg-color);
  font-family: Arial, Helvetica, sans-serif;
}

.comfy-widget-selection-dialog h2 {
  margin-top: 0;
}

.comfy-widget-selection-dialog section {
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
  flex-direction: column;
}

.comfy-widget-selection-item {
  display: flex;
  gap: 10px;
  align-items: center;
}

.comfy-widget-selection-item span {
  margin-right: auto;
}

.comfy-widget-selection-item span::before {
  content: '#' attr(data-id);
  opacity: 0.5;
  margin-right: 5px;
}

.comfy-modal .comfy-widget-selection-item button {
  font-size: 1em;
}

/***** Responsive *****/
.lg.comfyui-menu .lt-lg-show {
  display: none !important;
}
.comfyui-menu:not(.lg) .nlg-hide {
  display: none !important;
}
/** Large screen */
.lg.comfyui-menu>.comfyui-menu-mobile-collapse .comfyui-button span,
.lg.comfyui-menu>.comfyui-menu-mobile-collapse.comfyui-button span {
  display: none;
}
.lg.comfyui-menu>.comfyui-menu-mobile-collapse .comfyui-popup .comfyui-button span {
  display: unset;
}

/** Non large screen */
.lt-lg.comfyui-menu {
  flex-wrap: wrap;
}

.lt-lg.comfyui-menu > *:not(.comfyui-menu-mobile-collapse) {
  order: 1;
}

.lt-lg.comfyui-menu > .comfyui-menu-mobile-collapse {
  order: 9999;
  width: 100%;
}

.comfyui-body-bottom .lt-lg.comfyui-menu > .comfyui-menu-mobile-collapse {
  order: -1;
}

.comfyui-body-bottom .lt-lg.comfyui-menu > .comfyui-menu-button {
  top: unset;
  bottom: 4px;
}

.lt-lg.comfyui-menu > .comfyui-menu-mobile-collapse.comfyui-button-group {
  flex-wrap: wrap;
}

.lt-lg.comfyui-menu > .comfyui-menu-mobile-collapse .comfyui-button,
.lt-lg.comfyui-menu > .comfyui-menu-mobile-collapse.comfyui-button {
  padding: 10px;
}
.lt-lg.comfyui-menu > .comfyui-menu-mobile-collapse .comfyui-button,
.lt-lg.comfyui-menu > .comfyui-menu-mobile-collapse .comfyui-button-wrapper {
  width: 100%;
}

.lt-lg.comfyui-menu > .comfyui-menu-mobile-collapse .comfyui-popup {
  position: static;
  background-color: var(--comfy-input-bg);
  max-width: unset;
  max-height: 50vh;
  overflow: auto;
}

.lt-lg.comfyui-menu:not(.expanded) > .comfyui-menu-mobile-collapse {
  display: none;
}

.lt-lg .comfyui-menu-button {
  position: absolute;
  top: 4px;
  right: 8px;
}

.lt-lg.comfyui-menu > .comfyui-menu-mobile-collapse .comfyui-view-list-popup {
  border-radius: 0;
}

.lt-lg.comfyui-menu .comfyui-workflows-popup {
  width: 100vw;
}

/** Small */
.lt-md .comfyui-workflows-button-inner {
  width: unset !important;
}
.lt-md  .comfyui-workflows-label {
  display: none;
}

/** Extra small */
.lt-sm .comfyui-interrupt-button {
  margin-right: 45px;
}
.comfyui-body-bottom .lt-sm.comfyui-menu > .comfyui-menu-button{
  bottom: 41px;
}


.editable-text[data-v-d670c40f] {
  display: inline;
}
.editable-text input[data-v-d670c40f] {
  width: 100%;
  box-sizing: border-box;
}

.tree-node[data-v-a6457774] {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.leaf-count-badge[data-v-a6457774] {
  margin-left: 0.5rem;
}
.node-content[data-v-a6457774] {
  display: flex;
  align-items: center;
  flex-grow: 1;
}
.leaf-label[data-v-a6457774] {
  margin-left: 0.5rem;
}
[data-v-a6457774] .editable-text span {
  word-break: break-all;
}

[data-v-31d518da] .tree-explorer-node-label {
  width: 100%;
  display: flex;
  align-items: center;
  margin-left: var(--p-tree-node-gap);
  flex-grow: 1;
}

/*
 * The following styles are necessary to avoid layout shift when dragging nodes over folders.
 * By setting the position to relative on the parent and using an absolutely positioned pseudo-element,
 * we can create a visual indicator for the drop target without affecting the layout of other elements.
 */
[data-v-31d518da] .p-tree-node-content:has(.tree-folder) {
  position: relative;
}
[data-v-31d518da] .p-tree-node-content:has(.tree-folder.can-drop)::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid var(--p-content-color);
  pointer-events: none;
}

[data-v-5e759e25] .p-toolbar-end .p-button {

    padding-top: 0.25rem;

    padding-bottom: 0.25rem
}
@media (min-width: 1536px) {
[data-v-5e759e25] .p-toolbar-end .p-button {

        padding-top: 0.5rem;

        padding-bottom: 0.5rem
}
}
[data-v-5e759e25] .p-toolbar-start {

    min-width: 0px;

    flex: 1 1 0%;

    overflow: hidden
}

.model_preview[data-v-32e6c4d9] {
  background-color: var(--comfy-menu-bg);
  font-family: 'Open Sans', sans-serif;
  color: var(--descrip-text);
  border: 1px solid var(--descrip-text);
  min-width: 300px;
  max-width: 500px;
  width: -moz-fit-content;
  width: fit-content;
  height: -moz-fit-content;
  height: fit-content;
  z-index: 9999;
  border-radius: 12px;
  overflow: hidden;
  font-size: 12px;
  padding: 10px;
}
.model_preview_image[data-v-32e6c4d9] {
  margin: auto;
  width: -moz-fit-content;
  width: fit-content;
}
.model_preview_image img[data-v-32e6c4d9] {
  max-width: 100%;
  max-height: 150px;
  -o-object-fit: contain;
     object-fit: contain;
}
.model_preview_title[data-v-32e6c4d9] {
  font-weight: bold;
  text-align: center;
  font-size: 14px;
}
.model_preview_top_container[data-v-32e6c4d9] {
  text-align: center;
  line-height: 0.5;
}
.model_preview_filename[data-v-32e6c4d9],
.model_preview_author[data-v-32e6c4d9],
.model_preview_architecture[data-v-32e6c4d9] {
  display: inline-block;
  text-align: center;
  margin: 5px;
  font-size: 10px;
}
.model_preview_prefix[data-v-32e6c4d9] {
  font-weight: bold;
}

.model-lib-model-icon-container[data-v-b45ea43e] {
  display: inline-block;
  position: relative;
  left: 0;
  height: 1.5rem;
  vertical-align: top;
  width: 0px;
}
.model-lib-model-icon[data-v-b45ea43e] {
  background-size: cover;
  background-position: center;
  display: inline-block;
  position: relative;
  left: -2.2rem;
  top: -0.1rem;
  height: 1.7rem;
  width: 1.7rem;
  vertical-align: top;
}

[data-v-0bb2ac55] .pi-fake-spacer {
  height: 1px;
  width: 16px;
}

._content[data-v-c4279e6b] {

    display: flex;

    flex-direction: column
}
._content[data-v-c4279e6b] > :not([hidden]) ~ :not([hidden]) {

    --tw-space-y-reverse: 0;

    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));

    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse))
}
._footer[data-v-c4279e6b] {

    display: flex;

    flex-direction: column;

    align-items: flex-end;

    padding-top: 1rem
}

.slot_row[data-v-d9792337] {
  padding: 2px;
}

/* Original N-Sidebar styles */
._sb_dot[data-v-d9792337] {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: grey;
}
.node_header[data-v-d9792337] {
  line-height: 1;
  padding: 8px 13px 7px;
  margin-bottom: 5px;
  font-size: 15px;
  text-wrap: nowrap;
  overflow: hidden;
  display: flex;
  align-items: center;
}
.headdot[data-v-d9792337] {
  width: 10px;
  height: 10px;
  float: inline-start;
  margin-right: 8px;
}
.IMAGE[data-v-d9792337] {
  background-color: #64b5f6;
}
.VAE[data-v-d9792337] {
  background-color: #ff6e6e;
}
.LATENT[data-v-d9792337] {
  background-color: #ff9cf9;
}
.MASK[data-v-d9792337] {
  background-color: #81c784;
}
.CONDITIONING[data-v-d9792337] {
  background-color: #ffa931;
}
.CLIP[data-v-d9792337] {
  background-color: #ffd500;
}
.MODEL[data-v-d9792337] {
  background-color: #b39ddb;
}
.CONTROL_NET[data-v-d9792337] {
  background-color: #a5d6a7;
}
._sb_node_preview[data-v-d9792337] {
  background-color: var(--comfy-menu-bg);
  font-family: 'Open Sans', sans-serif;
  font-size: small;
  color: var(--descrip-text);
  border: 1px solid var(--descrip-text);
  min-width: 300px;
  width: -moz-min-content;
  width: min-content;
  height: -moz-fit-content;
  height: fit-content;
  z-index: 9999;
  border-radius: 12px;
  overflow: hidden;
  font-size: 12px;
  padding-bottom: 10px;
}
._sb_node_preview ._sb_description[data-v-d9792337] {
  margin: 10px;
  padding: 6px;
  background: var(--border-color);
  border-radius: 5px;
  font-style: italic;
  font-weight: 500;
  font-size: 0.9rem;
  word-break: break-word;
}
._sb_table[data-v-d9792337] {
  display: grid;

  grid-column-gap: 10px;
  /* Spazio tra le colonne */
  width: 100%;
  /* Imposta la larghezza della tabella al 100% del contenitore */
}
._sb_row[data-v-d9792337] {
  display: grid;
  grid-template-columns: 10px 1fr 1fr 1fr 10px;
  grid-column-gap: 10px;
  align-items: center;
  padding-left: 9px;
  padding-right: 9px;
}
._sb_row_string[data-v-d9792337] {
  grid-template-columns: 10px 1fr 1fr 10fr 1fr;
}
._sb_col[data-v-d9792337] {
  border: 0px solid #000;
  display: flex;
  align-items: flex-end;
  flex-direction: row-reverse;
  flex-wrap: nowrap;
  align-content: flex-start;
  justify-content: flex-end;
}
._sb_inherit[data-v-d9792337] {
  display: inherit;
}
._long_field[data-v-d9792337] {
  background: var(--bg-color);
  border: 2px solid var(--border-color);
  margin: 5px 5px 0 5px;
  border-radius: 10px;
  line-height: 1.7;
  text-wrap: nowrap;
}
._sb_arrow[data-v-d9792337] {
  color: var(--fg-color);
}
._sb_preview_badge[data-v-d9792337] {
  text-align: center;
  background: var(--comfy-input-bg);
  font-weight: bold;
  color: var(--error-text);
}

.node-lib-node-container[data-v-da9a8962] {
    height: 100%;
    width: 100%
}

.p-selectbutton .p-button[data-v-05364174] {
  padding: 0.5rem;
}
.p-selectbutton .p-button .pi[data-v-05364174] {
  font-size: 1.5rem;
}
.field[data-v-05364174] {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.color-picker-container[data-v-05364174] {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.scroll-container[data-v-ad33a347] {
  height: 100%;
  overflow-y: auto;

  /* Firefox */
  scrollbar-width: none;
&[data-v-ad33a347]::-webkit-scrollbar {
    width: 1px;
}
&[data-v-ad33a347]::-webkit-scrollbar-thumb {
    background-color: transparent;
}
}

.comfy-image-wrap[data-v-a748ccd8] {
  display: contents;
}
.comfy-image-blur[data-v-a748ccd8] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.comfy-image-main[data-v-a748ccd8] {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  z-index: 1;
}
.contain .comfy-image-wrap[data-v-a748ccd8] {
  position: relative;
  width: 100%;
  height: 100%;
}
.contain .comfy-image-main[data-v-a748ccd8] {
  -o-object-fit: contain;
     object-fit: contain;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  position: absolute;
}
.broken-image-placeholder[data-v-a748ccd8] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  margin: 2rem;
}
.broken-image-placeholder i[data-v-a748ccd8] {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

/* PrimeVue's galleria teleports the fullscreen gallery out of subtree so we
cannot use scoped style here. */
img.galleria-image {
  max-width: 100vw;
  max-height: 100vh;
  -o-object-fit: contain;
     object-fit: contain;
}
.p-galleria-close-button {
  /* Set z-index so the close button doesn't get hidden behind the image when image is large */
  z-index: 1;
}

.result-container[data-v-2403edc6] {
  width: 100%;
  height: 100%;
  aspect-ratio: 1 / 1;
  overflow: hidden;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.preview-mask[data-v-2403edc6] {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}
.result-container:hover .preview-mask[data-v-2403edc6] {
  opacity: 1;
}

.task-result-preview[data-v-b676a511] {
  aspect-ratio: 1 / 1;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.task-result-preview i[data-v-b676a511],
.task-result-preview span[data-v-b676a511] {
  font-size: 2rem;
}
.task-item[data-v-b676a511] {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}
.task-item-details[data-v-b676a511] {
  position: absolute;
  bottom: 0;
  padding: 0.6rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  z-index: 1;
}
.task-node-link[data-v-b676a511] {
  padding: 2px;
}

/* In dark mode, transparent background color for tags is not ideal for tags that
are floating on top of images. */
.tag-wrapper[data-v-b676a511] {
  background-color: var(--p-primary-contrast-color);
  border-radius: 6px;
  display: inline-flex;
}
.node-name-tag[data-v-b676a511] {
  word-break: break-all;
}
.status-tag-group[data-v-b676a511] {
  display: flex;
  flex-direction: column;
}
.progress-preview-img[data-v-b676a511] {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}
